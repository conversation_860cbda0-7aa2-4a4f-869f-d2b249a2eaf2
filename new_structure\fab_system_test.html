<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>FAB System Test</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <style>
      body {
        margin: 0;
        padding: 20px;
        font-family: Arial, sans-serif;
        background-color: #f5f5f5;
        min-height: 100vh;
      }

      .test-container {
        max-width: 400px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .test-content {
        height: 150px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin: 20px 0;
      }

      .status-display {
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        margin: 20px 0;
        font-family: monospace;
      }

      .status-good {
        color: #28a745;
      }
      .status-bad {
        color: #dc3545;
      }
      .status-info {
        color: #17a2b8;
      }

      /* FAB System CSS */
      .floating-action-system {
        position: fixed;
        bottom: 25px;
        right: 25px;
        z-index: 1001;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 15px;
      }

      .fab-menu {
        display: none;
        flex-direction: column;
        gap: 10px;
        align-items: flex-end;
      }

      .fab-menu.active {
        display: flex;
      }

      .fab-item {
        display: flex;
        align-items: center;
        gap: 10px;
        background: rgba(255, 255, 255, 0.95);
        color: #333;
        text-decoration: none;
        padding: 12px 16px;
        border-radius: 50px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        white-space: nowrap;
        font-size: 14px;
        font-weight: 500;
      }

      .fab-item:hover {
        background: #ffffff;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      }

      .fab-item i {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
      }

      .fab-main {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
      }

      .fab-main:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      }

      .fab-main i {
        color: white;
        font-size: 20px;
        transition: transform 0.3s ease;
      }

      .fab-main.active i {
        transform: rotate(45deg);
      }

      @media (max-width: 768px) {
        .floating-action-system {
          bottom: 20px;
          right: 20px;
        }

        .fab-item {
          padding: 10px 14px;
          font-size: 13px;
        }

        .fab-main {
          width: 50px;
          height: 50px;
        }
      }
    </style>
  </head>
  <body>
    <div class="test-container">
      <h1>📱 FAB System Test</h1>
      <p>
        Click the <strong>+</strong> button in the bottom right corner to test
        the floating action button system.
      </p>

      <div class="test-content">Test Content Area</div>

      <div class="status-display" id="statusDisplay">
        <div class="status-info">FAB Status: Closed</div>
        <div class="status-info">Last Action: None</div>
        <div class="status-info">Click Count: 0</div>
      </div>

      <div class="test-content">More Test Content</div>

      <div class="test-content">Bottom Content</div>
    </div>

    <!-- FAB System HTML -->
    <div class="floating-action-system">
      <div class="fab-menu" id="fab-menu">
        <a
          href="#"
          onclick="handleFabAction('upload-marks'); return false;"
          class="fab-item"
        >
          <i class="fas fa-upload"></i>
          <span>Upload Marks</span>
        </a>
        <a
          href="#"
          onclick="handleFabAction('download-template'); return false;"
          class="fab-item"
        >
          <i class="fas fa-download"></i>
          <span>Download Template</span>
        </a>
        <a
          href="#"
          onclick="handleFabAction('collaborative-marks'); return false;"
          class="fab-item"
        >
          <i class="fas fa-users"></i>
          <span>Collaborative Marks</span>
        </a>
        <a
          href="#"
          onclick="handleFabAction('generate-reports'); return false;"
          class="fab-item"
        >
          <i class="fas fa-file-pdf"></i>
          <span>Generate Reports</span>
        </a>
        <a
          href="#"
          onclick="handleFabAction('analytics'); return false;"
          class="fab-item"
        >
          <i class="fas fa-chart-line"></i>
          <span>Analytics</span>
        </a>
      </div>
      <button class="fab-main" onclick="toggleFab()" id="fab-main-btn">
        <i class="fas fa-plus" id="fab-icon"></i>
      </button>
    </div>

    <script>
      let clickCount = 0;
      let isOpen = false;

      function toggleFab() {
        const fabMenu = document.getElementById("fab-menu");
        const fabIcon = document.getElementById("fab-icon");
        const fabMain = document.getElementById("fab-main-btn");

        clickCount++;

        if (fabMenu.classList.contains("active")) {
          fabMenu.classList.remove("active");
          fabMain.classList.remove("active");
          fabIcon.className = "fas fa-plus";
          isOpen = false;
          updateStatus("Closed", "FAB menu closed");
        } else {
          fabMenu.classList.add("active");
          fabMain.classList.add("active");
          fabIcon.className = "fas fa-times";
          isOpen = true;
          updateStatus("Open", "FAB menu opened");
        }
      }

      function handleFabAction(action) {
        updateStatus(isOpen ? "Open" : "Closed", `Action: ${action}`);

        // Close the FAB menu after action
        setTimeout(() => {
          toggleFab();
        }, 500);
      }

      function updateStatus(status, lastAction) {
        const statusDisplay = document.getElementById("statusDisplay");
        statusDisplay.innerHTML = `
                <div class="status-${
                  status === "Open" ? "good" : "info"
                }">FAB Status: ${status}</div>
                <div class="status-info">Last Action: ${lastAction}</div>
                <div class="status-info">Click Count: ${clickCount}</div>
            `;
      }

      // Initialize
      document.addEventListener("DOMContentLoaded", function () {
        console.log("FAB System Test initialized");
        updateStatus("Closed", "Page loaded");
      });
    </script>
  </body>
</html>
