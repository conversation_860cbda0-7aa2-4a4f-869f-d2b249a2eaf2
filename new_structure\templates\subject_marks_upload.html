{% extends "base.html" %}

{% block title %}Subject Marks Upload - Hillview School{% endblock %}

{% block content %}
<div class="modern-container">
    <!-- Header -->
    <header class="modern-header fade-in">
        <div class="header-content">
            <div>
                <h1 class="header-title">
                    <i class="fas fa-upload"></i>
                    Subject Marks Upload
                </h1>
                <p class="header-subtitle">
                    Upload marks for subjects you teach across different classes
                </p>
            </div>
            <div class="header-actions">
                <a href="{{ url_for('classteacher.dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </header>

    <!-- Assignment Selection -->
    <div class="dashboard-card">
        <div class="card-header">
            <h2>Select Subject Assignment</h2>
            <p>Choose the subject and class for which you want to upload marks</p>
        </div>

        <div class="assignments-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
            {% for assignment in assignments %}
            <div class="assignment-card" style="
                background: white;
                border: 2px solid {% if assignment.subject_id == subject_id and assignment.grade_id == grade_id and assignment.stream_id == stream_id %}#007bff{% else %}#e0e0e0{% endif %};
                border-radius: 12px;
                padding: 20px;
                cursor: pointer;
                transition: all 0.2s ease;
            " onclick="selectAssignment({{ assignment.subject_id }}, {{ assignment.grade_id }}, {{ assignment.stream_id or 'null' }})">
                
                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                    <div style="
                        width: 40px;
                        height: 40px;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        border-radius: 8px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 12px;
                    ">
                        <i class="fas fa-book" style="color: white; font-size: 18px;"></i>
                    </div>
                    <div>
                        <h4 style="margin: 0; color: #333; font-size: 16px;">{{ assignment.subject_name }}</h4>
                        <p style="margin: 2px 0 0 0; color: #666; font-size: 12px;">{{ assignment.education_level }}</p>
                    </div>
                </div>

                <div>
                    <p style="margin: 0; color: #555; font-size: 14px;">
                        <strong>Grade {{ assignment.grade_name }}</strong>
                        {% if assignment.stream_name and assignment.stream_name != 'No Stream' %}
                        - {{ assignment.stream_name }}
                        {% endif %}
                    </p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Marks Upload Form (shown when assignment is selected) -->
    {% if selected_assignment and students %}
    <div class="dashboard-card">
        <div class="card-header">
            <h2>Upload Marks</h2>
            <p>
                Subject: <strong>{{ selected_assignment.subject.name }}</strong> | 
                Class: <strong>Grade {{ selected_assignment.grade.name }}{% if selected_assignment.stream %} - {{ selected_assignment.stream.name }}{% endif %}</strong>
            </p>
        </div>

        <!-- Term and Assessment Selection -->
        <form method="GET" action="{{ url_for('classteacher.subject_marks_upload') }}" style="margin-bottom: 30px;">
            <input type="hidden" name="subject_id" value="{{ subject_id }}">
            <input type="hidden" name="grade_id" value="{{ grade_id }}">
            <input type="hidden" name="stream_id" value="{{ stream_id }}">
            
            <div style="display: grid; grid-template-columns: 1fr 1fr auto; gap: 15px; align-items: end;">
                <div class="form-group">
                    <label for="term_id">Term</label>
                    <select id="term_id" name="term_id" required>
                        <option value="">Select Term</option>
                        {% for term in terms %}
                        <option value="{{ term.id }}">{{ term.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group">
                    <label for="assessment_type_id">Assessment Type</label>
                    <select id="assessment_type_id" name="assessment_type_id" required>
                        <option value="">Select Assessment Type</option>
                        {% for assessment_type in assessment_types %}
                        <option value="{{ assessment_type.id }}">{{ assessment_type.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    Load Students
                </button>
            </div>
        </form>

        <!-- Students List -->
        <div class="students-section">
            <h3>Students ({{ students|length }})</h3>
            <div class="students-grid" style="display: grid; gap: 10px;">
                {% for student in students %}
                <div class="student-row" style="
                    display: grid;
                    grid-template-columns: 40px 1fr 150px 100px;
                    gap: 15px;
                    align-items: center;
                    padding: 15px;
                    background: #f8f9fa;
                    border-radius: 8px;
                ">
                    <div style="
                        width: 32px;
                        height: 32px;
                        background: #007bff;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-weight: bold;
                        font-size: 12px;
                    ">
                        {{ loop.index }}
                    </div>
                    
                    <div>
                        <h5 style="margin: 0; color: #333;">{{ student.name }}</h5>
                        <p style="margin: 2px 0 0 0; color: #666; font-size: 12px;">
                            {{ student.admission_number or 'No Admission Number' }}
                        </p>
                    </div>
                    
                    <input type="number" 
                           class="form-input" 
                           placeholder="Enter mark"
                           min="0" 
                           max="100"
                           step="0.1"
                           style="text-align: center;">
                    
                    <div style="text-align: center; color: #6c757d; font-size: 12px;">
                        Not Entered
                    </div>
                </div>
                {% endfor %}
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button type="button" class="btn btn-success" style="padding: 12px 30px;">
                    <i class="fas fa-save"></i>
                    Save All Marks
                </button>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
function selectAssignment(subjectId, gradeId, streamId) {
    const url = new URL(window.location.href);
    url.searchParams.set('subject_id', subjectId);
    url.searchParams.set('grade_id', gradeId);
    if (streamId) {
        url.searchParams.set('stream_id', streamId);
    } else {
        url.searchParams.delete('stream_id');
    }
    window.location.href = url.toString();
}
</script>
{% endblock %}
