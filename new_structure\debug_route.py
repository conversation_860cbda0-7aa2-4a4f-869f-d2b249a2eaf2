from flask import Flask, request, jsonify
import sys
from datetime import datetime

# Debug logging route for mobile navigation
@app.route('/debug-log', methods=['POST'])
def debug_log():
    try:
        data = request.json
        message = data.get('message', '')
        timestamp = data.get('timestamp', datetime.now().isoformat())
        
        # Print to terminal with timestamp
        print(f"[{timestamp}] {message}", file=sys.stdout, flush=True)
        
        return jsonify({'status': 'success'})
    except Exception as e:
        print(f"[DEBUG-LOG ERROR] {str(e)}", file=sys.stderr, flush=True)
        return jsonify({'status': 'error', 'message': str(e)}), 500

# Helper function to send debug messages to terminal
def send_debug_to_terminal(message):
    """Send debug message to terminal output"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] 📱 MOBILE NAV DEBUG: {message}", file=sys.stdout, flush=True)

# Add this to your existing run.py or main application file
