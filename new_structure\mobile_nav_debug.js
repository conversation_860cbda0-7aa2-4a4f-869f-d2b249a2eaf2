// MO<PERSON>LE NAVIGATION DEBUG SCRIPT - TERMINAL OUTPUT VERSION
// This script will send debug information to the terminal via fetch requests

// Function to send debug info to terminal
function sendToTerminal(message) {
  // Send to Flask backend for terminal logging
  fetch("/debug-log", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      message: message,
      timestamp: new Date().toISOString(),
    }),
  }).catch((err) => {
    // Fallback to console if terminal logging fails
    console.log("📟 TERMINAL:", message);
  });
}

sendToTerminal("🔧 MOBILE NAVIGATION DEBUG SCRIPT STARTING...");
sendToTerminal("=================================================");

// Step 1: Check if navigation element exists
const navElement = document.getElementById("classteacherNav");
sendToTerminal("1. Navigation element found: " + !!navElement);

if (!navElement) {
  sendToTerminal("❌ Navigation not found! Looking for alternatives...");
  const allNavs = document.querySelectorAll('[id*="nav"], [class*="nav"]');
  sendToTerminal('Found elements with "nav": ' + allNavs.length);
  allNavs.forEach((el, i) => {
    sendToTerminal(
      `  ${i + 1}. ${el.tagName} - ID: ${el.id} - Class: ${el.className}`
    );
  });
} else {
  // Step 2: Check navigation state
  sendToTerminal("2. Navigation current state:");
  sendToTerminal("   - Classes: " + navElement.className);
  sendToTerminal(
    "   - Display: " + window.getComputedStyle(navElement).display
  );
  sendToTerminal(
    "   - Visibility: " + window.getComputedStyle(navElement).visibility
  );
  sendToTerminal(
    "   - Opacity: " + window.getComputedStyle(navElement).opacity
  );
  sendToTerminal(
    "   - Position: " + window.getComputedStyle(navElement).position
  );
  sendToTerminal("   - Z-index: " + window.getComputedStyle(navElement).zIndex);
  sendToTerminal(
    "   - Transform: " + window.getComputedStyle(navElement).transform
  );

  // Step 3: Check navigation links
  const links = navElement.querySelectorAll(".nav-link, .logout-btn");
  sendToTerminal("3. Navigation links found: " + links.length);

  if (links.length === 0) {
    sendToTerminal("❌ No navigation links found! Checking for any links...");
    const allLinks = navElement.querySelectorAll("a");
    sendToTerminal("   - Found <a> tags: " + allLinks.length);
    allLinks.forEach((link, i) => {
      sendToTerminal(
        `     ${i + 1}. "${link.textContent.trim()}" - Class: ${link.className}`
      );
    });
  } else {
    links.forEach((link, i) => {
      const styles = window.getComputedStyle(link);
      sendToTerminal(`   Link ${i + 1}: "${link.textContent.trim()}"`);
      sendToTerminal(`     - Display: ${styles.display}`);
      sendToTerminal(`     - Visibility: ${styles.visibility}`);
      sendToTerminal(`     - Opacity: ${styles.opacity}`);
      sendToTerminal(`     - Pointer Events: ${styles.pointerEvents}`);
      sendToTerminal(`     - Position: ${styles.position}`);
    });
  }

  // Step 4: Check hamburger button
  const toggleBtn = document.querySelector(".mobile-nav-toggle");
  sendToTerminal("4. Hamburger button found: " + !!toggleBtn);

  if (toggleBtn) {
    sendToTerminal(
      "   - Button visible: " +
        (window.getComputedStyle(toggleBtn).display !== "none")
    );
    sendToTerminal(
      "   - Button clickable: " +
        (window.getComputedStyle(toggleBtn).pointerEvents !== "none")
    );
  }

  // Step 5: Test manual toggle
  sendToTerminal("5. Testing manual navigation toggle...");

  if (navElement.classList.contains("show")) {
    sendToTerminal("   Navigation is currently open, closing...");
    navElement.classList.remove("show");
  } else {
    sendToTerminal("   Navigation is currently closed, opening...");
    navElement.classList.add("show");

    // Apply emergency styles
    navElement.style.cssText = `
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background: rgba(102, 126, 234, 0.98) !important;
            flex-direction: column !important;
            justify-content: flex-start !important;
            align-items: center !important;
            gap: 1rem !important;
            z-index: 9999 !important;
            padding: 3rem 2rem !important;
            overflow-y: auto !important;
            -webkit-overflow-scrolling: touch !important;
        `;

    // Force show all links
    links.forEach((link) => {
      link.style.cssText = `
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
                color: white !important;
                background: rgba(255, 255, 255, 0.15) !important;
                padding: 1.2rem 1.5rem !important;
                border-radius: 12px !important;
                text-decoration: none !important;
                font-size: 1.1rem !important;
                margin: 0.8rem 0 !important;
                border: 2px solid rgba(255, 255, 255, 0.3) !important;
                width: 100% !important;
                max-width: 280px !important;
                box-sizing: border-box !important;
                align-items: center !important;
                justify-content: center !important;
                pointer-events: auto !important;
                cursor: pointer !important;
                z-index: 10001 !important;
                position: relative !important;
                min-height: 54px !important;
            `;
    });

    sendToTerminal("✅ Navigation manually opened with emergency styles");
  }
}

sendToTerminal("=================================================");
sendToTerminal("🔧 DEBUG SCRIPT COMPLETED");
sendToTerminal("If navigation is now visible, the issue was CSS-related.");
sendToTerminal("If still not visible, there may be HTML structure issues.");
// Copy and paste this into your browser console (F12) to debug the navigation

console.log("🔧 MOBILE NAVIGATION DEBUG SCRIPT STARTING...");
console.log("=================================================");

// Step 1: Check if navigation element exists
const nav = document.getElementById("classteacherNav");
console.log("1. Navigation element found:", !!nav);

if (!nav) {
  console.log("❌ Navigation not found! Looking for alternatives...");
  const allNavs = document.querySelectorAll('[id*="nav"], [class*="nav"]');
  console.log('Found elements with "nav":', allNavs.length);
  allNavs.forEach((el, i) => {
    console.log(
      `  ${i + 1}. ${el.tagName} - ID: ${el.id} - Class: ${el.className}`
    );
  });
} else {
  // Step 2: Check navigation state
  console.log("2. Navigation current state:");
  console.log("   - Classes:", nav.className);
  console.log("   - Display:", window.getComputedStyle(nav).display);
  console.log("   - Visibility:", window.getComputedStyle(nav).visibility);
  console.log("   - Opacity:", window.getComputedStyle(nav).opacity);
  console.log("   - Position:", window.getComputedStyle(nav).position);
  console.log("   - Z-index:", window.getComputedStyle(nav).zIndex);
  console.log("   - Transform:", window.getComputedStyle(nav).transform);

  // Step 3: Check navigation links
  const links = nav.querySelectorAll(".nav-link, .logout-btn");
  console.log("3. Navigation links found:", links.length);

  if (links.length === 0) {
    console.log("❌ No navigation links found! Checking for any links...");
    const allLinks = nav.querySelectorAll("a");
    console.log("   - Found <a> tags:", allLinks.length);
    allLinks.forEach((link, i) => {
      console.log(
        `     ${i + 1}. "${link.textContent.trim()}" - Class: ${link.className}`
      );
    });
  } else {
    links.forEach((link, i) => {
      const styles = window.getComputedStyle(link);
      console.log(`   Link ${i + 1}: "${link.textContent.trim()}"`);
      console.log(`     - Display: ${styles.display}`);
      console.log(`     - Visibility: ${styles.visibility}`);
      console.log(`     - Opacity: ${styles.opacity}`);
      console.log(`     - Pointer Events: ${styles.pointerEvents}`);
      console.log(`     - Position: ${styles.position}`);
    });
  }

  // Step 4: Check hamburger button
  const toggleBtn = document.querySelector(".mobile-nav-toggle");
  console.log("4. Hamburger button found:", !!toggleBtn);

  if (toggleBtn) {
    console.log(
      "   - Button visible:",
      window.getComputedStyle(toggleBtn).display !== "none"
    );
    console.log(
      "   - Button clickable:",
      window.getComputedStyle(toggleBtn).pointerEvents !== "none"
    );
  }

  // Step 5: Test manual toggle
  console.log("5. Testing manual navigation toggle...");

  if (nav.classList.contains("show")) {
    console.log("   Navigation is currently open, closing...");
    nav.classList.remove("show");
  } else {
    console.log("   Navigation is currently closed, opening...");
    nav.classList.add("show");

    // Apply emergency styles
    nav.style.cssText = `
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background: rgba(102, 126, 234, 0.98) !important;
            flex-direction: column !important;
            justify-content: flex-start !important;
            align-items: center !important;
            gap: 1rem !important;
            z-index: 9999 !important;
            padding: 3rem 2rem !important;
            overflow-y: auto !important;
            -webkit-overflow-scrolling: touch !important;
        `;

    // Force show all links
    links.forEach((link) => {
      link.style.cssText = `
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
                color: white !important;
                background: rgba(255, 255, 255, 0.15) !important;
                padding: 1.2rem 1.5rem !important;
                border-radius: 12px !important;
                text-decoration: none !important;
                font-size: 1.1rem !important;
                margin: 0.8rem 0 !important;
                border: 2px solid rgba(255, 255, 255, 0.3) !important;
                width: 100% !important;
                max-width: 280px !important;
                box-sizing: border-box !important;
                align-items: center !important;
                justify-content: center !important;
                pointer-events: auto !important;
                cursor: pointer !important;
                z-index: 10001 !important;
                position: relative !important;
                min-height: 54px !important;
            `;
    });

    console.log("✅ Navigation manually opened with emergency styles");
  }
}

console.log("=================================================");
console.log("🔧 DEBUG SCRIPT COMPLETED");
console.log("If navigation is now visible, the issue was CSS-related.");
console.log("If still not visible, there may be HTML structure issues.");
