/* ===== PREMIUM MANAGEMENT CARDS INTERACTIONS ===== */
/* Enhanced interactions for premium card experience */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🎨 Initializing Premium Cards Interactions...');
    
    // Initialize premium card effects
    initializePremiumCards();
    
    // Add search functionality
    initializeCardSearch();
    
    // Add category filtering
    initializeCategoryFiltering();
    
    console.log('✨ Premium Cards Interactions Ready!');
});

function initializePremiumCards() {
    const cards = document.querySelectorAll('.quick-action-card');
    
    cards.forEach((card, index) => {
        // Add staggered entrance animation
        card.style.animationDelay = `${index * 0.1}s`;
        
        // Add premium hover sound effect (visual feedback)
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-12px) scale(1.02)';
            
            // Add subtle glow effect
            const icon = this.querySelector('.quick-action-icon');
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            }
            
            // Animate premium indicator
            const indicator = this.querySelector('.premium-dot');
            if (indicator) {
                indicator.style.animationDuration = '0.5s';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            
            // Reset icon
            const icon = this.querySelector('.quick-action-icon');
            if (icon) {
                icon.style.transform = '';
            }
            
            // Reset premium indicator
            const indicator = this.querySelector('.premium-dot');
            if (indicator) {
                indicator.style.animationDuration = '2s';
            }
        });
        
        // Add click ripple effect
        card.addEventListener('click', function(e) {
            createRippleEffect(e, this);
        });
        
        // Add focus enhancement for accessibility
        card.addEventListener('focus', function() {
            this.style.outline = '3px solid rgba(59, 130, 246, 0.5)';
            this.style.outlineOffset = '4px';
        });
        
        card.addEventListener('blur', function() {
            this.style.outline = '';
            this.style.outlineOffset = '';
        });
    });
}

function createRippleEffect(event, element) {
    const ripple = document.createElement('div');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
        z-index: 1;
    `;
    
    element.style.position = 'relative';
    element.appendChild(ripple);
    
    // Remove ripple after animation
    setTimeout(() => {
        if (ripple.parentNode) {
            ripple.parentNode.removeChild(ripple);
        }
    }, 600);
}

function initializeCardSearch() {
    const searchInput = document.getElementById('management-search');
    if (!searchInput) return;
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const cards = document.querySelectorAll('.quick-action-card');
        
        cards.forEach(card => {
            const title = card.querySelector('.quick-action-title')?.textContent.toLowerCase() || '';
            const desc = card.querySelector('.quick-action-desc')?.textContent.toLowerCase() || '';
            
            if (title.includes(searchTerm) || desc.includes(searchTerm)) {
                card.style.display = '';
                card.style.animation = 'cardSlideIn 0.3s ease-out';
            } else {
                card.style.display = 'none';
            }
        });
        
        // Update grid layout
        updateGridLayout();
    });
}

function initializeCategoryFiltering() {
    // Add category filter buttons if they don't exist
    const managementSection = document.querySelector('.modern-grid').parentElement;
    if (!managementSection) return;
    
    // Create filter buttons
    const filterContainer = document.createElement('div');
    filterContainer.className = 'premium-filters';
    filterContainer.innerHTML = `
        <div class="filter-buttons">
            <button class="filter-btn active" data-category="all">All</button>
            <button class="filter-btn" data-category="core">Core</button>
            <button class="filter-btn" data-category="structure">Structure</button>
            <button class="filter-btn" data-category="hub">Hub</button>
        </div>
    `;
    
    // Insert before the grid
    const grid = document.querySelector('.modern-grid');
    grid.parentElement.insertBefore(filterContainer, grid);
    
    // Add filter functionality
    const filterButtons = filterContainer.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // Update active state
            filterButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Filter cards
            const category = this.dataset.category;
            const cards = document.querySelectorAll('.quick-action-card');
            
            cards.forEach(card => {
                if (category === 'all' || card.dataset.category === category) {
                    card.style.display = '';
                    card.style.animation = 'cardSlideIn 0.3s ease-out';
                } else {
                    card.style.display = 'none';
                }
            });
            
            updateGridLayout();
        });
    });
}

function updateGridLayout() {
    const grid = document.querySelector('.modern-grid');
    const visibleCards = grid.querySelectorAll('.quick-action-card:not([style*="display: none"])');
    
    // Adjust grid columns based on visible cards
    if (visibleCards.length <= 2) {
        grid.style.gridTemplateColumns = `repeat(${visibleCards.length}, 1fr)`;
    } else {
        grid.style.gridTemplateColumns = '';
    }
}

// Add CSS for new elements
const premiumStyles = document.createElement('style');
premiumStyles.textContent = `
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }
    
    .premium-filters {
        margin-bottom: 24px;
        display: flex;
        justify-content: center;
    }
    
    .filter-buttons {
        display: flex;
        gap: 8px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        padding: 8px;
        border-radius: 16px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
    
    .filter-btn {
        padding: 8px 16px;
        border: none;
        background: transparent;
        border-radius: 12px;
        font-size: 0.85rem;
        font-weight: 600;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .filter-btn:hover {
        background: rgba(59, 130, 246, 0.1);
        color: #2563eb;
        transform: translateY(-2px);
    }
    
    .filter-btn.active {
        background: linear-gradient(135deg, #007bff, #6c63ff);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
    
    @media (max-width: 768px) {
        .filter-buttons {
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .filter-btn {
            padding: 6px 12px;
            font-size: 0.8rem;
        }
    }
`;

document.head.appendChild(premiumStyles);
