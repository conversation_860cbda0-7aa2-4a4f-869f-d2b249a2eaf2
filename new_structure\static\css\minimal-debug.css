/* MINIMAL DEBUG CSS - Your Custom Colors Only */
:root {
  --custom-bg: #fafafa;
  --custom-primary: #263ad1;
  --custom-secondary: #484b6a;
  --custom-accent: #f9cc48;
  --custom-text: #17cfe0;
}

body {
  background-color: var(--custom-bg) !important;
  color: var(--custom-primary) !important;
}

.navbar {
  background-color: var(--custom-bg) !important;
  border-bottom: 1px solid #e8e8e8 !important;
}

/* Card Improvements for Better Text Visibility */
.card {
  background-color: white !important;
  border: 2px solid #e8e8e8 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
  background-color: #f5f5f5 !important;
  border-bottom: 2px solid #e8e8e8 !important;
  color: var(--custom-primary) !important;
  font-weight: 600 !important;
}

.card-title {
  color: var(--custom-primary) !important;
  font-weight: 700 !important;
  font-size: 1.1rem !important;
}

.card-text {
  color: var(--custom-secondary) !important;
  font-weight: 500 !important;
  line-height: 1.5 !important;
}

.card-body {
  color: var(--custom-secondary) !important;
}

/* Management Options Card Specific */
.management-card {
  background-color: white !important;
  border: 2px solid var(--custom-primary) !important;
  box-shadow: 0 4px 12px rgba(38, 58, 209, 0.15) !important;
}

.management-card .card-title {
  color: var(--custom-primary) !important;
  font-weight: 700 !important;
  font-size: 1.2rem !important;
}

.management-card .card-text {
  color: var(--custom-secondary) !important;
  font-weight: 600 !important;
  font-size: 0.95rem !important;
}

/* Quick Action Cards (Management Options) */
.quick-action-card {
  background-color: white !important;
  border: 2px solid var(--custom-primary) !important;
  color: var(--custom-secondary) !important;
  text-decoration: none !important;
  box-shadow: 0 4px 12px rgba(38, 58, 209, 0.15) !important;
  transition: all 0.3s ease !important;
  opacity: 1 !important;
  transform: translateY(0) !important;
  display: block !important;
}

.quick-action-card:hover {
  text-decoration: none !important;
  border-color: var(--custom-accent) !important;
  box-shadow: 0 6px 16px rgba(38, 58, 209, 0.25) !important;
  transform: translateY(-3px) !important;
}

.quick-action-card.hidden {
  opacity: 0 !important;
  transform: translateY(-20px) scale(0.95) !important;
  pointer-events: none !important;
}

.quick-action-card.filtered-out {
  display: none !important;
}

.quick-action-title {
  color: var(--custom-primary) !important;
  font-weight: 700 !important;
  font-size: 1.1rem !important;
  margin-bottom: 8px !important;
  transition: color 0.3s ease !important;
}

.quick-action-desc {
  color: var(--custom-secondary) !important;
  font-weight: 500 !important;
  font-size: 0.9rem !important;
  line-height: 1.4 !important;
  margin-bottom: 8px !important;
  transition: color 0.3s ease !important;
}

.quick-action-icon {
  color: var(--custom-primary) !important;
  font-size: 1.5rem !important;
  transition: transform 0.3s ease !important;
}

.quick-action-icon i {
  color: var(--custom-primary) !important;
}

.quick-action-card:hover .quick-action-icon {
  transform: scale(1.1) !important;
}

/* Animation keyframes for smooth filtering */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
  }
}

.filter-fade-in {
  animation: fadeIn 0.4s ease-out !important;
}

.filter-fade-out {
  animation: fadeOut 0.3s ease-in !important;
} /* Modern badges in cards */
.modern-badge {
  color: white !important;
  font-weight: 600 !important;
  font-size: 0.8rem !important;
}

.badge-info {
  background-color: var(--custom-primary) !important;
  color: white !important;
}

.badge-success {
  background-color: #28a745 !important;
  color: white !important;
}

.badge-warning {
  background-color: var(--custom-accent) !important;
  color: black !important;
  font-weight: 700 !important;
}

/* Filter Navigation Buttons - All, Core, Assignment, Structure */
.modern-nav {
  display: flex !important;
  gap: 0.5rem !important;
  margin-bottom: 1rem !important;
  flex-wrap: wrap !important;
}

.nav-link {
  background-color: white !important;
  color: var(--custom-secondary) !important;
  border: 2px solid #e8e8e8 !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  font-size: 0.9rem !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  min-width: 80px !important;
  text-align: center !important;
}

.nav-link:hover {
  background-color: var(--custom-accent) !important;
  border-color: var(--custom-accent) !important;
  color: black !important;
  text-decoration: none !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(249, 204, 72, 0.3) !important;
}

.nav-link.active {
  background-color: var(--custom-primary) !important;
  border-color: var(--custom-primary) !important;
  color: white !important;
  font-weight: 700 !important;
  box-shadow: 0 4px 12px rgba(38, 58, 209, 0.3) !important;
}

.nav-link.active:hover {
  background-color: var(--custom-primary) !important;
  border-color: var(--custom-primary) !important;
  color: white !important;
}

/* Management Filters Section */
.management-filters {
  background-color: #f8f9fa !important;
  padding: 1.5rem !important;
  border-radius: 12px !important;
  border: 1px solid #e8e8e8 !important;
  margin-bottom: 2rem !important;
}

/* Search Input in Filters */
#management-search {
  width: 100% !important;
  padding: 0.75rem 1rem !important;
  border: 2px solid #e8e8e8 !important;
  border-radius: 8px !important;
  font-size: 0.95rem !important;
  background-color: white !important;
  color: var(--custom-secondary) !important;
}

#management-search:focus {
  outline: none !important;
  border-color: var(--custom-primary) !important;
  box-shadow: 0 0 0 3px rgba(38, 58, 209, 0.1) !important;
}

/* Mobile responsive adjustments for filters */
@media (max-width: 768px) {
  .modern-nav {
    gap: 0.3rem !important;
  }

  .nav-link {
    padding: 0.6rem 1rem !important;
    font-size: 0.85rem !important;
    min-width: 70px !important;
    flex: 1 !important;
  }

  .management-filters {
    padding: 1rem !important;
    margin-bottom: 1.5rem !important;
  }
}

@media (max-width: 480px) {
  .modern-nav {
    flex-direction: column !important;
    gap: 0.5rem !important;
  }

  .nav-link {
    width: 100% !important;
    min-width: unset !important;
    padding: 0.8rem 1.2rem !important;
  }
}

/* Enhanced search input with icon */
#management-search {
  padding-left: 2.5rem !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23484b6a' viewBox='0 0 24 24'%3E%3Cpath d='M15.5 14h-.79l-.28-.27A6.471 6.471 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E") !important;
  background-repeat: no-repeat !important;
  background-position: 0.75rem center !important;
  background-size: 1.2rem !important;
  transition: all 0.3s ease !important;
}

/* Active button scaling effect */
.nav-link.active {
  transform: scale(1.02) !important;
}

.nav-link.active:hover {
  transform: scale(1.02) translateY(-1px) !important;
} /* Button Improvements */
.btn-primary {
  background-color: var(--custom-accent) !important;
  border-color: var(--custom-accent) !important;
  color: black !important;
  font-weight: 600 !important;
}

.btn-primary:hover {
  background-color: #f7c332 !important;
  border-color: #f7c332 !important;
  color: black !important;
}

.btn-secondary {
  background-color: var(--custom-primary) !important;
  border-color: var(--custom-primary) !important;
  color: white !important;
  font-weight: 600 !important;
}

.btn {
  font-weight: 600 !important;
  border-radius: 6px !important;
  padding: 0.5rem 1rem !important;
}

/* Text Improvements */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--custom-primary) !important;
  font-weight: 700 !important;
}

p {
  color: var(--custom-secondary) !important;
  font-weight: 500 !important;
}

.text-muted {
  color: var(--custom-secondary) !important;
  opacity: 0.8 !important;
  font-weight: 500 !important;
}

a {
  color: var(--custom-text) !important;
  font-weight: 500 !important;
}

a:hover {
  color: #14a5b5 !important;
}

/* Small text improvements */
small,
.small {
  color: var(--custom-secondary) !important;
  font-weight: 500 !important;
}

/* List improvements */
ul li,
ol li {
  color: var(--custom-secondary) !important;
  font-weight: 500 !important;
}

/* Table improvements */
.table {
  background-color: white !important;
}

.table th {
  background-color: #f5f5f5 !important;
  color: var(--custom-primary) !important;
  font-weight: 700 !important;
  border-bottom: 2px solid #e8e8e8 !important;
}

.table td {
  color: var(--custom-secondary) !important;
  font-weight: 500 !important;
  border-top: 1px solid #e8e8e8 !important;
}
