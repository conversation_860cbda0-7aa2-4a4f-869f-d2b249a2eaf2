-- SQL script to add sample assignments for Kevin
-- First, let's check what data we have

-- Check Kevin's teacher ID
SELECT id, username, role FROM teacher WHERE username = 'kevin';

-- Check available subjects
SELECT id, name, education_level FROM subject LIMIT 10;

-- Check available grades
SELECT id, name FROM grade;

-- Check available streams
SELECT id, name, grade_id FROM stream;

-- Check existing assignments for Kevin
SELECT tsa.*, t.username, s.name as subject_name, g.name as grade_name, st.name as stream_name 
FROM teacher_subject_assignment tsa
JOIN teacher t ON tsa.teacher_id = t.id
JOIN subject s ON tsa.subject_id = s.id
JOIN grade g ON tsa.grade_id = g.id
LEFT JOIN stream st ON tsa.stream_id = st.id
WHERE t.username = 'kevin';

-- Add sample assignments for <PERSON> (assuming <PERSON>'s ID is 2)
-- Mathematics for Grade 7 Stream A
INSERT INTO teacher_subject_assignment (teacher_id, subject_id, grade_id, stream_id, is_class_teacher)
SELECT 
    (SELECT id FROM teacher WHERE username = 'kevin'),
    (SELECT id FROM subject WHERE name = 'MATHEMATICS' LIMIT 1),
    (SELECT id FROM grade WHERE name = 'Grade 7' LIMIT 1),
    (SELECT id FROM stream WHERE name = 'A' AND grade_id = (SELECT id FROM grade WHERE name = 'Grade 7' LIMIT 1) LIMIT 1),
    0
WHERE NOT EXISTS (
    SELECT 1 FROM teacher_subject_assignment 
    WHERE teacher_id = (SELECT id FROM teacher WHERE username = 'kevin')
    AND subject_id = (SELECT id FROM subject WHERE name = 'MATHEMATICS' LIMIT 1)
    AND grade_id = (SELECT id FROM grade WHERE name = 'Grade 7' LIMIT 1)
);

-- English for Grade 7 Stream A
INSERT INTO teacher_subject_assignment (teacher_id, subject_id, grade_id, stream_id, is_class_teacher)
SELECT 
    (SELECT id FROM teacher WHERE username = 'kevin'),
    (SELECT id FROM subject WHERE name = 'ENGLISH' LIMIT 1),
    (SELECT id FROM grade WHERE name = 'Grade 7' LIMIT 1),
    (SELECT id FROM stream WHERE name = 'A' AND grade_id = (SELECT id FROM grade WHERE name = 'Grade 7' LIMIT 1) LIMIT 1),
    0
WHERE NOT EXISTS (
    SELECT 1 FROM teacher_subject_assignment 
    WHERE teacher_id = (SELECT id FROM teacher WHERE username = 'kevin')
    AND subject_id = (SELECT id FROM subject WHERE name = 'ENGLISH' LIMIT 1)
    AND grade_id = (SELECT id FROM grade WHERE name = 'Grade 7' LIMIT 1)
);

-- Science for Grade 8 Stream A
INSERT INTO teacher_subject_assignment (teacher_id, subject_id, grade_id, stream_id, is_class_teacher)
SELECT 
    (SELECT id FROM teacher WHERE username = 'kevin'),
    (SELECT id FROM subject WHERE name = 'SCIENCE' LIMIT 1),
    (SELECT id FROM grade WHERE name = 'Grade 8' LIMIT 1),
    (SELECT id FROM stream WHERE name = 'A' AND grade_id = (SELECT id FROM grade WHERE name = 'Grade 8' LIMIT 1) LIMIT 1),
    0
WHERE NOT EXISTS (
    SELECT 1 FROM teacher_subject_assignment 
    WHERE teacher_id = (SELECT id FROM teacher WHERE username = 'kevin')
    AND subject_id = (SELECT id FROM subject WHERE name = 'SCIENCE' LIMIT 1)
    AND grade_id = (SELECT id FROM grade WHERE name = 'Grade 8' LIMIT 1)
);

-- Verify the assignments were added
SELECT tsa.*, t.username, s.name as subject_name, g.name as grade_name, st.name as stream_name 
FROM teacher_subject_assignment tsa
JOIN teacher t ON tsa.teacher_id = t.id
JOIN subject s ON tsa.subject_id = s.id
JOIN grade g ON tsa.grade_id = g.id
LEFT JOIN stream st ON tsa.stream_id = st.id
WHERE t.username = 'kevin';
