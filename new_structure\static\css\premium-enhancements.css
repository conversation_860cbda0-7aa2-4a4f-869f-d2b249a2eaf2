/* ===================================================================
   HILLVIEW SCHOOL MANAGEMENT SYSTEM - PREMIUM ENHANCEMENTS
   Applies the premium color palette to improve text visibility and aesthetics
   ================================================================== */

/* ===== GLOBAL IMPROVEMENTS ===== */
body {
  font-family: var(--font-family-sans) !important;
  background: var(--bg-gradient-primary) !important;
  color: var(--text-primary) !important;
  line-height: var(--line-height-normal) !important;
  font-size: var(--font-size-base) !important;
}

/* ===== NAVIGATION IMPROVEMENTS ===== */
.navbar {
  background: var(--bg-gradient-primary) !important;
  border-bottom: 1px solid var(--border-primary) !important;
  box-shadow: var(--shadow-sm) !important;
}

.navbar-brand {
  color: var(--text-inverse) !important;
  font-weight: var(--font-weight-bold) !important;
  font-size: var(--font-size-xl) !important;
}

.nav-link {
  color: rgba(248, 250, 252, 0.9) !important;
  font-weight: var(--font-weight-medium) !important;
  transition: var(--transition-all) !important;
}

.nav-link:hover {
  color: var(--text-inverse) !important;
  background: rgba(255, 255, 255, 0.1) !important;
}

.logout-btn {
  background: var(--error-bg) !important;
  border: 1px solid var(--error-border) !important;
  color: var(--error-color) !important;
  font-weight: var(--font-weight-medium) !important;
}

.logout-btn:hover {
  background: var(--error-color) !important;
  color: var(--text-inverse) !important;
}

/* ===== CARD IMPROVEMENTS ===== */
.modern-card,
.stat-card,
.card {
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-sm) !important;
  transition: var(--transition-all) !important;
}

.modern-card:hover,
.stat-card:hover,
.card:hover {
  box-shadow: var(--shadow-md) !important;
  transform: translateY(-2px) !important;
}

.card-header {
  background: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
  font-weight: var(--font-weight-semibold) !important;
  padding: var(--space-4) var(--space-6) !important;
}

.card-body {
  padding: var(--space-6) !important;
  color: var(--text-secondary) !important;
}

.card-title {
  color: var(--text-primary) !important;
  font-weight: var(--font-weight-semibold) !important;
  font-size: var(--font-size-lg) !important;
  margin-bottom: var(--space-3) !important;
}

/* ===== BUTTON IMPROVEMENTS ===== */
.modern-btn,
.btn {
  font-family: var(--font-family-sans) !important;
  font-weight: var(--font-weight-medium) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-3) var(--space-6) !important;
  transition: var(--transition-all) !important;
  border: 1px solid transparent !important;
  cursor: pointer !important;
}

.btn-primary,
.modern-btn.btn-primary {
  background: var(--primary-color) !important;
  color: var(--text-inverse) !important;
  border-color: var(--primary-color) !important;
}

.btn-primary:hover,
.modern-btn.btn-primary:hover {
  background: var(--primary-hover) !important;
  border-color: var(--primary-hover) !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-md) !important;
}

.btn-secondary,
.modern-btn.btn-secondary {
  background: var(--secondary-color) !important;
  color: var(--text-inverse) !important;
  border-color: var(--secondary-color) !important;
}

.btn-secondary:hover,
.modern-btn.btn-secondary:hover {
  background: var(--secondary-hover) !important;
  border-color: var(--secondary-hover) !important;
}

.btn-success,
.modern-btn.btn-success {
  background: var(--success-color) !important;
  color: var(--text-inverse) !important;
  border-color: var(--success-color) !important;
}

.btn-success:hover,
.modern-btn.btn-success:hover {
  background: var(--accent-hover) !important;
  border-color: var(--accent-hover) !important;
}

.btn-warning,
.modern-btn.btn-warning {
  background: var(--warning-color) !important;
  color: var(--text-inverse) !important;
  border-color: var(--warning-color) !important;
}

.btn-warning:hover,
.modern-btn.btn-warning:hover {
  opacity: var(--hover-opacity) !important;
}

.btn-danger,
.modern-btn.btn-danger {
  background: var(--error-color) !important;
  color: var(--text-inverse) !important;
  border-color: var(--error-color) !important;
}

.btn-danger:hover,
.modern-btn.btn-danger:hover {
  opacity: var(--hover-opacity) !important;
}

.btn-outline-primary {
  background: transparent !important;
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.btn-outline-primary:hover {
  background: var(--primary-color) !important;
  color: var(--text-inverse) !important;
}

/* ===== FORM IMPROVEMENTS ===== */
.form-control,
.form-select {
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-3) var(--space-4) !important;
  color: var(--text-primary) !important;
  font-size: var(--font-size-base) !important;
  transition: var(--transition-all) !important;
}

.form-control:focus,
.form-select:focus {
  border-color: var(--border-focus) !important;
  box-shadow: var(--focus-ring) !important;
  outline: none !important;
}

.form-control::placeholder {
  color: var(--text-placeholder) !important;
}

.form-label {
  color: var(--text-primary) !important;
  font-weight: var(--font-weight-medium) !important;
  margin-bottom: var(--space-2) !important;
}

.form-text {
  color: var(--text-tertiary) !important;
  font-size: var(--font-size-sm) !important;
}

/* ===== TABLE IMPROVEMENTS ===== */
.table {
  background: var(--bg-primary) !important;
  border-radius: var(--radius-lg) !important;
  overflow: hidden !important;
  box-shadow: var(--shadow-sm) !important;
}

.table thead th {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  font-weight: var(--font-weight-semibold) !important;
  border-bottom: 2px solid var(--border-primary) !important;
  padding: var(--space-4) !important;
}

.table tbody td {
  padding: var(--space-4) !important;
  border-bottom: 1px solid var(--border-primary) !important;
  color: var(--text-secondary) !important;
}

.table tbody tr:hover {
  background: var(--bg-secondary) !important;
}

/* ===== STATS IMPROVEMENTS ===== */
.stat-number,
.stat-value {
  color: var(--text-primary) !important;
  font-weight: var(--font-weight-bold) !important;
  font-size: var(--font-size-3xl) !important;
}

.stat-label {
  color: var(--text-tertiary) !important;
  font-weight: var(--font-weight-medium) !important;
  font-size: var(--font-size-sm) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
}

/* ===== ALERT IMPROVEMENTS ===== */
.alert {
  border-radius: var(--radius-md) !important;
  padding: var(--space-4) var(--space-6) !important;
  border: 1px solid transparent !important;
  font-weight: var(--font-weight-medium) !important;
}

.alert-success {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
  color: var(--success-text) !important;
}

.alert-warning {
  background: var(--warning-bg) !important;
  border-color: var(--warning-border) !important;
  color: var(--warning-text) !important;
}

.alert-danger {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
  color: var(--error-text) !important;
}

.alert-info {
  background: var(--info-bg) !important;
  border-color: var(--info-border) !important;
  color: var(--info-text) !important;
}

/* ===== MODAL IMPROVEMENTS ===== */
.modal {
  background: rgba(15, 23, 42, 0.5) !important;
  backdrop-filter: var(--glass-backdrop) !important;
}

.modal-content {
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
}

.modal-header {
  background: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
  padding: var(--space-6) !important;
}

.modal-title {
  color: var(--text-primary) !important;
  font-weight: var(--font-weight-semibold) !important;
  font-size: var(--font-size-lg) !important;
}

.modal-body {
  padding: var(--space-6) !important;
  color: var(--text-secondary) !important;
}

.modal-footer {
  background: var(--bg-secondary) !important;
  border-top: 1px solid var(--border-primary) !important;
  padding: var(--space-4) var(--space-6) !important;
}

/* ===== TYPOGRAPHY IMPROVEMENTS ===== */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--text-primary) !important;
  font-weight: var(--font-weight-semibold) !important;
  line-height: var(--line-height-tight) !important;
}

h1 {
  font-size: var(--font-size-4xl) !important;
}

h2 {
  font-size: var(--font-size-3xl) !important;
}

h3 {
  font-size: var(--font-size-2xl) !important;
}

h4 {
  font-size: var(--font-size-xl) !important;
}

h5 {
  font-size: var(--font-size-lg) !important;
}

h6 {
  font-size: var(--font-size-base) !important;
}

p {
  color: var(--text-secondary) !important;
  line-height: var(--line-height-relaxed) !important;
}

.text-muted {
  color: var(--text-muted) !important;
}

.text-primary {
  color: var(--primary-color) !important;
}

.text-secondary {
  color: var(--secondary-color) !important;
}

.text-success {
  color: var(--success-color) !important;
}

.text-warning {
  color: var(--warning-color) !important;
}

.text-danger {
  color: var(--error-color) !important;
}

.text-info {
  color: var(--info-color) !important;
}

/* ===== BADGE IMPROVEMENTS ===== */
.badge {
  font-weight: var(--font-weight-medium) !important;
  font-size: var(--font-size-xs) !important;
  padding: var(--space-1) var(--space-3) !important;
  border-radius: var(--radius-full) !important;
}

.badge-primary {
  background: var(--primary-color) !important;
  color: var(--text-inverse) !important;
}

.badge-secondary {
  background: var(--secondary-color) !important;
  color: var(--text-inverse) !important;
}

.badge-success {
  background: var(--success-color) !important;
  color: var(--text-inverse) !important;
}

.badge-warning {
  background: var(--warning-color) !important;
  color: var(--text-inverse) !important;
}

.badge-danger {
  background: var(--error-color) !important;
  color: var(--text-inverse) !important;
}

.badge-info {
  background: var(--info-color) !important;
  color: var(--text-inverse) !important;
}

/* ===== CONTAINER IMPROVEMENTS ===== */
.container,
.container-fluid {
  background: var(--bg-primary) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-6) !important;
  box-shadow: var(--shadow-sm) !important;
  border: 1px solid var(--border-primary) !important;
}

.modern-container {
  background: var(--bg-primary) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--space-8) !important;
  box-shadow: var(--shadow-md) !important;
  border: 1px solid var(--border-primary) !important;
}

/* ===== DASHBOARD SPECIFIC IMPROVEMENTS ===== */
.dashboard-header {
  background: var(--bg-gradient-primary) !important;
  color: var(--text-inverse) !important;
  padding: var(--space-8) !important;
  border-radius: var(--radius-lg) !important;
  margin-bottom: var(--space-6) !important;
}

.dashboard-grid {
  display: grid !important;
  gap: var(--space-6) !important;
}

.quick-action-card {
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--space-6) !important;
  transition: var(--transition-all) !important;
  text-decoration: none !important;
  color: var(--text-primary) !important;
}

.quick-action-card:hover {
  box-shadow: var(--shadow-md) !important;
  transform: translateY(-2px) !important;
  border-color: var(--primary-color) !important;
}

/* ===== TABS IMPROVEMENTS ===== */
.nav-tabs {
  border-bottom: 1px solid var(--border-primary) !important;
  margin-bottom: var(--space-6) !important;
}

.nav-tabs .nav-link {
  color: var(--text-secondary) !important;
  border: 1px solid transparent !important;
  padding: var(--space-3) var(--space-6) !important;
  border-radius: var(--radius-md) var(--radius-md) 0 0 !important;
  font-weight: var(--font-weight-medium) !important;
}

.nav-tabs .nav-link.active {
  color: var(--primary-color) !important;
  background: var(--bg-primary) !important;
  border-color: var(--border-primary) var(--border-primary) var(--bg-primary) !important;
}

.nav-tabs .nav-link:hover {
  color: var(--primary-color) !important;
  background: var(--bg-secondary) !important;
}

.tab-content {
  background: var(--bg-primary) !important;
  border-radius: 0 var(--radius-md) var(--radius-md) var(--radius-md) !important;
  padding: var(--space-6) !important;
}

/* ===== FOOTER IMPROVEMENTS ===== */
.site-footer {
  background: var(--bg-secondary) !important;
  border-top: 1px solid var(--border-primary) !important;
  color: var(--text-tertiary) !important;
  padding: var(--space-6) !important;
  text-align: center !important;
  font-size: var(--font-size-sm) !important;
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
:focus {
  outline: 2px solid var(--border-focus) !important;
  outline-offset: 2px !important;
}

/* ===== LOADING STATES ===== */
.loading {
  opacity: var(--disabled-opacity) !important;
  pointer-events: none !important;
}

.skeleton {
  background: linear-gradient(
    90deg,
    var(--bg-secondary) 25%,
    var(--bg-tertiary) 50%,
    var(--bg-secondary) 75%
  ) !important;
  background-size: 200% 100% !important;
  animation: skeleton-loading 1.5s infinite !important;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0 !important;
  }
  100% {
    background-position: -200% 0 !important;
  }
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */
@media (max-width: 768px) {
  .container,
  .container-fluid,
  .modern-container {
    padding: var(--space-4) !important;
    border-radius: var(--radius-md) !important;
  }

  .dashboard-grid {
    grid-template-columns: 1fr !important;
  }

  .table {
    font-size: var(--font-size-sm) !important;
  }

  .btn,
  .modern-btn {
    padding: var(--space-4) var(--space-6) !important;
    font-size: var(--font-size-base) !important;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  .no-print {
    display: none !important;
  }
}
