/* Modern Class Teacher UI Framework */
/* Note: Theme variables are now managed by theme-manager.css */
:root {
  /* Modern Gradient Overrides for specific components */
  --primary-gradient: var(--bg-gradient-primary);
  --secondary-gradient: var(--bg-gradient-secondary);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

  /* Legacy color mappings - these will use theme variables */
  --white: var(--bg-primary);
  --gray-50: var(--bg-secondary);
  --gray-100: var(--bg-tertiary);
  --gray-200: var(--bg-quaternary);
  --gray-300: var(--border-primary);
  --gray-400: var(--border-secondary);
  --gray-500: var(--text-tertiary);
  --gray-600: var(--text-secondary);
  --gray-700: var(--text-primary);
  --gray-800: var(--text-primary);
  --gray-900: var(--text-primary);

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Typography */
  --font-sans: "Inter", "Segoe UI", system-ui, -apple-system, sans-serif;
  --font-mono: "JetBrains Mono", "Fira Code", monospace;
}

/* Global Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-sans);
  background: var(--bg-gradient-primary);
  min-height: 100vh;
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Modern Container */
.modern-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--space-6);
}

/* Modern Header */
.modern-header {
  background: var(--bg-overlay);
  backdrop-filter: var(--glass-backdrop);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  margin-bottom: var(--space-8);
  box-shadow: var(--shadow-xl);
  border: var(--glass-border);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.header-title {
  font-size: 2.5rem;
  font-weight: 800;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-2);
}

.header-subtitle {
  font-size: 1.125rem;
  color: var(--gray-600);
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}

/* Modern Navigation */
.modern-nav {
  display: flex;
  gap: var(--space-2);
  background: var(--gray-100);
  padding: var(--space-2);
  border-radius: var(--radius-xl);
}

.nav-link {
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--gray-600);
  font-weight: 600;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.nav-link:hover,
.nav-link.active {
  background: var(--white);
  color: var(--gray-800);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* Navigation Items (for both buttons and links) */
.nav-item {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--text-inverse);
  font-weight: 600;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  background: var(--bg-glass);
  border: var(--glass-border);
  backdrop-filter: var(--glass-backdrop);
  cursor: pointer;
  font-size: 0.875rem;
  font-family: inherit;
}

.nav-item:hover {
  background: var(--bg-glass);
  border-color: var(--border-glass);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: var(--text-inverse);
  text-decoration: none;
  opacity: 0.9;
}

.nav-item.active {
  background: var(--bg-glass);
  border-color: var(--border-glass);
  box-shadow: var(--shadow-md);
  opacity: 0.8;
}

.nav-item i {
  font-size: 1rem;
}

.nav-main {
  display: flex;
  gap: var(--space-3);
  align-items: center;
  flex-wrap: wrap;
}

/* Modern Cards */
.modern-card {
  background: var(--bg-overlay);
  backdrop-filter: var(--glass-backdrop);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-xl);
  border: var(--glass-border);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.modern-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.modern-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.modern-card:hover::before {
  transform: scaleX(1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 2px solid var(--border-primary);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.card-icon {
  font-size: 1.75rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Modern Buttons */
.modern-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.modern-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.modern-btn:hover::before {
  left: 100%;
}

.modern-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-primary {
  background: var(--primary-gradient);
  color: var(--text-inverse);
}

.btn-secondary {
  background: var(--secondary-gradient);
  color: var(--text-inverse);
}

.btn-success {
  background: var(--success-gradient);
  color: var(--text-inverse);
}

.btn-warning {
  background: var(--warning-gradient);
  color: var(--text-inverse);
}

.btn-danger {
  background: var(--danger-gradient);
  color: var(--text-inverse);
}

.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--border-secondary);
}

.btn-outline:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-tertiary);
}

/* Modern Grid System */
.modern-grid {
  display: grid;
  gap: var(--space-6);
}

.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr);
}
.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}
.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}
.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 768px) {
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }
}

/* Modern Quick Actions */
.modern-quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
  padding: var(--space-6);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.quick-action-card {
  flex: 1;
  min-width: 200px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.quick-action-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.quick-action-card:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-2xl);
  text-decoration: none;
  color: inherit;
}

.quick-action-card:hover::before {
  transform: scaleX(1);
}

.quick-action-icon {
  font-size: 2.5rem;
  margin-bottom: var(--space-3);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.quick-action-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--gray-800);
  margin-bottom: var(--space-2);
}

.quick-action-desc {
  font-size: 0.875rem;
  color: var(--gray-600);
  line-height: 1.5;
}

/* Modern Tables */
.modern-table-container {
  background: var(--white);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
}

.modern-table {
  width: 100%;
  border-collapse: collapse;
}

.modern-table th {
  background: var(--gray-50);
  padding: var(--space-4) var(--space-6);
  text-align: left;
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--gray-200);
}

.modern-table td {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--gray-100);
  vertical-align: middle;
}

.modern-table tr:hover {
  background: var(--gray-50);
}

.modern-table tr:last-child td {
  border-bottom: none;
}

/* Modern Forms */
.modern-form {
  display: grid;
  gap: var(--space-6);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-label {
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.form-input,
.form-select {
  padding: var(--space-4);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  transition: all 0.2s ease;
  background: var(--white);
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Modern Alerts */
.modern-alert {
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  border: 1px solid;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-weight: 500;
}

.alert-success {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.2);
  color: rgb(21, 128, 61);
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
  color: rgb(146, 64, 14);
}

.alert-danger {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
  color: rgb(153, 27, 27);
}

.alert-info {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
  color: rgb(30, 64, 175);
}

/* Modern Tabs */
.modern-tabs {
  background: var(--white);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--space-8);
}

.tab-nav {
  display: flex;
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
}

.tab-button {
  flex: 1;
  padding: var(--space-4) var(--space-6);
  background: none;
  border: none;
  font-weight: 600;
  color: var(--gray-600);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.tab-button.active {
  color: #667eea;
  background: var(--white);
}

.tab-button.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
}

.tab-content {
  padding: var(--space-8);
}

/* Modern Progress */
.modern-progress {
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  height: 8px;
}

.progress-bar {
  height: 100%;
  background: var(--primary-gradient);
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
}

/* Modern Badges */
.modern-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-success {
  background: rgba(34, 197, 94, 0.1);
  color: rgb(21, 128, 61);
}

.badge-warning {
  background: rgba(245, 158, 11, 0.1);
  color: rgb(146, 64, 14);
}

.badge-danger {
  background: rgba(239, 68, 68, 0.1);
  color: rgb(153, 27, 27);
}

.badge-info {
  background: rgba(59, 130, 246, 0.1);
  color: rgb(30, 64, 175);
}

/* Enhanced Mobile-First Responsive Design */

/* Mobile First - Base styles for mobile devices */
@media (max-width: 480px) {
  :root {
    --space-1: 0.125rem;
    --space-2: 0.25rem;
    --space-3: 0.5rem;
    --space-4: 0.75rem;
    --space-5: 1rem;
    --space-6: 1.25rem;
    --space-8: 1.5rem;
    --space-10: 2rem;
    --space-12: 2.5rem;
    --space-16: 3rem;
  }

  body {
    font-size: 14px;
    line-height: 1.5;
  }

  .modern-container {
    padding: var(--space-3);
    max-width: 100%;
  }

  .page-container {
    padding: var(--space-3);
  }

  .content-wrapper {
    padding: 0;
  }

  /* Mobile Navigation */
  .navbar {
    padding: var(--space-4);
    flex-direction: column;
    gap: var(--space-3);
  }

  .navbar-brand {
    font-size: 1.25rem;
    margin-bottom: var(--space-2);
  }

  .navbar-nav {
    flex-direction: column;
    width: 100%;
    gap: var(--space-2);
  }

  .nav-link {
    padding: var(--space-3);
    text-align: center;
    width: 100%;
  }

  .logout-btn {
    width: 100%;
    text-align: center;
    margin-top: var(--space-2);
  }

  /* Mobile Header */
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-4);
  }

  .header-title {
    font-size: 1.5rem;
    line-height: 1.3;
  }

  .header-subtitle {
    font-size: 0.875rem;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
    gap: var(--space-2);
  }

  .back-button {
    width: 100%;
    justify-content: center;
  }

  /* Mobile Navigation Items */
  .nav-main {
    flex-direction: column;
    gap: var(--space-2);
    width: 100%;
  }

  .nav-item {
    width: 100%;
    justify-content: center;
    padding: var(--space-4);
    font-size: 0.875rem;
  }

  /* Mobile Cards */
  .modern-card {
    padding: var(--space-4);
    margin-bottom: var(--space-4);
  }

  .card-header {
    flex-direction: column;
    gap: var(--space-3);
    text-align: center;
  }

  .card-title {
    font-size: 1.125rem;
  }

  /* Mobile Quick Actions */
  .modern-quick-actions {
    flex-direction: column;
    gap: var(--space-3);
    padding: var(--space-4);
  }

  .quick-action-card {
    min-width: auto;
    width: 100%;
    padding: var(--space-4);
    text-align: center;
  }

  .quick-action-icon {
    font-size: 2rem;
  }

  .quick-action-title {
    font-size: 1rem;
  }

  .quick-action-desc {
    font-size: 0.8rem;
  }

  /* Mobile Grid System */
  .modern-grid {
    gap: var(--space-3);
  }

  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }

  /* Mobile Tables */
  .modern-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .modern-table {
    min-width: 600px;
    font-size: 0.75rem;
  }

  .modern-table th,
  .modern-table td {
    padding: var(--space-2) var(--space-3);
    white-space: nowrap;
  }

  /* Mobile Forms */
  .modern-form {
    gap: var(--space-4);
  }

  .form-input,
  .form-select {
    padding: var(--space-3);
    font-size: 1rem; /* Prevent zoom on iOS */
  }

  .form-label {
    font-size: 0.8rem;
  }

  /* Mobile Buttons */
  .modern-btn {
    padding: var(--space-3) var(--space-4);
    font-size: 0.875rem;
    width: 100%;
    justify-content: center;
    margin-bottom: var(--space-2);
  }

  .action-btn {
    width: 100%;
    justify-content: center;
    margin-bottom: var(--space-2);
  }

  /* Mobile Tabs */
  .tab-nav {
    flex-direction: column;
  }

  .tab-button {
    padding: var(--space-3);
    text-align: center;
  }

  .tab-content {
    padding: var(--space-4);
  }

  /* Mobile Alerts */
  .modern-alert {
    flex-direction: column;
    text-align: center;
    gap: var(--space-2);
  }

  /* Mobile Badges */
  .modern-badge {
    font-size: 0.7rem;
    padding: var(--space-1) var(--space-2);
  }
}

/* Small Mobile Devices */
@media (max-width: 576px) {
  .modern-container {
    padding: var(--space-2);
  }

  .header-title {
    font-size: 1.25rem;
  }

  .quick-action-card {
    padding: var(--space-3);
  }

  .modern-card {
    padding: var(--space-3);
  }

  .modern-btn {
    padding: var(--space-2) var(--space-3);
    font-size: 0.8rem;
  }
}

/* Tablet Portrait */
@media (max-width: 768px) and (min-width: 481px) {
  .modern-container {
    padding: var(--space-4);
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-4);
  }

  .modern-nav {
    flex-wrap: wrap;
    justify-content: center;
  }

  .nav-item {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }

  .modern-quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
  }

  .quick-action-card {
    min-width: auto;
  }

  .modern-grid.grid-cols-3,
  .modern-grid.grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Tablet Landscape */
@media (max-width: 1024px) and (min-width: 769px) {
  .modern-container {
    padding: var(--space-5);
  }

  .header-title {
    font-size: 2rem;
  }

  .modern-quick-actions {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-4);
  }

  .quick-action-card {
    min-width: auto;
  }

  .modern-grid.grid-cols-4 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Desktop */
@media (min-width: 1025px) {
  .modern-container {
    padding: var(--space-6);
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading States */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Mobile-Specific Utilities */
.mobile-hidden {
  display: none;
}

.mobile-only {
  display: block;
}

@media (min-width: 769px) {
  .mobile-hidden {
    display: block;
  }

  .mobile-only {
    display: none;
  }
}

/* Touch-Friendly Components */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile Navigation Drawer */
.mobile-nav-toggle {
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  padding: var(--space-2);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.mobile-nav-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

@media (max-width: 768px) {
  .mobile-nav-toggle {
    display: block;
  }

  .navbar-nav {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(102, 126, 234, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
    padding: var(--space-4);
    box-shadow: var(--shadow-xl);
    flex-direction: column;
    gap: var(--space-2);
    z-index: 1000;
  }

  .navbar-nav.show {
    display: flex;
  }

  /* Enhanced mobile navigation for different page types */
  .nav-main.show {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
  }

  .navbar-menu.show {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
  }

  #classteacherNav.show,
  #teacherNav.show,
  #parentNav.show {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
  }
}

/* Mobile-Optimized Tables */
.mobile-table {
  display: none;
}

@media (max-width: 768px) {
  .desktop-table {
    display: none;
  }

  .mobile-table {
    display: block;
  }

  .mobile-table-card {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    margin-bottom: var(--space-3);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
  }

  .mobile-table-header {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--space-2);
    padding-bottom: var(--space-2);
    border-bottom: 1px solid var(--border-color);
  }

  .mobile-table-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2) 0;
    border-bottom: 1px solid var(--gray-100);
  }

  .mobile-table-row:last-child {
    border-bottom: none;
  }

  .mobile-table-label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.875rem;
  }

  .mobile-table-value {
    font-weight: 600;
    color: var(--text-primary);
  }
}

/* Mobile Form Enhancements */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: var(--space-3);
  }

  .form-group {
    width: 100%;
  }

  .form-actions {
    flex-direction: column;
    gap: var(--space-3);
  }

  .form-actions .modern-btn {
    width: 100%;
  }
}

/* Mobile Modal Enhancements */
@media (max-width: 768px) {
  .modal-dialog {
    margin: var(--space-4);
    max-width: calc(100vw - 2rem);
  }

  .modal-content {
    border-radius: var(--radius-lg);
  }

  .modal-header {
    padding: var(--space-4);
    flex-direction: column;
    text-align: center;
    gap: var(--space-2);
  }

  .modal-body {
    padding: var(--space-4);
  }

  .modal-footer {
    padding: var(--space-4);
    flex-direction: column;
    gap: var(--space-3);
  }

  .modal-footer .modern-btn {
    width: 100%;
  }
}

/* Mobile Analytics Cards */
@media (max-width: 768px) {
  .analytics-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .analytics-card {
    padding: var(--space-4);
  }

  .chart-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .chart-container canvas {
    min-width: 300px;
  }
}

/* Mobile Dashboard Stats */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-3);
  }

  .stat-card {
    padding: var(--space-4);
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    font-size: 2rem;
    margin-right: 0;
    margin-bottom: var(--space-2);
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-label {
    font-size: 0.75rem;
  }
}

/* Mobile Performance Optimizations */
@media (max-width: 768px) {
  * {
    -webkit-tap-highlight-color: transparent;
  }

  .modern-card,
  .quick-action-card,
  .modern-btn {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  .modern-table-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
}

/* Extra Small Mobile Devices (360px and below) */
@media (max-width: 360px) {
  :root {
    --space-1: 0.1rem;
    --space-2: 0.2rem;
    --space-3: 0.4rem;
    --space-4: 0.6rem;
    --space-5: 0.8rem;
    --space-6: 1rem;
    --space-8: 1.2rem;
  }

  .modern-container {
    padding: var(--space-3);
  }

  .modern-header {
    padding: var(--space-4);
  }

  .page-title {
    font-size: 1.25rem;
  }

  .page-subtitle {
    font-size: 0.75rem;
  }

  .modern-card {
    padding: var(--space-4);
    margin-bottom: var(--space-4);
  }

  .card-title {
    font-size: 1rem;
  }

  .modern-btn {
    padding: var(--space-3) var(--space-4);
    font-size: 0.8rem;
    min-height: 40px;
  }

  .nav-item {
    padding: var(--space-3);
    font-size: 0.8rem;
    min-height: 44px;
  }

  .form-input,
  .form-select,
  .form-textarea {
    padding: var(--space-3);
    font-size: 0.9rem;
    min-height: 44px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .stat-card {
    padding: var(--space-3);
  }

  .stat-value {
    font-size: 1.25rem;
  }

  .stat-label {
    font-size: 0.7rem;
  }
}

/* Touch-friendly enhancements for all mobile devices */
@media (max-width: 768px) and (pointer: coarse) {
  .modern-btn,
  .nav-item,
  .form-input,
  .form-select,
  .form-textarea,
  .theme-toggle {
    min-height: 44px;
  }

  .clickable,
  .interactive {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
