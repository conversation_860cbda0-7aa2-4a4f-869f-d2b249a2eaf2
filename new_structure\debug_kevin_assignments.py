#!/usr/bin/env python3
"""
Debug script to check <PERSON>'s teacher assignments and data visibility issues.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import Teacher, TeacherSubjectAssignment, Subject, Grade, Stream, Student, Mark
from extensions import db
from app import create_app

def debug_kevin_assignments():
    """Debug <PERSON>'s assignments and data."""
    app = create_app()
    
    with app.app_context():
        print("=== DEBUGGING KEVIN'S ASSIGNMENTS ===")
        
        # Find Kevin
        kevin = Teacher.query.filter_by(username='kevin').first()
        if not kevin:
            print("❌ Kevin not found in database")
            return
        
        print(f"✅ Found Kevin: ID={kevin.id}, Role={kevin.role}")
        
        # Check <PERSON>'s subject assignments
        assignments = TeacherSubjectAssignment.query.filter_by(teacher_id=kevin.id).all()
        print(f"\n📚 <PERSON>'s Subject Assignments ({len(assignments)} total):")
        
        if not assignments:
            print("❌ No subject assignments found for <PERSON>")
        else:
            for assignment in assignments:
                subject_name = assignment.subject.name if assignment.subject else "Unknown"
                grade_name = assignment.grade.name if assignment.grade else "Unknown"
                stream_name = assignment.stream.name if assignment.stream else "All"
                print(f"  - {subject_name} | Grade {grade_name} | Stream {stream_name}")
        
        # Check if Kevin has any marks uploaded
        marks = Mark.query.join(TeacherSubjectAssignment).filter(
            TeacherSubjectAssignment.teacher_id == kevin.id
        ).limit(5).all()
        
        print(f"\n📊 Recent Marks by Kevin ({len(marks)} found):")
        for mark in marks:
            student_name = mark.student.name if mark.student else "Unknown"
            subject_name = mark.subject.name if mark.subject else "Unknown"
            print(f"  - {student_name}: {subject_name} = {mark.percentage}%")
        
        # Check total students and grades
        total_students = Student.query.count()
        total_grades = Grade.query.count()
        total_subjects = Subject.query.count()
        
        print(f"\n📈 Database Statistics:")
        print(f"  - Total Students: {total_students}")
        print(f"  - Total Grades: {total_grades}")
        print(f"  - Total Subjects: {total_subjects}")
        
        # Check if Kevin has stream assignment
        if kevin.stream_id:
            stream = Stream.query.get(kevin.stream_id)
            if stream:
                grade = Grade.query.get(stream.grade_id)
                print(f"\n🏫 Kevin's Class Assignment:")
                print(f"  - Grade: {grade.name if grade else 'Unknown'}")
                print(f"  - Stream: {stream.name}")
        else:
            print(f"\n🏫 Kevin has no direct class assignment")

if __name__ == "__main__":
    debug_kevin_assignments()
