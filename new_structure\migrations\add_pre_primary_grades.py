#!/usr/bin/env python3
"""
Migration script to add Pre-Primary grades (PP1, PP2) to the database.
This implements the correct Kenya CBC educational structure.
"""

import sys
import os
from datetime import datetime

# Add the parent directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
grandparent_dir = os.path.dirname(parent_dir)
sys.path.insert(0, grandparent_dir)

def add_pre_primary_grades():
    """Add Pre-Primary grades (PP1, PP2) to the database."""
    
    try:
        from new_structure import create_app
        from new_structure.extensions import db
        from new_structure.models.academic import Grade, Stream
        
        print("🎓 Adding Pre-Primary Grades (PP1, PP2)")
        print("=" * 50)
        
        # Create Flask app context
        app = create_app('development')
        
        with app.app_context():
            # Check current database connection
            print(f"📍 Database URI: {app.config['SQLALCHEMY_DATABASE_URI']}")
            
            # Check existing grades
            existing_grades = Grade.query.all()
            existing_grade_names = [grade.name for grade in existing_grades]
            
            print(f"📋 Current grades in database: {len(existing_grades)}")
            for grade in existing_grades:
                print(f"   - {grade.name} ({grade.education_level})")
            
            # Add PP1 and PP2 if they don't exist
            pre_primary_grades = [
                ('PP1', 'pre_primary'),
                ('PP2', 'pre_primary')
            ]
            
            added_grades = []
            for grade_name, education_level in pre_primary_grades:
                if grade_name not in existing_grade_names:
                    new_grade = Grade(name=grade_name, education_level=education_level)
                    db.session.add(new_grade)
                    added_grades.append(grade_name)
                    print(f"✅ Added grade: {grade_name} ({education_level})")
                else:
                    print(f"⚠️  Grade {grade_name} already exists")
            
            # Commit the changes
            if added_grades:
                db.session.commit()
                print(f"\n🎉 Successfully added {len(added_grades)} pre-primary grades!")
            else:
                print(f"\n✅ All pre-primary grades already exist in database")
            
            # Verify the addition
            print(f"\n🔍 Verification - Current grades after migration:")
            all_grades = Grade.query.order_by(Grade.name).all()
            for grade in all_grades:
                print(f"   - {grade.name} ({grade.education_level})")
            
            print(f"\n📊 Educational Level Distribution:")
            levels = {}
            for grade in all_grades:
                if grade.education_level not in levels:
                    levels[grade.education_level] = []
                levels[grade.education_level].append(grade.name)
            
            for level, grades in levels.items():
                print(f"   {level}: {', '.join(grades)}")
            
            return True
            
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pre_primary_grades():
    """Test that pre-primary grades were added correctly."""
    
    try:
        from new_structure import create_app
        from new_structure.models.academic import Grade
        from new_structure.utils.constants import educational_level_mapping
        
        app = create_app('development')
        
        with app.app_context():
            print("\n🧪 Testing Pre-Primary Grades...")
            
            # Test database grades
            pp1 = Grade.query.filter_by(name='PP1').first()
            pp2 = Grade.query.filter_by(name='PP2').first()
            
            if pp1 and pp2:
                print(f"✅ PP1 found: {pp1.name} ({pp1.education_level})")
                print(f"✅ PP2 found: {pp2.name} ({pp2.education_level})")
            else:
                print(f"❌ Pre-primary grades not found in database")
                return False
            
            # Test constants mapping
            if 'pre_primary' in educational_level_mapping:
                pre_primary_grades = educational_level_mapping['pre_primary']
                print(f"✅ Constants mapping: pre_primary = {pre_primary_grades}")
                
                if 'PP1' in pre_primary_grades and 'PP2' in pre_primary_grades:
                    print(f"✅ PP1 and PP2 correctly mapped in constants")
                else:
                    print(f"❌ PP1 or PP2 missing from constants mapping")
                    return False
            else:
                print(f"❌ pre_primary not found in educational_level_mapping")
                return False
            
            print(f"✅ All tests passed!")
            return True
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Pre-Primary Grades Migration")
    print("=" * 40)
    
    # Run the migration
    success = add_pre_primary_grades()
    
    if success:
        print("\n🔍 Running tests...")
        test_success = test_pre_primary_grades()
        
        if test_success:
            print("\n" + "=" * 40)
            print("✅ PRE-PRIMARY GRADES MIGRATION COMPLETED!")
            print("🎓 Added grades:")
            print("   - PP1 (Pre-Primary 1)")
            print("   - PP2 (Pre-Primary 2)")
            print("📚 Educational structure now includes:")
            print("   - Pre-Primary: PP1, PP2")
            print("   - Lower Primary: Grade 1, 2, 3")
            print("   - Upper Primary: Grade 4, 5, 6")
            print("   - Junior Secondary: Grade 7, 8, 9")
            print("=" * 40)
        else:
            print("\n❌ TESTS FAILED!")
    else:
        print("\n❌ MIGRATION FAILED!")
        sys.exit(1)
