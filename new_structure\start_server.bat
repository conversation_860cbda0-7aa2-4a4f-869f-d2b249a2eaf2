@echo off
REM Enhanced Windows batch script for Hillview School Management System
REM This script provides better compatibility with Windows and MINGW64 environments

title Hillview School Management System

echo.
echo ======================================
echo   Hillview School Management System
echo ======================================
echo.

REM Check if we're in a virtual environment
if defined VIRTUAL_ENV (
    echo [INFO] Virtual environment detected: %VIRTUAL_ENV%
) else (
    echo [WARNING] No virtual environment detected
    echo [INFO] Activating virtual environment...
    if exist "venv\Scripts\activate.bat" (
        call venv\Scripts\activate.bat
        echo [SUCCESS] Virtual environment activated
    ) else (
        echo [ERROR] Virtual environment not found
        echo [INFO] Please create a virtual environment first:
        echo         python -m venv venv
        echo         venv\Scripts\activate.bat
        echo         pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo.
echo [INFO] Starting Hillview School Management System...
echo [INFO] Press Ctrl+C to stop the server
echo.

REM Try to run the enhanced script first, fallback to original if needed
if exist "run_enhanced.py" (
    python run_enhanced.py
) else (
    python run.py
)

echo.
echo [INFO] Server stopped
pause
