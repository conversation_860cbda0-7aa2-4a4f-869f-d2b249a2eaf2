/* Hillview School Management System - Theme Manager */
/* Centralized theme management with light and dark mode support */

:root {
  /* ===== LIGHT THEME (DEFAULT) ===== */

  /* Primary Brand Colors */
  --primary-color: #1f7d53;
  --primary-hover: #18230f;
  --primary-light: #4ade80;
  --primary-dark: #0f5132;

  /* Secondary Colors */
  --secondary-color: #18230f;
  --secondary-hover: #2c5530;
  --secondary-light: #4a5c3a;

  /* Accent Colors */
  --accent-color: #4ade80;
  --accent-hover: #22c55e;
  --accent-light: #86efac;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-quaternary: #e2e8f0;
  --bg-overlay: rgba(255, 255, 255, 0.95);
  --bg-glass: rgba(255, 255, 255, 0.1);
  --bg-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --bg-gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

  /* Text Colors */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-tertiary: #94a3b8;
  --text-light: #cbd5e1;
  --text-inverse: #ffffff;
  --text-muted: #6b7280;

  /* Border Colors */
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --border-tertiary: #94a3b8;
  --border-focus: #3b82f6;
  --border-glass: rgba(255, 255, 255, 0.2);

  /* Status Colors */
  --success-color: #10b981;
  --success-bg: rgba(16, 185, 129, 0.1);
  --success-border: rgba(16, 185, 129, 0.2);

  --warning-color: #f59e0b;
  --warning-bg: rgba(245, 158, 11, 0.1);
  --warning-border: rgba(245, 158, 11, 0.2);

  --error-color: #ef4444;
  --error-bg: rgba(239, 68, 68, 0.1);
  --error-border: rgba(239, 68, 68, 0.2);

  --info-color: #3b82f6;
  --info-bg: rgba(59, 130, 246, 0.1);
  --info-border: rgba(59, 130, 246, 0.2);

  /* Shadow Colors */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Glassmorphism Effects */
  --glass-backdrop: blur(20px);
  --glass-border: 1px solid rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* ===== DARK THEME ===== */
[data-theme="dark"] {
  /* Primary Brand Colors - Adjusted for dark mode */
  --primary-color: #4ade80;
  --primary-hover: #22c55e;
  --primary-light: #86efac;
  --primary-dark: #16a34a;

  /* Secondary Colors */
  --secondary-color: #374151;
  --secondary-hover: #4b5563;
  --secondary-light: #6b7280;

  /* Accent Colors */
  --accent-color: #60a5fa;
  --accent-hover: #3b82f6;
  --accent-light: #93c5fd;

  /* Background Colors */
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --bg-quaternary: #4b5563;
  --bg-overlay: rgba(17, 24, 39, 0.95);
  --bg-glass: rgba(17, 24, 39, 0.3);
  --bg-gradient-primary: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  --bg-gradient-secondary: linear-gradient(135deg, #374151 0%, #1f2937 100%);

  /* Text Colors */
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-tertiary: #9ca3af;
  --text-light: #6b7280;
  --text-inverse: #111827;
  --text-muted: #9ca3af;

  /* Border Colors */
  --border-primary: #374151;
  --border-secondary: #4b5563;
  --border-tertiary: #6b7280;
  --border-focus: #60a5fa;
  --border-glass: rgba(255, 255, 255, 0.1);

  /* Status Colors - Adjusted for dark mode */
  --success-color: #34d399;
  --success-bg: rgba(52, 211, 153, 0.1);
  --success-border: rgba(52, 211, 153, 0.2);

  --warning-color: #fbbf24;
  --warning-bg: rgba(251, 191, 36, 0.1);
  --warning-border: rgba(251, 191, 36, 0.2);

  --error-color: #f87171;
  --error-bg: rgba(248, 113, 113, 0.1);
  --error-border: rgba(248, 113, 113, 0.2);

  --info-color: #60a5fa;
  --info-bg: rgba(96, 165, 250, 0.1);
  --info-border: rgba(96, 165, 250, 0.2);

  /* Shadow Colors - Darker shadows for dark mode */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4),
    0 4px 6px -4px rgb(0 0 0 / 0.3);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4),
    0 8px 10px -6px rgb(0 0 0 / 0.3);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.5);

  /* Glassmorphism Effects - Adjusted for dark mode */
  --glass-backdrop: blur(20px);
  --glass-border: 1px solid rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
}

/* ===== THEME TRANSITION ANIMATIONS ===== */
* {
  transition: background-color 0.3s ease, color 0.3s ease,
    border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Disable transitions during theme switch to prevent flashing */
.theme-switching * {
  transition: none !important;
}

/* ===== THEME TOGGLE BUTTON STYLES ===== */
.theme-toggle {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 26px;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-secondary);
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
}

.theme-toggle:hover {
  background: var(--bg-quaternary);
  border-color: var(--border-tertiary);
}

.theme-toggle:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

.theme-toggle-slider {
  position: absolute;
  width: 18px;
  height: 18px;
  background: var(--primary-color);
  border-radius: 50%;
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: var(--text-inverse);
}

.theme-toggle[data-theme="light"] .theme-toggle-slider {
  transform: translateX(-10px);
}

.theme-toggle[data-theme="dark"] .theme-toggle-slider {
  transform: translateX(10px);
}

/* Theme toggle icons */
.theme-toggle-icon {
  font-size: 10px;
  opacity: 0.8;
}

/* ===== RESPONSIVE THEME TOGGLE ===== */
@media (max-width: 768px) {
  .theme-toggle {
    width: 45px;
    height: 24px;
  }

  .theme-toggle-slider {
    width: 16px;
    height: 16px;
    font-size: 9px;
  }

  .theme-toggle[data-theme="light"] .theme-toggle-slider {
    transform: translateX(-9px);
  }

  .theme-toggle[data-theme="dark"] .theme-toggle-slider {
    transform: translateX(9px);
  }

  .theme-toggle-label {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .theme-toggle-container {
    gap: 0.25rem;
  }

  .theme-toggle {
    width: 40px;
    height: 22px;
  }

  .theme-toggle-slider {
    width: 14px;
    height: 14px;
    font-size: 8px;
  }

  .theme-toggle[data-theme="light"] .theme-toggle-slider {
    transform: translateX(-8px);
  }

  .theme-toggle[data-theme="dark"] .theme-toggle-slider {
    transform: translateX(8px);
  }

  .theme-toggle-label {
    display: none; /* Hide label on very small screens */
  }
}

@media (max-width: 360px) {
  .theme-toggle {
    width: 36px;
    height: 20px;
  }

  .theme-toggle-slider {
    width: 12px;
    height: 12px;
    font-size: 7px;
  }

  .theme-toggle[data-theme="light"] .theme-toggle-slider {
    transform: translateX(-7px);
  }

  .theme-toggle[data-theme="dark"] .theme-toggle-slider {
    transform: translateX(7px);
  }
}
