/**
 * HIL<PERSON><PERSON>EW SCHOOL MANAGEMENT SYSTEM - MOBILE NAVIGATION
 * Fixed to work with existing HTML structure
 */

// Navigation feature mapping for backward compatibility
window.navigateToFeature = function(feature) {
    // Navigate to feature
    switch (feature) {
        case 'upload-marks':
            if (typeof switchMainTab === 'function') {
                switchMainTab('upload-marks');
            } else {
                // Fallback method
                const uploadTab = document.getElementById('upload-marks-tab');
                if (uploadTab) {
                    // Hide all tabs
                    document.querySelectorAll('.tab-content-container').forEach(tab => {
                        tab.classList.remove('active');
                    });
                    // Show upload marks tab
                    uploadTab.classList.add('active');
                    
                    // Update tab buttons
                    document.querySelectorAll('.tab-button').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    const uploadBtn = document.querySelector('.tab-button[onclick*="upload-marks"]');
                    if (uploadBtn) {
                        uploadBtn.classList.add('active');
                    }
                }
            }
            
            // Scroll to upload marks section
            setTimeout(() => {
                const uploadSection = document.getElementById('upload-marks-tab') ||
                                    document.getElementById('upload-marks-section');
                if (uploadSection) {
                    uploadSection.scrollIntoView({ 
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }, 100);
            break;
            
        case 'recent-reports':
            if (typeof switchMainTab === 'function') {
                switchMainTab('recent-reports');
            }
            break;
            
        case 'generate-reports':
            if (typeof switchMainTab === 'function') {
                switchMainTab('generate-reports');
            }
            break;
            
        case 'management':
            if (typeof switchMainTab === 'function') {
                switchMainTab('management');
            }
            break;
            
        default:
            console.warn('Unknown feature:', feature);
    }
    
    return false; // Prevent default link behavior
};

class MobileNavigation {
    constructor() {
        this.navElement = document.getElementById('classteacherNav');
        this.toggleBtn = document.querySelector('.mobile-nav-toggle');
        this.toggleIcon = document.querySelector('.mobile-nav-toggle i');
        this.isOpen = false;
        
        this.init();
    }
    
    init() {
        if (!this.toggleBtn || !this.navElement) {
            console.warn('Mobile navigation elements not found');
            return;
        }
        
        // Add event listeners
        this.toggleBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggleNav();
        });
        
        // Handle navigation clicks
        this.navElement.addEventListener('click', (e) => {
            const link = e.target.closest('a[data-tab]');
            if (link) {
                e.preventDefault();
                const tab = link.dataset.tab;
                this.navigateToTab(tab);
            }
        });
        
        // Close navigation when clicking outside
        document.addEventListener('click', (e) => {
            if (this.isOpen && !this.navElement.contains(e.target) && !this.toggleBtn.contains(e.target)) {
                this.closeNav();
            }
        });
        
        // Close navigation on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeNav();
            }
        });
        
        console.log('Mobile navigation initialized');
    }
    
    toggleNav() {
        if (this.isOpen) {
            this.closeNav();
        } else {
            this.openNav();
        }
    }
    
    openNav() {
        this.navElement.classList.add('mobile-nav-open');
        this.toggleIcon.classList.remove('fa-bars');
        this.toggleIcon.classList.add('fa-times');
        this.isOpen = true;
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
        
        console.log('Mobile navigation opened');
    }
    
    closeNav() {
        this.navElement.classList.remove('mobile-nav-open');
        this.toggleIcon.classList.remove('fa-times');
        this.toggleIcon.classList.add('fa-bars');
        this.isOpen = false;
        
        // Restore body scroll
        document.body.style.overflow = '';
        
        console.log('Mobile navigation closed');
    }
    
    navigateToTab(tab) {
        this.closeNav();
        
        // Use the global navigateToFeature function
        if (typeof navigateToFeature === 'function') {
            navigateToFeature(tab);
        }
        
        // Scroll to section
        setTimeout(() => {
            const section = document.getElementById(`${tab}-tab`) || 
                          document.getElementById(`${tab}-section`);
            if (section) {
                section.scrollIntoView({ 
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }, 100);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.mobileNav = new MobileNavigation();
});

// Also initialize if DOM is already loaded
if (document.readyState !== 'loading') {
    window.mobileNav = new MobileNavigation();
}
