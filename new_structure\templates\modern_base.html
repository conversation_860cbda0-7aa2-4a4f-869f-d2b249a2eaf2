<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#667eea" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Hillview SMS" />
    <meta name="msapplication-TileColor" content="#667eea" />
    <meta
      name="description"
      content="Hillview School Management System - Complete solution for students, teachers, and parents"
    />

    <title>
      {% block title %}{{ school_info.school_name or 'Hillview School' }}
      Management{% endblock %}
    </title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="/static/manifest.json" />

    <!-- <PERSON><PERSON> Icons -->
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="/static/images/icons/icon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="/static/images/icons/icon-16x16.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="/static/images/icons/icon-180x180.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="152x152"
      href="/static/images/icons/icon-152x152.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="144x144"
      href="/static/images/icons/icon-144x144.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="120x120"
      href="/static/images/icons/icon-120x120.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="114x114"
      href="/static/images/icons/icon-114x114.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="76x76"
      href="/static/images/icons/icon-76x76.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="72x72"
      href="/static/images/icons/icon-72x72.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="60x60"
      href="/static/images/icons/icon-60x60.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="57x57"
      href="/static/images/icons/icon-57x57.png"
    />

    <!-- Modern Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Icons -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />

    <!-- Theme Manager CSS -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/theme-manager.css') }}"
    />

    <!-- Modern CSS Framework -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/modern_classteacher.css') }}"
    />

    <!-- Page-specific CSS -->
    {% block extra_css %}{% endblock %}

    <style>
      /* Page-specific modern enhancements */
      .page-container {
        background: var(--bg-gradient-primary);
        min-height: 100vh;
        padding: var(--space-6);
      }

      .content-wrapper {
        max-width: 1400px;
        margin: 0 auto;
      }

      .page-header {
        background: var(--bg-overlay);
        backdrop-filter: var(--glass-backdrop);
        border-radius: var(--radius-2xl);
        padding: var(--space-8);
        margin-bottom: var(--space-8);
        box-shadow: var(--shadow-xl);
        border: var(--glass-border);
      }

      .breadcrumb-nav {
        background: var(--bg-glass);
        backdrop-filter: var(--glass-backdrop);
        border-radius: var(--radius-xl);
        padding: var(--space-4) var(--space-6);
        margin-bottom: var(--space-6);
        border: var(--glass-border);
      }

      .breadcrumb-nav a {
        color: rgba(255, 255, 255, 0.9);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s ease;
      }

      .breadcrumb-nav a:hover {
        color: white;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
      }

      .breadcrumb-nav .separator {
        color: rgba(255, 255, 255, 0.6);
        margin: 0 var(--space-3);
      }

      .breadcrumb-nav .current {
        color: white;
        font-weight: 600;
      }

      .main-content {
        background: var(--bg-overlay);
        backdrop-filter: var(--glass-backdrop);
        border-radius: var(--radius-2xl);
        padding: var(--space-8);
        box-shadow: var(--shadow-xl);
        border: var(--glass-border);
        min-height: 60vh;
        color: var(--text-primary);
      }

      .flash-messages {
        margin-bottom: var(--space-6);
      }

      .flash-message {
        padding: var(--space-4) var(--space-6);
        border-radius: var(--radius-lg);
        margin-bottom: var(--space-3);
        border: 1px solid;
        display: flex;
        align-items: center;
        gap: var(--space-3);
        font-weight: 500;
        animation: slideUp 0.3s ease-out;
      }

      .flash-success {
        background: var(--success-bg);
        border-color: var(--success-border);
        color: var(--success-color);
      }

      .flash-warning {
        background: var(--warning-bg);
        border-color: var(--warning-border);
        color: var(--warning-color);
      }

      .flash-error {
        background: var(--error-bg);
        border-color: var(--error-border);
        color: var(--error-color);
      }

      .flash-info {
        background: var(--info-bg);
        border-color: var(--info-border);
        color: var(--info-color);
      }

      .page-actions {
        display: flex;
        gap: var(--space-4);
        margin-bottom: var(--space-6);
        flex-wrap: wrap;
      }

      .back-button {
        display: inline-flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-3) var(--space-6);
        background: var(--bg-glass);
        color: var(--text-inverse);
        text-decoration: none;
        border-radius: var(--radius-lg);
        font-weight: 600;
        transition: all 0.2s ease;
        backdrop-filter: blur(10px);
        border: var(--glass-border);
      }

      .back-button:hover {
        background: var(--bg-glass);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        text-decoration: none;
        color: var(--text-inverse);
        opacity: 0.9;
      }

      /* Mobile-specific enhancements */
      @media (max-width: 768px) {
        .page-container {
          padding: var(--space-3);
        }

        .content-wrapper {
          padding: 0;
        }

        .page-header {
          padding: var(--space-4);
          margin-bottom: var(--space-4);
        }

        .header-content {
          flex-direction: column;
          text-align: center;
          gap: var(--space-4);
        }

        .header-title {
          font-size: 1.5rem;
        }

        .header-subtitle {
          font-size: 0.875rem;
        }

        .header-actions {
          width: 100%;
          justify-content: center;
        }

        .back-button {
          width: 100%;
          justify-content: center;
        }

        .main-content {
          padding: var(--space-4);
        }

        .breadcrumb-nav {
          padding: var(--space-3);
          margin-bottom: var(--space-4);
        }

        .breadcrumb-nav a,
        .breadcrumb-nav .current {
          font-size: 0.875rem;
        }
      }

      @media (max-width: 480px) {
        .page-container {
          padding: var(--space-2);
        }

        .page-header {
          padding: var(--space-3);
          margin-bottom: var(--space-3);
        }

        .main-content {
          padding: var(--space-3);
        }

        .header-title {
          font-size: 1.25rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="page-container">
      <div class="content-wrapper">
        <!-- Breadcrumb Navigation -->
        {% block breadcrumb %}
        <nav class="breadcrumb-nav">
          <a href="{{ url_for('classteacher.dashboard') }}">
            <i class="fas fa-home"></i>
            Dashboard
          </a>
          <span class="separator">
            <i class="fas fa-chevron-right"></i>
          </span>
          <span class="current">{% block page_title %}Page{% endblock %}</span>
        </nav>
        {% endblock %}

        <!-- Page Header -->
        <header class="page-header fade-in">
          <div class="header-content">
            <div>
              <h1 class="header-title">
                {% block header_icon %}<i class="fas fa-cog"></i>{% endblock %}
                {% block header_title %}Page Title{% endblock %}
              </h1>
              <p class="header-subtitle">
                {% block header_subtitle %}Page description{% endblock %}
              </p>
            </div>
            <div class="header-actions">
              {% block header_actions %}
              <!-- Theme Toggle -->
              <div class="theme-toggle-container">
                <button
                  class="theme-toggle"
                  type="button"
                  role="switch"
                  aria-label="Toggle theme"
                  title="Toggle light/dark mode"
                  data-theme="light"
                >
                  <div class="theme-toggle-slider">
                    <i class="theme-toggle-icon fas fa-sun"></i>
                  </div>
                </button>
                <span class="theme-toggle-label">Theme</span>
              </div>

              <a
                href="{{ url_for('classteacher.dashboard') }}"
                class="back-button"
              >
                <i class="fas fa-arrow-left"></i>
                Back to Dashboard
              </a>
              {% endblock %}
            </div>
          </div>
        </header>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %} {% if
        messages %}
        <div class="flash-messages">
          {% for category, message in messages %}
          <div
            class="flash-message flash-{{ 'error' if category == 'error' else category }}"
          >
            <i
              class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' if category == 'warning' else 'times-circle' if category == 'error' else 'info-circle' }}"
            ></i>
            {{ message }}
          </div>
          {% endfor %}
        </div>
        {% endif %} {% endwith %}

        <!-- Main Content -->
        <main class="main-content slide-up">
          {% block content %}
          <p>Content goes here</p>
          {% endblock %}
        </main>
      </div>
    </div>

    <!-- Force HTTP Script (Development Only) -->
    <script src="{{ url_for('static', filename='js/force-http.js') }}"></script>

    <!-- Theme Manager JavaScript -->
    <script src="{{ url_for('static', filename='js/theme-manager.js') }}"></script>

    <!-- JavaScript -->
    <script>
      // Modern UI JavaScript
      document.addEventListener("DOMContentLoaded", function () {
        // Auto-hide flash messages after 5 seconds
        const flashMessages = document.querySelectorAll(".flash-message");
        flashMessages.forEach((message) => {
          setTimeout(() => {
            message.style.animation = "fadeOut 0.3s ease-out forwards";
            setTimeout(() => {
              message.remove();
            }, 300);
          }, 5000);
        });

        // Add loading states to form submissions
        const forms = document.querySelectorAll("form");
        forms.forEach((form) => {
          form.addEventListener("submit", function () {
            const submitButton = form.querySelector(
              'button[type="submit"], input[type="submit"]'
            );
            if (submitButton) {
              submitButton.classList.add("loading");
              submitButton.disabled = true;
            }
          });
        });

        // Enhanced button interactions
        const buttons = document.querySelectorAll(".modern-btn");
        buttons.forEach((button) => {
          button.addEventListener("mouseenter", function () {
            this.style.transform = "translateY(-2px)";
          });

          button.addEventListener("mouseleave", function () {
            this.style.transform = "translateY(0)";
          });
        });
      });

      // Add fadeOut animation
      const style = document.createElement("style");
      style.textContent = `
            @keyframes fadeOut {
                from { opacity: 1; transform: translateY(0); }
                to { opacity: 0; transform: translateY(-10px); }
            }
        `;
      document.head.appendChild(style);
    </script>

    <!-- PWA Manager -->
    <script src="/static/js/pwa-manager.js"></script>

    {% block extra_js %}{% endblock %}
  </body>
</html>
