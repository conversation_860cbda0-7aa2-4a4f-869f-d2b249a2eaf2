#!/usr/bin/env python3
"""
Hillview School Management System - Virtual Environment Runner
This script ensures the application runs with the correct virtual environment.
"""
import sys
import os

def main():
    # Get the directory containing this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Add the script directory to Python path
    if script_dir not in sys.path:
        sys.path.insert(0, script_dir)
    
    print("🚀 Starting Hillview School Management System...")
    print(f"📁 Working Directory: {script_dir}")
    print(f"🐍 Python Executable: {sys.executable}")
    
    try:
        # Test imports first
        print("🔍 Testing imports...")
        
        # Test Flask-WTF import
        try:
            import flask_wtf
            print("✅ Flask-WTF imported successfully")
        except ImportError as e:
            print(f"❌ Flask-WTF import failed: {e}")
            print("💡 Please install Flask-WTF: pip install Flask-WTF==1.1.1")
            return False
        
        # Test application import
        try:
            from new_structure import create_app
            print("✅ Application module imported successfully")
        except ImportError as e:
            print(f"❌ Application import failed: {e}")
            return False
        
        # Create application
        print("🏗️ Creating application...")
        app = create_app()
        print("✅ Application created successfully!")
        
        # Start the server
        print("🌐 Starting server...")
        print("📍 Local:   http://localhost:3000")
        print("🌐 Network: http://0.0.0.0:3000")
        print("=" * 50)
        
        app.run(
            host='0.0.0.0',
            port=3000,
            debug=True,
            use_reloader=False
        )
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n💡 Troubleshooting:")
        print("1. Make sure you're in the virtual environment")
        print("2. Install required packages: pip install Flask-WTF WTForms Flask-SQLAlchemy")
        print("3. Check that all dependencies are installed")
        return False
        
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    if not success:
        input("Press Enter to exit...")
        sys.exit(1)
