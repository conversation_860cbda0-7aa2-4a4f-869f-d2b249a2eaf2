<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSL/HTTPS Not Supported - Hillview School</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Theme Manager CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/theme-manager.css') }}">
    
    <style>
        body {
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
            background: var(--bg-gradient-primary);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
            padding: 1rem;
            color: var(--text-primary);
        }

        .error-container {
            background: var(--bg-overlay);
            backdrop-filter: var(--glass-backdrop);
            border-radius: 1.5rem;
            padding: 3rem;
            box-shadow: var(--shadow-2xl);
            border: var(--glass-border);
            max-width: 600px;
            width: 100%;
            text-align: center;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .error-icon {
            font-size: 4rem;
            color: var(--warning-color);
            margin-bottom: 1.5rem;
        }

        .error-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .error-message {
            font-size: 1.125rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .solution-box {
            background: var(--bg-secondary);
            border-radius: 1rem;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid var(--primary-color);
        }

        .solution-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .solution-text {
            color: var(--text-secondary);
            margin-bottom: 1rem;
        }

        .url-box {
            background: var(--bg-tertiary);
            border-radius: 0.5rem;
            padding: 1rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 1rem;
            color: var(--primary-color);
            border: 2px solid var(--border-primary);
            word-break: break-all;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: var(--text-inverse);
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 2px solid var(--border-primary);
        }

        .btn-secondary:hover {
            background: var(--bg-tertiary);
            border-color: var(--border-secondary);
        }

        .technical-details {
            margin-top: 2rem;
            padding: 1rem;
            background: var(--bg-secondary);
            border-radius: 0.5rem;
            font-size: 0.875rem;
            color: var(--text-tertiary);
        }

        .theme-toggle-container {
            position: absolute;
            top: 1rem;
            right: 1rem;
        }

        @media (max-width: 768px) {
            .error-container {
                padding: 2rem;
                margin: 1rem;
            }

            .error-title {
                font-size: 1.5rem;
            }

            .error-message {
                font-size: 1rem;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Theme Toggle -->
    <div class="theme-toggle-container">
        <div class="theme-toggle-container">
            <button class="theme-toggle" 
                    type="button" 
                    role="switch" 
                    aria-label="Toggle theme"
                    title="Toggle light/dark mode"
                    data-theme="light">
                <div class="theme-toggle-slider">
                    <i class="theme-toggle-icon fas fa-sun"></i>
                </div>
            </button>
            <span class="theme-toggle-label">Theme</span>
        </div>
    </div>

    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-shield-alt"></i>
        </div>
        
        <h1 class="error-title">HTTPS Not Supported</h1>
        
        <p class="error-message">
            You're trying to access this server using HTTPS, but this development server only supports HTTP connections.
        </p>

        <div class="solution-box">
            <div class="solution-title">
                <i class="fas fa-lightbulb"></i>
                Solution
            </div>
            <p class="solution-text">
                Please use the HTTP version of the URL instead:
            </p>
            <div class="url-box" id="http-url">
                http://{{ request.host }}{{ request.path }}
            </div>
        </div>

        <div class="action-buttons">
            <a href="http://{{ request.host }}{{ request.path }}" class="btn btn-primary">
                <i class="fas fa-external-link-alt"></i>
                Go to HTTP Version
            </a>
            <button onclick="copyUrl()" class="btn btn-secondary">
                <i class="fas fa-copy"></i>
                Copy HTTP URL
            </button>
        </div>

        <div class="technical-details">
            <strong>Technical Details:</strong><br>
            This is a development server running on HTTP (port 8080). 
            SSL/TLS encryption is not configured for development environments.
            In production, HTTPS will be properly configured with valid SSL certificates.
        </div>
    </div>

    <!-- Theme Manager JavaScript -->
    <script src="{{ url_for('static', filename='js/theme-manager.js') }}"></script>
    
    <script>
        function copyUrl() {
            const url = document.getElementById('http-url').textContent;
            navigator.clipboard.writeText(url).then(() => {
                const btn = event.target.closest('.btn');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                btn.style.background = 'var(--success-color)';
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.style.background = '';
                }, 2000);
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = url;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                const btn = event.target.closest('.btn');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                btn.style.background = 'var(--success-color)';
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.style.background = '';
                }, 2000);
            });
        }

        // Auto-redirect after 10 seconds
        let countdown = 10;
        const redirectTimer = setInterval(() => {
            countdown--;
            if (countdown <= 0) {
                clearInterval(redirectTimer);
                window.location.href = `http://${window.location.host}${window.location.pathname}`;
            }
        }, 1000);
    </script>
</body>
</html>
