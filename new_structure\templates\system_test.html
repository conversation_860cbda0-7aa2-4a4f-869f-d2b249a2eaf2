<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>System Test - Hillview School</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background: #f5f5f5;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .status {
        padding: 10px;
        margin: 10px 0;
        border-radius: 4px;
      }
      .success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
      .warning {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
      }
      .test-button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
      }
      .test-button:hover {
        background: #0056b3;
      }
      #results {
        margin-top: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 4px;
        font-family: monospace;
        white-space: pre-wrap;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Hillview School System Test</h1>

      <div class="status success">
        <strong>✅ System Status:</strong> Online and Running
      </div>

      <div class="status warning">
        <strong>⚠️ Performance Notice:</strong> If you're experiencing slow
        loading, this page will help diagnose the issue.
      </div>

      <h2>Quick Tests</h2>
      <button class="test-button" onclick="testLogin()">Test Login Page</button>
      <button class="test-button" onclick="testDatabase()">
        Test Database
      </button>
      <button class="test-button" onclick="testPerformance()">
        Test Performance
      </button>
      <button class="test-button" onclick="clearCache()">Clear Cache</button>

      <h2>Navigation</h2>
      <p>
        <a href="/classteacher_login">Class Teacher Login</a> |
        <a href="/admin_login">Admin Login</a> |
        <a href="/debug/performance">Performance Debug</a>
      </p>

      <h2>Test Results</h2>
      <div id="results">Click a test button to see results...</div>

      <h2>Troubleshooting</h2>
      <div class="status">
        <strong>If login button doesn't work:</strong>
        <ul>
          <li>Clear browser cache (Ctrl+Shift+Delete)</li>
          <li>Disable browser extensions</li>
          <li>Try incognito/private browsing mode</li>
          <li>Check browser console for errors (F12)</li>
        </ul>
      </div>

      <div class="status">
        <strong>If page loads slowly:</strong>
        <ul>
          <li>Check network connection</li>
          <li>Restart the Flask server</li>
          <li>Check system resources (CPU/Memory)</li>
          <li>Try the performance debug page</li>
        </ul>
      </div>
    </div>

    <script>
      function testLogin() {
        updateResults("Testing login page...");

        fetch("/classteacher_login")
          .then((response) => {
            if (response.ok) {
              updateResults("✅ Login page: OK - Page loads successfully");
            } else {
              updateResults(
                "❌ Login page: ERROR - Status: " + response.status
              );
            }
          })
          .catch((error) => {
            updateResults("❌ Login page: ERROR - " + error.message);
          });
      }

      function testDatabase() {
        updateResults("Testing database connection...");

        // This would need a proper endpoint, for now just show a message
        updateResults("ℹ️ Database test would require a proper API endpoint");
      }

      function testPerformance() {
        updateResults("Testing performance...");

        const startTime = performance.now();

        fetch("/debug/performance")
          .then((response) => {
            const endTime = performance.now();
            const duration = endTime - startTime;

            if (response.ok) {
              updateResults(
                `✅ Performance: OK - Response time: ${duration.toFixed(2)}ms`
              );
            } else {
              updateResults(
                `❌ Performance: ERROR - Status: ${response.status}`
              );
            }
          })
          .catch((error) => {
            updateResults("❌ Performance: ERROR - " + error.message);
          });
      }

      function clearCache() {
        updateResults("Clearing cache...");

        fetch("/debug/clear-cache")
          .then((response) => response.text())
          .then((text) => {
            updateResults("✅ Cache: " + text);
          })
          .catch((error) => {
            updateResults("❌ Cache: ERROR - " + error.message);
          });
      }

      function updateResults(text) {
        const results = document.getElementById("results");
        const timestamp = new Date().toLocaleTimeString();
        results.textContent = `[${timestamp}] ${text}`;
      }

      // Auto-test on page load
      window.addEventListener("load", function () {
        updateResults(
          "System test page loaded successfully. Ready for testing."
        );
      });
    </script>
  </body>
</html>
