<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Awesome Test</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          rel="stylesheet"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
          crossorigin="anonymous"
          referrerpolicy="no-referrer">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 8px;
        }
        .icon-test {
            font-size: 24px;
            margin-right: 10px;
            color: #007bff;
        }
        .icon-test.large {
            font-size: 48px;
        }
        .test-item {
            margin-bottom: 10px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            margin-left: 10px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Font Awesome Icon Test</h1>
    
    <div class="test-section">
        <h2>Basic Icons Test</h2>
        <div class="test-item">
            <i class="fas fa-bars icon-test"></i>
            Hamburger Menu Icon (fas fa-bars)
        </div>
        <div class="test-item">
            <i class="fas fa-times icon-test"></i>
            Close Icon (fas fa-times)
        </div>
        <div class="test-item">
            <i class="fas fa-home icon-test"></i>
            Home Icon (fas fa-home)
        </div>
        <div class="test-item">
            <i class="fas fa-user icon-test"></i>
            User Icon (fas fa-user)
        </div>
        <div class="test-item">
            <i class="fas fa-cog icon-test"></i>
            Settings Icon (fas fa-cog)
        </div>
    </div>

    <div class="test-section">
        <h2>Navigation Icons Test</h2>
        <div class="test-item">
            <i class="fas fa-upload icon-test"></i>
            Upload Icon (fas fa-upload)
        </div>
        <div class="test-item">
            <i class="fas fa-chart-bar icon-test"></i>
            Chart Icon (fas fa-chart-bar)
        </div>
        <div class="test-item">
            <i class="fas fa-file-alt icon-test"></i>
            File Icon (fas fa-file-alt)
        </div>
        <div class="test-item">
            <i class="fas fa-users icon-test"></i>
            Users Icon (fas fa-users)
        </div>
    </div>

    <div class="test-section">
        <h2>Large Icons Test</h2>
        <div class="test-item">
            <i class="fas fa-bars icon-test large"></i>
            Large Hamburger Menu
        </div>
        <div class="test-item">
            <i class="fas fa-times icon-test large"></i>
            Large Close Icon
        </div>
    </div>

    <div class="test-section">
        <h2>Font Awesome Status Check</h2>
        <div id="fa-status">Checking Font Awesome...</div>
    </div>

    <script>
        // Check if Font Awesome is loaded
        document.addEventListener('DOMContentLoaded', function() {
            const statusEl = document.getElementById('fa-status');
            
            // Test if Font Awesome CSS is loaded
            const testIcon = document.createElement('i');
            testIcon.className = 'fas fa-home';
            testIcon.style.visibility = 'hidden';
            testIcon.style.position = 'absolute';
            document.body.appendChild(testIcon);
            
            setTimeout(() => {
                const computedStyle = window.getComputedStyle(testIcon);
                const fontFamily = computedStyle.fontFamily;
                
                if (fontFamily && fontFamily.includes('Font Awesome')) {
                    statusEl.innerHTML = '<span class="status success">✓ Font Awesome is loaded correctly</span>';
                } else {
                    statusEl.innerHTML = '<span class="status error">✗ Font Awesome failed to load</span>';
                    statusEl.innerHTML += '<br><small>Font Family: ' + fontFamily + '</small>';
                }
                
                document.body.removeChild(testIcon);
            }, 1000);
        });
    </script>
</body>
</html>
