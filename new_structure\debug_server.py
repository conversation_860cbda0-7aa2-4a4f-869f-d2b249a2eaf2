#!/usr/bin/env python3
"""
EMERGENCY DEBUG SERVER - Minimal Flask App for Testing
This script runs a bare minimum server to test if the issue is in the code or environment.
"""

import os
import sys
from flask import Flask, render_template

# Simple Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'debug-key-12345'

@app.route('/')
def index():
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Emergency Debug - Hillview</title>
        <style>
            body { 
                background: #FAFAFA; 
                color: #263AD1; 
                font-family: Arial, sans-serif;
                padding: 20px;
            }
            .card { 
                background: white; 
                border: 1px solid #e8e8e8; 
                padding: 20px; 
                border-radius: 8px;
                max-width: 600px;
                margin: 0 auto;
            }
            .btn { 
                background: #F9CC48; 
                color: black; 
                padding: 10px 20px; 
                text-decoration: none; 
                border-radius: 4px;
                display: inline-block;
                margin: 10px 5px;
            }
            a { color: #17cfe0; }
            h1, h2 { color: #263AD1; }
            p { color: #484B6A; }
        </style>
    </head>
    <body>
        <div class="card">
            <h1>🚨 Emergency Debug Mode</h1>
            <p>This is a minimal Flask server to test if the segmentation fault is caused by:</p>
            <ul>
                <li>Python environment issues</li>
                <li>Flask configuration problems</li>
                <li>CSS/Template complexity</li>
            </ul>
            
            <h2>Your Custom Colors:</h2>
            <ul>
                <li><strong>Background:</strong> #FAFAFA (Light Gray)</li>
                <li><strong>Primary Text:</strong> #263AD1 (Blue)</li>
                <li><strong>Secondary Text:</strong> #484B6A (Dark Gray)</li>
                <li><strong>Accent/Button:</strong> #F9CC48 (Yellow)</li>
                <li><strong>Link Text:</strong> #17cfe0 (Cyan)</li>
            </ul>
            
            <div>
                <a href="/test" class="btn">Test Route</a>
                <a href="/minimal" class="btn">Minimal Dashboard</a>
            </div>
            
            <p><strong>If this page loads successfully, the issue is in the main application complexity.</strong></p>
        </div>
    </body>
    </html>
    '''

@app.route('/test')
def test():
    return '<h1>Test route working!</h1><a href="/">Back</a>'

@app.route('/minimal')
def minimal():
    try:
        return render_template('classteacher_minimal.html')
    except Exception as e:
        return f'<h1>Template Error:</h1><p>{str(e)}</p><a href="/">Back</a>'

if __name__ == '__main__':
    print("🚨 EMERGENCY DEBUG SERVER STARTING...")
    print("📍 URL: http://localhost:5000")
    print("💡 This minimal server will help identify the crash cause")
    
    try:
        app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)
    except Exception as e:
        print(f"❌ Server failed to start: {e}")
        input("Press Enter to continue...")
