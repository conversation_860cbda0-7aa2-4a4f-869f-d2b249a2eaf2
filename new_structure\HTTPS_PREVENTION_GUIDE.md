# 🚨 HTTPS Prevention Guide - Hillview School Management System

## ⚠️ **CRITICAL: Always Use HTTP, Never HTTPS**

This development server **ONLY** supports HTTP connections. Using HTTPS will result in SSL protocol errors.

---

## ✅ **Correct URLs to Use**

### Main Application
```
✅ CORRECT:   http://*************:8080
❌ WRONG:     https://*************:8080
```

### Admin Login
```
✅ CORRECT:   http://*************:8080/admin_login
❌ WRONG:     https://*************:8080/admin_login
```

### Class Teacher Login
```
✅ CORRECT:   http://*************:8080/classteacher_login
❌ WRONG:     https://*************:8080/classteacher_login
```

### Teacher Login
```
✅ CORRECT:   http://*************:8080/teacher_login
❌ WRONG:     https://*************:8080/teacher_login
```

---

## 🔧 **Automatic Fixes Implemented**

### 1. Server-Side HTTP Enforcement
- **Development Mode**: Automatically redirects HTT<PERSON> attempts to HTTP
- **Production Mode**: Will enforce HTTPS (when deployed)

### 2. Client-Side JavaScript Protection
- **Auto-detection**: Detects HTTPS and redirects to HTTP
- **Link Updates**: Converts any HTTPS links to HTTP automatically
- **Form Updates**: Ensures forms submit to HTTP endpoints

### 3. Enhanced Error Handling
- **User-friendly messages**: Clear error pages for SSL attempts
- **Auto-redirect**: Automatic redirection from HTTPS to HTTP

---

## 🛠️ **Manual Fixes if Issues Persist**

### Clear Browser Cache
1. **Chrome**: `Ctrl + Shift + Delete` → Clear "Cached images and files"
2. **Firefox**: `Ctrl + Shift + Delete` → Clear "Cache"
3. **Safari**: `Cmd + Option + E`
4. **Edge**: `Ctrl + Shift + Delete` → Clear "Cached data"

### Force HTTP in Browser
1. **Type HTTP explicitly**: Always type `http://` in address bar
2. **Disable HTTPS Everywhere**: Turn off browser extensions that force HTTPS
3. **Use Incognito/Private Mode**: Bypasses cached redirects

### Browser Settings
1. **Chrome**: Settings → Privacy → Site Settings → Remove cached HTTPS redirects
2. **Firefox**: Settings → Privacy → Clear Data → Cached Web Content
3. **Safari**: Develop → Empty Caches

---

## 🚨 **Common Error Messages and Solutions**

### "This site can't provide a secure connection"
**Cause**: Using HTTPS instead of HTTP  
**Solution**: Change `https://` to `http://` in URL

### "ERR_SSL_PROTOCOL_ERROR"
**Cause**: Browser trying SSL/TLS on HTTP server  
**Solution**: Use HTTP URL and clear browser cache

### "Invalid response" or "Connection timeout"
**Cause**: Wrong protocol or cached HTTPS redirect  
**Solution**: Force HTTP and clear cache

---

## 📱 **Mobile Device Instructions**

### For Mobile Browsers
1. **Clear browser data**: Settings → Privacy → Clear browsing data
2. **Use HTTP URL**: Type `http://*************:8080` manually
3. **Bookmark HTTP version**: Save correct URL to avoid retyping
4. **Try private/incognito mode**: Bypasses cached redirects

### For Mobile Apps
- Most mobile apps respect HTTP URLs correctly
- If issues persist, clear app cache/data

---

## 🔍 **Troubleshooting Steps**

### Step 1: Verify Server is Running
```bash
python run.py
```
Look for: `Running on http://*************:8080`

### Step 2: Test Local Access First
```
http://localhost:8080
```

### Step 3: Test Network Access
```
http://*************:8080
```

### Step 4: Check for SSL Errors in Terminal
- No SSL handshake errors should appear
- If you see `\x16\x03\x01` patterns, someone is using HTTPS

### Step 5: Use Network Test Script
```bash
python network_test.py
```

---

## 🛡️ **Prevention Strategies**

### 1. Bookmark Correct URLs
Save these bookmarks:
- `http://*************:8080` (Main)
- `http://*************:8080/admin_login` (Admin)
- `http://*************:8080/classteacher_login` (Class Teacher)

### 2. Browser Configuration
- Disable "HTTPS Everywhere" extensions for local development
- Add exception for `*************:8080` in security settings
- Use development browser profile with relaxed security

### 3. Network Configuration
- Ensure all devices use same WiFi network
- Check Windows Firewall allows port 8080
- Verify no proxy/VPN forcing HTTPS

---

## 🔧 **Developer Notes**

### Current Implementation
- **Development**: HTTP only (port 8080)
- **Production**: Will use HTTPS (when deployed)
- **Auto-redirect**: HTTPS → HTTP in development

### Files Modified
- `__init__.py`: Added HTTP enforcement
- `force-http.js`: Client-side HTTPS detection
- `ssl_handler.py`: Enhanced error handling
- Templates: Added force-http script

### Testing
- All templates now include HTTP enforcement script
- Server automatically redirects HTTPS to HTTP
- Enhanced error messages guide users to correct URL

---

## 📞 **Quick Reference**

### ✅ Always Use These URLs:
- **Main**: `http://*************:8080`
- **Admin**: `http://*************:8080/admin_login`
- **Local**: `http://localhost:8080`

### ❌ Never Use These URLs:
- **HTTPS Main**: `https://*************:8080`
- **HTTPS Admin**: `https://*************:8080/admin_login`
- **HTTPS Local**: `https://localhost:8080`

### 🆘 Emergency Fix:
```javascript
// Run in browser console
window.location.replace(window.location.href.replace('https://', 'http://'));
```

---

**Remember: HTTP for development, HTTPS for production!**
