/**
 * Modern Premium Login JavaScript
 * Enhanced functionality for login pages with premium UX
 */

class ModernLogin {
  constructor() {
    this.init();
  }

  init() {
    this.setupRoleTabs();
    this.setupFormValidation();
    this.setupLoadingStates();
    this.setupKeyboardNavigation();
    this.setupPasswordToggle();
    this.setupFormAnimations();
  }

  /**
   * Setup role tab switching functionality
   */
  setupRoleTabs() {
    const roleTabs = document.querySelectorAll('.role-tab');
    const forms = document.querySelectorAll('.login-form');

    roleTabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        e.preventDefault();
        
        // Remove active class from all tabs
        roleTabs.forEach(t => t.classList.remove('active'));
        
        // Add active class to clicked tab
        tab.classList.add('active');
        
        // Hide all forms
        forms.forEach(form => {
          form.style.display = 'none';
          form.classList.remove('active');
        });
        
        // Show corresponding form
        const targetRole = tab.dataset.role;
        const targetForm = document.getElementById(`${targetRole}-form`);
        if (targetForm) {
          targetForm.style.display = 'block';
          targetForm.classList.add('active');
          
          // Focus first input
          const firstInput = targetForm.querySelector('input[type="text"], input[type="email"]');
          if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
          }
        }
        
        // Update page title
        this.updatePageTitle(targetRole);
      });
    });
  }

  /**
   * Update page title based on selected role
   */
  updatePageTitle(role) {
    const titles = {
      'headteacher': 'Headteacher Login',
      'classteacher': 'Class Teacher Login',
      'teacher': 'Subject Teacher Login'
    };
    
    if (titles[role]) {
      document.title = `${titles[role]} - Hillview School`;
    }
  }

  /**
   * Setup real-time form validation
   */
  setupFormValidation() {
    const inputs = document.querySelectorAll('.form-input');
    
    inputs.forEach(input => {
      // Real-time validation on input
      input.addEventListener('input', (e) => {
        this.validateField(e.target);
      });
      
      // Validation on blur
      input.addEventListener('blur', (e) => {
        this.validateField(e.target);
      });
      
      // Clear validation on focus
      input.addEventListener('focus', (e) => {
        this.clearFieldValidation(e.target);
      });
    });
  }

  /**
   * Validate individual form field
   */
  validateField(field) {
    const value = field.value.trim();
    const fieldType = field.type;
    const fieldName = field.name;
    
    // Remove existing validation classes
    field.classList.remove('field-valid', 'field-invalid');
    
    let isValid = true;
    let errorMessage = '';
    
    // Required field validation
    if (field.hasAttribute('required') && !value) {
      isValid = false;
      errorMessage = 'This field is required';
    }
    
    // Email validation
    if (fieldType === 'email' && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid email address';
      }
    }
    
    // Username validation
    if (fieldName === 'username' && value) {
      if (value.length < 3) {
        isValid = false;
        errorMessage = 'Username must be at least 3 characters';
      }
    }
    
    // Password validation
    if (fieldType === 'password' && value) {
      if (value.length < 6) {
        isValid = false;
        errorMessage = 'Password must be at least 6 characters';
      }
    }
    
    // Apply validation styling
    if (value) { // Only show validation if field has content
      field.classList.add(isValid ? 'field-valid' : 'field-invalid');
      this.showFieldMessage(field, errorMessage, isValid);
    }
    
    return isValid;
  }

  /**
   * Clear field validation styling
   */
  clearFieldValidation(field) {
    field.classList.remove('field-valid', 'field-invalid');
    this.hideFieldMessage(field);
  }

  /**
   * Show field validation message
   */
  showFieldMessage(field, message, isValid) {
    let messageEl = field.parentNode.querySelector('.field-message');
    
    if (!messageEl) {
      messageEl = document.createElement('div');
      messageEl.className = 'field-message';
      field.parentNode.appendChild(messageEl);
    }
    
    messageEl.textContent = message;
    messageEl.className = `field-message ${isValid ? 'field-message-success' : 'field-message-error'}`;
  }

  /**
   * Hide field validation message
   */
  hideFieldMessage(field) {
    const messageEl = field.parentNode.querySelector('.field-message');
    if (messageEl) {
      messageEl.remove();
    }
  }

  /**
   * Setup loading states for form submission
   */
  setupLoadingStates() {
    const forms = document.querySelectorAll('.login-form');
    
    forms.forEach(form => {
      form.addEventListener('submit', (e) => {
        const submitBtn = form.querySelector('.btn-primary');
        if (submitBtn) {
          this.setLoadingState(submitBtn, true);
          
          // Validate all fields before submission
          const inputs = form.querySelectorAll('.form-input[required]');
          let allValid = true;
          
          inputs.forEach(input => {
            if (!this.validateField(input)) {
              allValid = false;
            }
          });
          
          if (!allValid) {
            e.preventDefault();
            this.setLoadingState(submitBtn, false);
            this.showAlert('Please correct the errors above', 'error');
            return;
          }
          
          // Reset loading state after 10 seconds (fallback)
          setTimeout(() => {
            this.setLoadingState(submitBtn, false);
          }, 10000);
        }
      });
    });
  }

  /**
   * Set button loading state
   */
  setLoadingState(button, isLoading) {
    if (isLoading) {
      button.classList.add('btn-loading');
      button.disabled = true;
      button.dataset.originalText = button.textContent;
      button.textContent = 'Signing in...';
    } else {
      button.classList.remove('btn-loading');
      button.disabled = false;
      button.textContent = button.dataset.originalText || 'Sign In';
    }
  }

  /**
   * Setup keyboard navigation
   */
  setupKeyboardNavigation() {
    // Tab navigation for role tabs
    const roleTabs = document.querySelectorAll('.role-tab');
    
    roleTabs.forEach((tab, index) => {
      tab.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
          e.preventDefault();
          const direction = e.key === 'ArrowLeft' ? -1 : 1;
          const nextIndex = (index + direction + roleTabs.length) % roleTabs.length;
          roleTabs[nextIndex].focus();
          roleTabs[nextIndex].click();
        }
      });
    });
    
    // Enter key to submit form
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && e.target.classList.contains('form-input')) {
        const form = e.target.closest('.login-form');
        if (form && form.style.display !== 'none') {
          const submitBtn = form.querySelector('.btn-primary');
          if (submitBtn && !submitBtn.disabled) {
            submitBtn.click();
          }
        }
      }
    });
  }

  /**
   * Setup password visibility toggle
   */
  setupPasswordToggle() {
    const passwordInputs = document.querySelectorAll('input[type="password"]');
    
    passwordInputs.forEach(input => {
      const toggleBtn = document.createElement('button');
      toggleBtn.type = 'button';
      toggleBtn.className = 'password-toggle';
      toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
      toggleBtn.setAttribute('aria-label', 'Toggle password visibility');
      
      // Insert toggle button
      input.parentNode.appendChild(toggleBtn);
      
      toggleBtn.addEventListener('click', () => {
        const isPassword = input.type === 'password';
        input.type = isPassword ? 'text' : 'password';
        toggleBtn.innerHTML = `<i class="fas fa-eye${isPassword ? '-slash' : ''}"></i>`;
        toggleBtn.setAttribute('aria-label', `${isPassword ? 'Hide' : 'Show'} password`);
      });
    });
  }

  /**
   * Setup form animations
   */
  setupFormAnimations() {
    // Animate form elements on page load
    const animatedElements = document.querySelectorAll('.form-group, .role-selector, .login-header');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, { threshold: 0.1 });
    
    animatedElements.forEach(el => observer.observe(el));
  }

  /**
   * Show alert message
   */
  showAlert(message, type = 'info') {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    // Create new alert
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    
    // Insert alert
    const loginCard = document.querySelector('.login-card');
    const firstFormGroup = loginCard.querySelector('.form-group');
    if (firstFormGroup) {
      loginCard.insertBefore(alert, firstFormGroup);
    } else {
      loginCard.appendChild(alert);
    }
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
      if (alert.parentNode) {
        alert.remove();
      }
    }, 5000);
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new ModernLogin();
});

// Export for use in other scripts
window.ModernLogin = ModernLogin;
