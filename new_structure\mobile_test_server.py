#!/usr/bin/env python3
"""
MO<PERSON><PERSON> TESTING SERVER - Fixed for mobile login issues
This script starts a minimal server optimized for mobile testing.
"""

import os
import sys

# Add the parent directory to the Python path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

try:
    # Import create_app from the new_structure package
    from new_structure import create_app

    print("🚀 MOBILE TESTING SERVER")
    print("📱 Optimized for mobile login testing")
    print("=" * 50)

    # Create the Flask application
    app = create_app('development')

    # Mobile testing routes
    @app.route('/mobile-test')
    def mobile_test():
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Mobile Test</title>
            <style>
                body { background: #FAFAFA; color: #263AD1; padding: 20px; text-align: center; }
                .btn { background: #F9CC48; color: black; padding: 15px 30px; border: none; border-radius: 8px; margin: 10px; }
            </style>
        </head>
        <body>
            <h1>📱 Mobile Testing Hub</h1>
            <div style="background: white; padding: 30px; border-radius: 15px; border: 2px solid #e8e8e8;">
                <h2>Test Links:</h2>
                <a href="/auth/classteacher_login" class="btn" style="display: block; text-decoration: none; margin: 10px 0;">
                    Class Teacher Login (Fixed)
                </a>
                <a href="/auth/mobile-login-test" class="btn" style="display: block; text-decoration: none; margin: 10px 0;">
                    Simple Login Test
                </a>
                <a href="/classteacher/minimal" class="btn" style="display: block; text-decoration: none; margin: 10px 0;">
                    Minimal Dashboard
                </a>
                <p style="color: #484B6A; margin-top: 20px;">
                    All forms are now optimized for mobile devices with proper touch handling.
                </p>
            </div>
        </body>
        </html>
        '''

    print("📍 URLs to test:")
    print("   • Main: http://localhost:5000")
    print("   • Mobile Hub: http://localhost:5000/mobile-test")
    print("   • Class Teacher Login: http://localhost:5000/auth/classteacher_login")
    print("   • Simple Test: http://localhost:5000/auth/mobile-login-test")
    print("")
    print("🔧 FIXES APPLIED:")
    print("   ✅ Mobile-optimized login form")
    print("   ✅ Touch-friendly inputs")
    print("   ✅ Improved form submission")
    print("   ✅ Your custom color scheme")
    print("   ✅ Timeout protection")
    print("")

    # Start the server with mobile-optimized settings
    app.run(
        debug=True,
        host='127.0.0.1',
        port=5000,
        use_reloader=False,  # Prevents crashes in MINGW64
        threaded=True,       # Better mobile performance
        extra_files=[]       # Don't watch files to prevent crashes
    )

except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Make sure you're in the correct directory and the virtual environment is activated.")
    input("Press Enter to exit...")
except Exception as e:
    print(f"❌ Server Error: {e}")
    print("Check the error above and try running debug_server.py instead.")
    input("Press Enter to exit...")
