// AUTO-LOAD DEBUG SCRIPT FOR MOBILE NAVIGATION
// This script will automatically run when the page loads to debug mobile navigation issues

document.addEventListener("DOMContentLoaded", function () {
  // Wait a moment for the page to fully load
  setTimeout(function () {
    if (window.innerWidth <= 768) {
      // Send initial debug info to terminal
      sendToTerminal(
        "🔧 AUTO-DEBUG: Mobile device detected, running diagnostic..."
      );

      // Load and run the debug script
      const script = document.createElement("script");
      script.src = "/static/mobile_nav_debug.js";
      script.onload = function () {
        sendToTerminal("✅ Debug script loaded and executed automatically");
      };
      script.onerror = function () {
        sendToTerminal("❌ Failed to load debug script");
      };
      document.head.appendChild(script);
    } else {
      sendToTerminal(
        "🔧 AUTO-DEBUG: Desktop device detected, no mobile debug needed"
      );
    }
  }, 1000);
});

// Function to manually trigger debug
function runMobileDebug() {
  sendToTerminal("🔧 MANUAL DEBUG TRIGGERED");

  // Check if navigation element exists
  const navElement = document.getElementById("classteacherNav");
  sendToTerminal("1. Navigation element found: " + !!navElement);

  if (!navElement) {
    sendToTerminal("❌ Navigation not found! Looking for alternatives...");
    const allNavs = document.querySelectorAll('[id*="nav"], [class*="nav"]');
    sendToTerminal('Found elements with "nav": ' + allNavs.length);
    allNavs.forEach((el, i) => {
      sendToTerminal(
        `  ${i + 1}. ${el.tagName} - ID: ${el.id} - Class: ${el.className}`
      );
    });
  } else {
    // Check navigation state
    sendToTerminal("2. Navigation current state:");
    sendToTerminal("   - Classes: " + navElement.className);
    sendToTerminal(
      "   - Display: " + window.getComputedStyle(navElement).display
    );
    sendToTerminal(
      "   - Visibility: " + window.getComputedStyle(navElement).visibility
    );
    sendToTerminal(
      "   - Opacity: " + window.getComputedStyle(navElement).opacity
    );

    // Check navigation links
    const links = navElement.querySelectorAll(".nav-link, .logout-btn");
    sendToTerminal("3. Navigation links found: " + links.length);

    links.forEach((link, i) => {
      const styles = window.getComputedStyle(link);
      sendToTerminal(`   Link ${i + 1}: "${link.textContent.trim()}"`);
      sendToTerminal(`     - Display: ${styles.display}`);
      sendToTerminal(`     - Visibility: ${styles.visibility}`);
      sendToTerminal(`     - Opacity: ${styles.opacity}`);
    });

    // Force show navigation for testing
    sendToTerminal("4. Force showing navigation...");
    navElement.classList.add("show");

    // Apply emergency styles
    navElement.style.cssText = `
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background: rgba(102, 126, 234, 0.98) !important;
            flex-direction: column !important;
            justify-content: flex-start !important;
            align-items: center !important;
            gap: 1rem !important;
            z-index: 9999 !important;
            padding: 3rem 2rem !important;
            overflow-y: auto !important;
            -webkit-overflow-scrolling: touch !important;
        `;

    // Force show all links
    links.forEach((link) => {
      link.style.cssText = `
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
                color: white !important;
                background: rgba(255, 255, 255, 0.15) !important;
                padding: 1.2rem 1.5rem !important;
                border-radius: 12px !important;
                text-decoration: none !important;
                font-size: 1.1rem !important;
                margin: 0.8rem 0 !important;
                border: 2px solid rgba(255, 255, 255, 0.3) !important;
                width: 100% !important;
                max-width: 280px !important;
                box-sizing: border-box !important;
                align-items: center !important;
                justify-content: center !important;
                pointer-events: auto !important;
                cursor: pointer !important;
                z-index: 10001 !important;
                position: relative !important;
                min-height: 54px !important;
            `;
    });

    sendToTerminal("✅ Navigation manually opened with emergency styles");
  }

  sendToTerminal("🔧 MANUAL DEBUG COMPLETED");
}

// Make the debug function globally available
window.runMobileDebug = runMobileDebug;
