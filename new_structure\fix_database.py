#!/usr/bin/env python3
"""
Database Migration Script for Hillview School Management System
This script fixes the missing revoked_at column in class_teacher_permissions table
"""

import pymysql
import os
from config import Config

def run_database_migration():
    """Run the database migration to fix the missing revoked_at column"""
    
    print("🔧 Starting Database Migration...")
    
    try:
        # Get database configuration
        config = Config()
        
        # Database connection parameters
        db_config = {
            'host': config.MYSQL_HOST,
            'user': config.MYSQL_USER,
            'password': config.MYSQL_PASSWORD,
            'database': config.MYSQL_DATABASE,
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }
        
        # Connect to database
        connection = pymysql.connect(**db_config)
        
        with connection.cursor() as cursor:
            print("📊 Connected to database successfully")
            
            # Check if the column already exists
            cursor.execute("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'class_teacher_permissions' 
                AND COLUMN_NAME = 'revoked_at'
                AND TABLE_SCHEMA = %s
            """, (config.MYSQL_DATABASE,))
            
            column_exists = cursor.fetchone()
            
            if column_exists:
                print("✅ Column 'revoked_at' already exists in class_teacher_permissions table")
            else:
                print("🔄 Adding missing 'revoked_at' column...")
                
                # Add the missing column
                cursor.execute("""
                    ALTER TABLE class_teacher_permissions 
                    ADD COLUMN revoked_at DATETIME NULL 
                    AFTER granted_at
                """)
                
                print("✅ Added 'revoked_at' column successfully")
            
            # Create index if it doesn't exist
            cursor.execute("""
                SELECT INDEX_NAME 
                FROM INFORMATION_SCHEMA.STATISTICS 
                WHERE TABLE_NAME = 'class_teacher_permissions' 
                AND INDEX_NAME = 'idx_class_teacher_permissions_revoked_at'
                AND TABLE_SCHEMA = %s
            """, (config.MYSQL_DATABASE,))
            
            index_exists = cursor.fetchone()
            
            if not index_exists:
                print("🔄 Creating index for revoked_at column...")
                cursor.execute("""
                    CREATE INDEX idx_class_teacher_permissions_revoked_at 
                    ON class_teacher_permissions(revoked_at)
                """)
                print("✅ Created index successfully")
            else:
                print("✅ Index already exists")
            
            # Commit the changes
            connection.commit()
            
            # Verify the table structure
            cursor.execute("DESCRIBE class_teacher_permissions")
            columns = cursor.fetchall()
            
            print("📋 Current table structure:")
            for column in columns:
                print(f"  - {column['Field']}: {column['Type']} {'NULL' if column['Null'] == 'YES' else 'NOT NULL'}")
            
            print("🎉 Database migration completed successfully!")
            
    except pymysql.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    finally:
        if 'connection' in locals():
            connection.close()
            print("📊 Database connection closed")
    
    return True

if __name__ == "__main__":
    success = run_database_migration()
    if success:
        print("\n✅ You can now restart your Flask application:")
        print("   python run.py")
    else:
        print("\n❌ Migration failed. Please check the error messages above.")
