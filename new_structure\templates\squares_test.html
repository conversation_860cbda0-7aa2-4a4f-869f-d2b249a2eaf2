<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Squares Issue Test - Hillview School</title>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          rel="stylesheet"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
          crossorigin="anonymous"
          referrerpolicy="no-referrer">
    
    <!-- Squares Issue Fix -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/fix_squares_issue.css') }}">
    
    <style>
        body {
            font-family: 'Inter', Arial, sans-serif;
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .icon-test {
            display: inline-block;
            margin: 0.5rem;
            padding: 0.5rem;
            background: #667eea;
            color: white;
            border-radius: 4px;
            font-size: 1.2rem;
        }
        
        .checkbox-test {
            display: flex;
            align-items: center;
            margin: 0.5rem 0;
            padding: 0.5rem;
            background: white;
            border-radius: 4px;
        }
        
        .checkbox-test label {
            margin-left: 0.5rem;
            cursor: pointer;
        }
        
        .status {
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-weight: bold;
            margin: 0.5rem 0;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Squares Issue Test Page</h1>
        <p>This page tests if the small squares issue has been fixed throughout the application.</p>
        
        <div class="test-section">
            <h3>1. Font Awesome Icons Test</h3>
            <p>These icons should display properly, not as squares:</p>
            
            <div class="icon-test"><i class="fas fa-home"></i> Home</div>
            <div class="icon-test"><i class="fas fa-user"></i> User</div>
            <div class="icon-test"><i class="fas fa-users"></i> Users</div>
            <div class="icon-test"><i class="fas fa-school"></i> School</div>
            <div class="icon-test"><i class="fas fa-book"></i> Book</div>
            <div class="icon-test"><i class="fas fa-chart-bar"></i> Chart</div>
            <div class="icon-test"><i class="fas fa-cog"></i> Settings</div>
            <div class="icon-test"><i class="fas fa-edit"></i> Edit</div>
            <div class="icon-test"><i class="fas fa-trash"></i> Delete</div>
            <div class="icon-test"><i class="fas fa-download"></i> Download</div>
            <div class="icon-test"><i class="fas fa-upload"></i> Upload</div>
            <div class="icon-test"><i class="fas fa-plus"></i> Add</div>
            <div class="icon-test"><i class="fas fa-minus"></i> Remove</div>
            <div class="icon-test"><i class="fas fa-times"></i> Close</div>
            <div class="icon-test"><i class="fas fa-check"></i> Check</div>
            <div class="icon-test"><i class="fas fa-bars"></i> Menu</div>
        </div>
        
        <div class="test-section">
            <h3>2. Checkbox Test</h3>
            <p>These checkboxes should display properly, not as squares:</p>
            
            <div class="checkbox-test">
                <input type="checkbox" id="test1" checked>
                <label for="test1">Checked checkbox (should show checkmark)</label>
            </div>
            
            <div class="checkbox-test">
                <input type="checkbox" id="test2">
                <label for="test2">Unchecked checkbox (should show empty box)</label>
            </div>
            
            <div class="checkbox-test">
                <input type="checkbox" id="test3" checked>
                <label for="test3">Another checked checkbox</label>
            </div>
        </div>
        
        <div class="test-section">
            <h3>3. Radio Button Test</h3>
            <p>These radio buttons should display properly, not as squares:</p>
            
            <div class="checkbox-test">
                <input type="radio" id="radio1" name="test-radio" checked>
                <label for="radio1">Selected radio button</label>
            </div>
            
            <div class="checkbox-test">
                <input type="radio" id="radio2" name="test-radio">
                <label for="radio2">Unselected radio button</label>
            </div>
            
            <div class="checkbox-test">
                <input type="radio" id="radio3" name="test-radio">
                <label for="radio3">Another unselected radio button</label>
            </div>
        </div>
        
        <div class="test-section">
            <h3>4. Mobile Navigation Test</h3>
            <p>Mobile navigation toggle (resize window to see):</p>
            
            <button class="mobile-nav-toggle" style="display: block; background: #667eea; color: white; border: none; padding: 1rem; border-radius: 4px; cursor: pointer;">
                <i class="fas fa-bars"></i> Mobile Menu Toggle
            </button>
        </div>
        
        <div class="test-section">
            <h3>5. Status Check</h3>
            <div id="status-container">
                <div class="status success">✅ Squares fix CSS loaded successfully</div>
                <div class="status success">✅ Squares fix JavaScript loaded successfully</div>
                <div id="font-awesome-status" class="status">⏳ Checking Font Awesome...</div>
                <div id="icons-status" class="status">⏳ Checking icons...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>6. Manual Fix Button</h3>
            <p>If you still see squares, click this button to manually apply fixes:</p>
            <button onclick="fixSquares()" style="background: #667eea; color: white; border: none; padding: 1rem 2rem; border-radius: 4px; cursor: pointer; font-size: 1rem;">
                🔧 Fix Squares Manually
            </button>
        </div>
    </div>
    
    <!-- Squares Issue Fix Script -->
    <script src="{{ url_for('static', filename='js/fix_squares_issue.js') }}"></script>
    
    <script>
        // Additional status checking
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                // Check Font Awesome status
                const fontAwesomeStatus = document.getElementById('font-awesome-status');
                const iconsStatus = document.getElementById('icons-status');
                
                // Test if Font Awesome is working
                const testIcon = document.createElement('i');
                testIcon.className = 'fas fa-home';
                testIcon.style.position = 'absolute';
                testIcon.style.left = '-9999px';
                document.body.appendChild(testIcon);
                
                const computedStyle = window.getComputedStyle(testIcon, ':before');
                const content = computedStyle.getPropertyValue('content');
                
                document.body.removeChild(testIcon);
                
                if (content && content !== 'none' && content !== '""') {
                    fontAwesomeStatus.className = 'status success';
                    fontAwesomeStatus.textContent = '✅ Font Awesome is working correctly';
                } else {
                    fontAwesomeStatus.className = 'status error';
                    fontAwesomeStatus.textContent = '❌ Font Awesome not loading properly';
                }
                
                // Check for squares in icons
                const icons = document.querySelectorAll('.fa, .fas, .far, .fab');
                let hasSquares = false;
                
                icons.forEach(icon => {
                    if (icon.textContent === '□' || icon.textContent === '') {
                        hasSquares = true;
                    }
                });
                
                if (!hasSquares) {
                    iconsStatus.className = 'status success';
                    iconsStatus.textContent = '✅ No icon squares detected';
                } else {
                    iconsStatus.className = 'status error';
                    iconsStatus.textContent = '❌ Some icons showing as squares';
                }
            }, 1000);
        });
    </script>
</body>
</html>
