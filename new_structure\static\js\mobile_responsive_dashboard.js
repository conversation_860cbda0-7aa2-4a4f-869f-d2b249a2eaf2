/**
 * Mobile Responsive Dashboard JavaScript
 * Enhanced mobile functionality for Subject Teacher page
 */

(function() {
    'use strict';
    
    // Mobile Detection
    const isMobile = window.innerWidth <= 768;
    const isTouch = 'ontouchstart' in window;
    
    // Initialize mobile enhancements when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        initMobileEnhancements();
        initTouchEnhancements();
        initFormEnhancements();
        initTableEnhancements();
        initNavigationEnhancements();
    });
    
    /**
     * Initialize general mobile enhancements
     */
    function initMobileEnhancements() {
        // Add mobile class to body
        if (isMobile) {
            document.body.classList.add('mobile-device');
        }
        
        // Prevent zoom on double tap for iOS
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
        
        // Handle orientation changes
        window.addEventListener('orientationchange', function() {
            setTimeout(function() {
                // Recalculate layouts after orientation change
                window.scrollTo(0, 0);
                adjustForOrientation();
            }, 100);
        });
    }
    
    /**
     * Initialize touch enhancements
     */
    function initTouchEnhancements() {
        if (!isTouch) return;
        
        // Add touch feedback to buttons
        const buttons = document.querySelectorAll('button, .btn, input[type="submit"]');
        buttons.forEach(button => {
            button.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
                this.style.opacity = '0.8';
            });
            
            button.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                    this.style.opacity = '1';
                }, 100);
            });
        });
        
        // Improve select dropdown touch handling
        const selects = document.querySelectorAll('select');
        selects.forEach(select => {
            select.addEventListener('touchstart', function() {
                this.style.fontSize = '16px'; // Prevent zoom on iOS
            });
        });
    }
    
    /**
     * Initialize form enhancements
     */
    function initFormEnhancements() {
        // Auto-resize textareas
        const textareas = document.querySelectorAll('textarea');
        textareas.forEach(textarea => {
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
            });
        });
        
        // Improve form validation display
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                const invalidFields = form.querySelectorAll(':invalid');
                if (invalidFields.length > 0) {
                    e.preventDefault();
                    
                    // Scroll to first invalid field
                    invalidFields[0].scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                    
                    // Focus the field
                    setTimeout(() => {
                        invalidFields[0].focus();
                    }, 300);
                    
                    // Show mobile-friendly error message
                    showMobileAlert('Please fill in all required fields', 'error');
                }
            });
        });
        
        // Improve number input handling
        const numberInputs = document.querySelectorAll('input[type="number"]');
        numberInputs.forEach(input => {
            // Add mobile-friendly number pad
            input.setAttribute('inputmode', 'numeric');
            input.setAttribute('pattern', '[0-9]*');
        });
    }
    
    /**
     * Initialize table enhancements
     */
    function initTableEnhancements() {
        const tables = document.querySelectorAll('table');
        tables.forEach(table => {
            // Wrap tables in responsive container if not already wrapped
            if (!table.parentElement.classList.contains('table-responsive')) {
                const wrapper = document.createElement('div');
                wrapper.className = 'table-responsive';
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            }
            
            // Add scroll indicators for mobile
            if (isMobile) {
                addScrollIndicators(table.parentElement);
            }
        });
    }
    
    /**
     * Initialize navigation enhancements
     */
    function initNavigationEnhancements() {
        // Improve navigation link spacing on mobile
        const navLinks = document.querySelector('.nav-links');
        if (navLinks && isMobile) {
            navLinks.style.flexWrap = 'wrap';
            navLinks.style.justifyContent = 'center';
            navLinks.style.gap = '0.5rem';
        }
        
        // Add smooth scrolling to anchor links
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        anchorLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }
    
    /**
     * Add scroll indicators to tables
     */
    function addScrollIndicators(container) {
        const table = container.querySelector('table');
        if (!table) return;
        
        // Create scroll indicators
        const leftIndicator = document.createElement('div');
        leftIndicator.className = 'scroll-indicator scroll-indicator-left';
        leftIndicator.innerHTML = '←';
        
        const rightIndicator = document.createElement('div');
        rightIndicator.className = 'scroll-indicator scroll-indicator-right';
        rightIndicator.innerHTML = '→';
        
        container.appendChild(leftIndicator);
        container.appendChild(rightIndicator);
        
        // Update indicators on scroll
        container.addEventListener('scroll', function() {
            const scrollLeft = this.scrollLeft;
            const scrollWidth = this.scrollWidth;
            const clientWidth = this.clientWidth;
            
            leftIndicator.style.opacity = scrollLeft > 0 ? '1' : '0';
            rightIndicator.style.opacity = scrollLeft < scrollWidth - clientWidth ? '1' : '0';
        });
        
        // Initial state
        const scrollWidth = container.scrollWidth;
        const clientWidth = container.clientWidth;
        rightIndicator.style.opacity = scrollWidth > clientWidth ? '1' : '0';
    }
    
    /**
     * Show mobile-friendly alert
     */
    function showMobileAlert(message, type = 'info') {
        // Remove existing alerts
        const existingAlerts = document.querySelectorAll('.mobile-alert');
        existingAlerts.forEach(alert => alert.remove());
        
        // Create new alert
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} mobile-alert`;
        alert.textContent = message;
        alert.style.position = 'fixed';
        alert.style.top = '20px';
        alert.style.left = '20px';
        alert.style.right = '20px';
        alert.style.zIndex = '9999';
        alert.style.borderRadius = '8px';
        alert.style.padding = '1rem';
        alert.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
        
        document.body.appendChild(alert);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
        
        // Allow manual dismissal
        alert.addEventListener('click', () => {
            alert.remove();
        });
    }
    
    /**
     * Adjust layout for orientation changes
     */
    function adjustForOrientation() {
        const isLandscape = window.orientation === 90 || window.orientation === -90;
        
        if (isLandscape) {
            document.body.classList.add('landscape');
            document.body.classList.remove('portrait');
        } else {
            document.body.classList.add('portrait');
            document.body.classList.remove('landscape');
        }
        
        // Adjust form layouts in landscape
        const formSections = document.querySelectorAll('.form-section');
        formSections.forEach(section => {
            if (isLandscape && window.innerWidth > 600) {
                section.style.display = 'flex';
                section.style.flexWrap = 'wrap';
                section.style.gap = '1rem';
                
                const formGroups = section.querySelectorAll('.form-group');
                formGroups.forEach(group => {
                    group.style.flex = '1 1 45%';
                    group.style.minWidth = '200px';
                });
            } else {
                section.style.display = 'block';
                const formGroups = section.querySelectorAll('.form-group');
                formGroups.forEach(group => {
                    group.style.flex = 'none';
                    group.style.minWidth = 'auto';
                });
            }
        });
    }
    
    /**
     * Utility function to debounce events
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Handle window resize with debouncing
    window.addEventListener('resize', debounce(function() {
        adjustForOrientation();
    }, 250));
    
    // Export functions for external use
    window.MobileDashboard = {
        showAlert: showMobileAlert,
        isMobile: isMobile,
        isTouch: isTouch
    };
    
})();
