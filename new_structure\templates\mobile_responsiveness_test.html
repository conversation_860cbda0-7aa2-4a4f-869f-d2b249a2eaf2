{% extends "modern_base.html" %}

{% block title %}Mobile Responsiveness Test - {{ super() }}{% endblock %}

{% block page_title %}Mobile Responsiveness Test{% endblock %}

{% block header_icon %}<i class="fas fa-mobile-alt"></i>{% endblock %}
{% block header_title %}Mobile Responsiveness & Theme Test{% endblock %}
{% block header_subtitle %}Testing responsive design across all breakpoints with light/dark themes{% endblock %}

{% block content %}
<div class="mobile-test-container">
  <!-- Device Simulation Section -->
  <div class="modern-card">
    <div class="card-header">
      <h2 class="card-title">
        <i class="fas fa-devices"></i>
        Device Simulation Test
      </h2>
    </div>
    
    <div class="card-content">
      <p class="text-secondary mb-4">
        This page tests mobile responsiveness across different device sizes. 
        Use browser dev tools to simulate different devices or resize your browser window.
      </p>
      
      <div class="device-breakpoints">
        <div class="breakpoint-info">
          <h4>Current Breakpoint: <span id="current-breakpoint">Desktop</span></h4>
          <p>Screen Width: <span id="screen-width">-</span>px</p>
          <p>Theme: <span id="current-theme">Light</span></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation Test -->
  <div class="modern-card">
    <div class="card-header">
      <h2 class="card-title">
        <i class="fas fa-bars"></i>
        Navigation Responsiveness
      </h2>
    </div>
    
    <div class="card-content">
      <div class="nav-test-grid">
        <div class="nav-test-item">
          <h4>Desktop Navigation</h4>
          <p>Full horizontal navigation bar with all items visible</p>
          <div class="test-status" data-breakpoint="desktop">✅ Active on Desktop</div>
        </div>
        
        <div class="nav-test-item">
          <h4>Tablet Navigation</h4>
          <p>Compact navigation with reduced spacing</p>
          <div class="test-status" data-breakpoint="tablet">📱 Active on Tablet</div>
        </div>
        
        <div class="nav-test-item">
          <h4>Mobile Navigation</h4>
          <p>Collapsible hamburger menu for mobile devices</p>
          <div class="test-status" data-breakpoint="mobile">📱 Active on Mobile</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Theme Toggle Test -->
  <div class="modern-card">
    <div class="card-header">
      <h2 class="card-title">
        <i class="fas fa-palette"></i>
        Theme Toggle Responsiveness
      </h2>
    </div>
    
    <div class="card-content">
      <div class="theme-test-grid">
        <div class="theme-test-item">
          <h4>Desktop Theme Toggle</h4>
          <p>Full size toggle with label</p>
          <div class="theme-demo desktop-theme">
            <div class="theme-toggle-container">
              <button class="theme-toggle" type="button" data-theme="light">
                <div class="theme-toggle-slider">
                  <i class="theme-toggle-icon fas fa-sun"></i>
                </div>
              </button>
              <span class="theme-toggle-label">Theme</span>
            </div>
          </div>
        </div>
        
        <div class="theme-test-item">
          <h4>Mobile Theme Toggle</h4>
          <p>Compact toggle without label</p>
          <div class="theme-demo mobile-theme">
            <div class="theme-toggle-container mobile-size">
              <button class="theme-toggle mobile-size" type="button" data-theme="light">
                <div class="theme-toggle-slider">
                  <i class="theme-toggle-icon fas fa-sun"></i>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Form Responsiveness Test -->
  <div class="modern-card">
    <div class="card-header">
      <h2 class="card-title">
        <i class="fas fa-wpforms"></i>
        Form Responsiveness
      </h2>
    </div>
    
    <div class="card-content">
      <form class="responsive-test-form">
        <div class="form-grid">
          <div class="form-group">
            <label class="form-label">Full Name</label>
            <input type="text" class="form-input" placeholder="Enter your full name">
          </div>
          
          <div class="form-group">
            <label class="form-label">Email</label>
            <input type="email" class="form-input" placeholder="Enter your email">
          </div>
          
          <div class="form-group">
            <label class="form-label">Grade</label>
            <select class="form-select">
              <option>Select Grade</option>
              <option>Grade 1</option>
              <option>Grade 2</option>
              <option>Grade 3</option>
            </select>
          </div>
          
          <div class="form-group">
            <label class="form-label">Message</label>
            <textarea class="form-textarea" rows="3" placeholder="Enter your message"></textarea>
          </div>
        </div>
        
        <div class="form-actions">
          <button type="submit" class="modern-btn btn-primary">Submit Form</button>
          <button type="reset" class="modern-btn btn-outline">Reset</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Touch Target Test -->
  <div class="modern-card">
    <div class="card-header">
      <h2 class="card-title">
        <i class="fas fa-hand-pointer"></i>
        Touch Target Test
      </h2>
    </div>
    
    <div class="card-content">
      <p class="text-secondary mb-4">
        All interactive elements should be at least 44px in height for optimal touch interaction.
      </p>
      
      <div class="touch-test-grid">
        <button class="touch-test-btn">Touch Test Button</button>
        <a href="#" class="touch-test-link">Touch Test Link</a>
        <div class="touch-test-card">Touch Test Card</div>
      </div>
    </div>
  </div>
</div>

<style>
/* Mobile Test Specific Styles */
.mobile-test-container {
  max-width: 100%;
}

.device-breakpoints {
  background: var(--bg-secondary);
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.breakpoint-info h4 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.nav-test-grid,
.theme-test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.nav-test-item,
.theme-test-item {
  background: var(--bg-secondary);
  padding: 1rem;
  border-radius: 8px;
  border: 2px solid var(--border-primary);
}

.test-status {
  padding: 0.5rem;
  border-radius: 4px;
  font-weight: 600;
  margin-top: 0.5rem;
}

.theme-demo {
  display: flex;
  justify-content: center;
  padding: 1rem;
  background: var(--bg-tertiary);
  border-radius: 8px;
  margin-top: 0.5rem;
}

.mobile-size .theme-toggle {
  width: 40px !important;
  height: 22px !important;
}

.mobile-size .theme-toggle-slider {
  width: 14px !important;
  height: 14px !important;
  font-size: 8px !important;
}

.mobile-size .theme-toggle-label {
  display: none !important;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.touch-test-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.touch-test-btn,
.touch-test-link,
.touch-test-card {
  min-height: 44px;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.touch-test-btn {
  background: var(--primary-color);
  color: var(--text-inverse);
  border: none;
  cursor: pointer;
}

.touch-test-link {
  background: var(--accent-color);
  color: var(--text-inverse);
}

.touch-test-card {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 2px solid var(--border-primary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .nav-test-grid,
  .theme-test-grid {
    grid-template-columns: 1fr;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .touch-test-grid {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .device-breakpoints {
    padding: 0.75rem;
  }
  
  .nav-test-item,
  .theme-test-item {
    padding: 0.75rem;
  }
}
</style>

<script>
// Mobile responsiveness detection
function updateBreakpointInfo() {
  const width = window.innerWidth;
  const breakpointElement = document.getElementById('current-breakpoint');
  const widthElement = document.getElementById('screen-width');
  const themeElement = document.getElementById('current-theme');
  
  if (breakpointElement) {
    let breakpoint = 'Desktop';
    if (width <= 360) breakpoint = 'Small Mobile';
    else if (width <= 480) breakpoint = 'Mobile';
    else if (width <= 768) breakpoint = 'Tablet';
    
    breakpointElement.textContent = breakpoint;
  }
  
  if (widthElement) {
    widthElement.textContent = width;
  }
  
  if (themeElement) {
    const theme = document.documentElement.getAttribute('data-theme') || 'light';
    themeElement.textContent = theme.charAt(0).toUpperCase() + theme.slice(1);
  }
}

// Update on load and resize
window.addEventListener('load', updateBreakpointInfo);
window.addEventListener('resize', updateBreakpointInfo);

// Listen for theme changes
window.addEventListener('themeChanged', updateBreakpointInfo);

// Touch feedback for test elements
document.addEventListener('DOMContentLoaded', function() {
  const touchElements = document.querySelectorAll('.touch-test-btn, .touch-test-link, .touch-test-card');
  
  touchElements.forEach(element => {
    element.addEventListener('touchstart', function() {
      this.style.transform = 'scale(0.95)';
    });
    
    element.addEventListener('touchend', function() {
      this.style.transform = 'scale(1)';
    });
  });
});
</script>
{% endblock %}
