/* ===================================================================
   HILLVIEW SCHOOL MANAGEMENT SYSTEM - MODERN PREMIUM LOGIN
   Modern, Mobile-First, Premium UI Design for Login Pages
   ================================================================== */

/* ===== CSS RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* ===== MODERN DESIGN TOKENS ===== */
:root {
  /* Typography */
  --font-display: "Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont,
    system-ui, sans-serif;
  --font-body: "Inter", system-ui, sans-serif;

  /* Type Scale */
  --text-xs: 0.75rem; /* 12px */
  --text-sm: 0.875rem; /* 14px */
  --text-base: 1rem; /* 16px */
  --text-lg: 1.125rem; /* 18px */
  --text-xl: 1.25rem; /* 20px */
  --text-2xl: 1.5rem; /* 24px */
  --text-3xl: 1.875rem; /* 30px */
  --text-4xl: 2.25rem; /* 36px */

  /* Spacing Scale */
  --space-1: 0.25rem; /* 4px */
  --space-2: 0.5rem; /* 8px */
  --space-3: 0.75rem; /* 12px */
  --space-4: 1rem; /* 16px */
  --space-5: 1.25rem; /* 20px */
  --space-6: 1.5rem; /* 24px */
  --space-8: 2rem; /* 32px */
  --space-10: 2.5rem; /* 40px */
  --space-12: 3rem; /* 48px */
  --space-16: 4rem; /* 64px */

  /* Modern Color Palette */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-900: #0c4a6e;

  --neutral-50: #f9fafb;
  --neutral-100: #f3f4f6;
  --neutral-200: #e5e7eb;
  --neutral-300: #d1d5db;
  --neutral-400: #9ca3af;
  --neutral-500: #6b7280;
  --neutral-600: #4b5563;
  --neutral-700: #374151;
  --neutral-800: #1f2937;
  --neutral-900: #111827;

  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;

  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Border Radius */
  --radius-sm: 0.375rem; /* 6px */
  --radius-md: 0.5rem; /* 8px */
  --radius-lg: 0.75rem; /* 12px */
  --radius-xl: 1rem; /* 16px */
  --radius-2xl: 1.5rem; /* 24px */
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== BODY & LAYOUT ===== */
body {
  font-family: var(--font-body);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== MAIN CONTAINER ===== */
.login-container {
  width: 100%;
  max-width: 28rem; /* 448px */
  margin: 0 auto;
}

/* ===== MODERN CARD DESIGN ===== */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  padding: var(--space-8);
  position: relative;
  overflow: hidden;
}

.login-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

/* ===== HEADER SECTION ===== */
.login-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.school-logo {
  width: 4rem;
  height: 4rem;
  margin: 0 auto var(--space-4);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-lg);
  object-fit: cover;
}

.login-title {
  font-family: var(--font-display);
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--neutral-900);
  margin-bottom: var(--space-2);
  letter-spacing: -0.025em;
}

.login-subtitle {
  font-size: var(--text-base);
  color: var(--neutral-600);
  font-weight: 400;
}

/* ===== ROLE SELECTOR ===== */
.role-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-3);
  margin-bottom: var(--space-6);
  padding: var(--space-1);
  background: var(--neutral-100);
  border-radius: var(--radius-lg);
}

.role-tab {
  padding: var(--space-3) var(--space-4);
  border: none;
  background: transparent;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--neutral-600);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: center;
}

.role-tab:hover {
  color: var(--neutral-900);
  background: rgba(255, 255, 255, 0.5);
}

.role-tab.active {
  background: white;
  color: var(--primary-600);
  box-shadow: var(--shadow-sm);
}

/* ===== FORM STYLES ===== */
.login-form {
  space-y: var(--space-6);
}

.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--neutral-700);
  margin-bottom: var(--space-2);
}

.form-input-group {
  position: relative;
}

.form-input {
  width: 100%;
  padding: var(--space-4) var(--space-4) var(--space-4) var(--space-12);
  border: 2px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  color: var(--neutral-900);
  background: white;
  transition: all var(--transition-fast);
  outline: none;
}

.form-input:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.form-input::placeholder {
  color: var(--neutral-400);
}

.form-icon {
  position: absolute;
  left: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--neutral-400);
  font-size: var(--text-lg);
  pointer-events: none;
  transition: color var(--transition-fast);
}

.form-input:focus + .form-icon {
  color: var(--primary-500);
}

/* ===== BUTTON STYLES ===== */
.btn-primary {
  width: 100%;
  padding: var(--space-4);
  background: linear-gradient(
    135deg,
    var(--primary-500) 0%,
    var(--primary-600) 100%
  );
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* ===== FOOTER LINKS ===== */
.login-footer {
  margin-top: var(--space-8);
  text-align: center;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--neutral-600);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: 500;
  transition: color var(--transition-fast);
}

.back-link:hover {
  color: var(--primary-600);
}

/* ===== ALERT STYLES ===== */
.alert {
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-6);
  font-size: var(--text-sm);
  font-weight: 500;
}

.alert-error {
  background: var(--error-50);
  color: var(--error-600);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.alert-success {
  background: var(--success-50);
  color: var(--success-600);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.alert-warning {
  background: var(--warning-50);
  color: var(--warning-600);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

/* ===== LOADING STATES ===== */
.btn-loading {
  position: relative;
  color: transparent;
}

.btn-loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* ===== MOBILE RESPONSIVENESS ===== */
@media (max-width: 640px) {
  body {
    padding: var(--space-4) var(--space-3);
  }

  .login-card {
    padding: var(--space-6);
  }

  .login-title {
    font-size: var(--text-2xl);
  }

  .role-selector {
    grid-template-columns: 1fr;
    gap: var(--space-2);
  }

  .role-tab {
    padding: var(--space-4);
    font-size: var(--text-base);
  }

  .form-input {
    padding: var(--space-5) var(--space-4) var(--space-5) var(--space-12);
    font-size: var(--text-lg);
  }

  .btn-primary {
    padding: var(--space-5);
    font-size: var(--text-lg);
  }
}

@media (max-width: 480px) {
  .login-container {
    max-width: 100%;
  }

  .login-card {
    padding: var(--space-5);
    margin: var(--space-2);
  }

  .school-logo {
    width: 3rem;
    height: 3rem;
  }

  .login-title {
    font-size: var(--text-xl);
  }

  .login-subtitle {
    font-size: var(--text-sm);
  }
}

/* ===== TABLET STYLES ===== */
@media (min-width: 641px) and (max-width: 1024px) {
  .login-container {
    max-width: 32rem;
  }

  .login-card {
    padding: var(--space-10);
  }
}

/* ===== DESKTOP ENHANCEMENTS ===== */
@media (min-width: 1025px) {
  .login-container {
    max-width: 28rem;
  }

  .login-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.25);
    transition: all var(--transition-normal);
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  .login-card {
    border: 2px solid var(--neutral-900);
    background: white;
  }

  .form-input {
    border: 2px solid var(--neutral-900);
  }

  .btn-primary {
    background: var(--neutral-900);
    border: 2px solid var(--neutral-900);
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  :root {
    --neutral-50: #111827;
    --neutral-100: #1f2937;
    --neutral-200: #374151;
    --neutral-300: #4b5563;
    --neutral-400: #6b7280;
    --neutral-500: #9ca3af;
    --neutral-600: #d1d5db;
    --neutral-700: #e5e7eb;
    --neutral-800: #f3f4f6;
    --neutral-900: #f9fafb;
  }

  body {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  }

  .login-card {
    background: rgba(31, 41, 55, 0.95);
    border: 1px solid rgba(75, 85, 99, 0.3);
  }

  .role-selector {
    background: var(--neutral-200);
  }

  .role-tab.active {
    background: var(--neutral-100);
  }

  .form-input {
    background: var(--neutral-100);
    border-color: var(--neutral-300);
    color: var(--neutral-900);
  }
}

/* ===== FOCUS VISIBLE IMPROVEMENTS ===== */
.role-tab:focus-visible,
.form-input:focus-visible,
.btn-primary:focus-visible,
.back-link:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* ===== PREMIUM ANIMATIONS ===== */
.login-card {
  animation: slideUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(2rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-group {
  animation: fadeIn 0.8s cubic-bezier(0.16, 1, 0.3, 1);
  animation-fill-mode: both;
}

.form-group:nth-child(1) {
  animation-delay: 0.1s;
}
.form-group:nth-child(2) {
  animation-delay: 0.2s;
}
.form-group:nth-child(3) {
  animation-delay: 0.3s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== FORM VALIDATION STYLES ===== */
.form-input.field-valid {
  border-color: var(--success-500);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.form-input.field-invalid {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.field-message {
  margin-top: var(--space-2);
  font-size: var(--text-xs);
  font-weight: 500;
}

.field-message-success {
  color: var(--success-600);
}

.field-message-error {
  color: var(--error-600);
}

/* ===== PASSWORD TOGGLE ===== */
.password-toggle {
  position: absolute;
  right: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--neutral-400);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-sm);
  transition: color var(--transition-fast);
}

.password-toggle:hover {
  color: var(--neutral-600);
}

.password-toggle:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* ===== ENHANCED FORM STATES ===== */
.login-form {
  display: none;
}

.login-form.active {
  display: block;
}

.login-form:first-of-type {
  display: block;
}

/* ===== ANIMATION CLASSES ===== */
.animate-in {
  animation: slideInUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(1.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== IMPROVED FOCUS STATES ===== */
.form-input:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
  transform: translateY(-1px);
}

/* ===== PREMIUM MICRO-INTERACTIONS ===== */
.role-tab {
  position: relative;
  overflow: hidden;
}

.role-tab::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.role-tab:hover::before {
  left: 100%;
}

.btn-primary {
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.6s;
}

.btn-primary:hover::before {
  left: 100%;
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */
@media (max-width: 640px) {
  .password-toggle {
    right: var(--space-3);
    padding: var(--space-3);
  }

  .field-message {
    font-size: var(--text-sm);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  body {
    background: white !important;
  }

  .login-card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }

  .btn-primary {
    background: #333 !important;
    color: white !important;
  }
}
