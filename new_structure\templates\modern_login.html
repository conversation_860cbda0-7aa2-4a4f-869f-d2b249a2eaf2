<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=yes, maximum-scale=5.0"
    />
    <meta
      name="description"
      content="Login to {{ school_info.school_name or 'Hillview School' }} Management System"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="theme-color" content="#0ea5e9" />

    <title>Login - {{ school_info.school_name or 'Hillview School' }}</title>

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" />

    <!-- Modern Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Icons -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
      integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <!-- Modern Premium Login Styles -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/modern_premium_login.css') }}"
    />

    <!-- Icon Fallback Styles -->
    <style>
      .fas,
      .far,
      .fab,
      .fa {
        font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands", sans-serif;
        font-weight: 900;
        font-style: normal;
        display: inline-block;
        text-rendering: auto;
        -webkit-font-smoothing: antialiased;
      }

      /* Fallback icons */
      .fa-user::before {
        content: "👤";
        font-family: sans-serif;
      }
      .fa-lock::before {
        content: "🔒";
        font-family: sans-serif;
      }
      .fa-sign-in-alt::before {
        content: "🚪";
        font-family: sans-serif;
      }
      .fa-arrow-left::before {
        content: "←";
        font-family: sans-serif;
      }
      .fa-chalkboard-teacher::before {
        content: "👩‍🏫";
        font-family: sans-serif;
      }
      .fa-user-tie::before {
        content: "👔";
        font-family: sans-serif;
      }
      .fa-graduation-cap::before {
        content: "🎓";
        font-family: sans-serif;
      }
      .fa-eye::before {
        content: "👁";
        font-family: sans-serif;
      }
      .fa-eye-slash::before {
        content: "🙈";
        font-family: sans-serif;
      }
    </style>
  </head>
  <body>
    <div class="login-container">
      <div class="login-card">
        <!-- Header Section -->
        <div class="login-header">
          {% if school_info and school_info.logo_path and school_info.logo_path
          != '/static/images/default_logo.png' %}
          <img
            src="{{ school_info.logo_path }}"
            alt="{{ school_info.school_name or 'School' }} Logo"
            class="school-logo"
          />
          {% else %}
          <!-- Use the known Hillview logo file -->
          <img
            src="{{ url_for('static', filename='uploads/logos/optimized_school_logo_1750595986_hvs.jpg') }}"
            alt="{{ school_info.school_name or 'Hillview School' }} Logo"
            class="school-logo"
            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
          />
          <!-- Fallback to letter icon if image fails to load -->
          <div
            class="school-logo"
            style="
              background: linear-gradient(135deg, #0ea5e9, #0284c7);
              display: none;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 1.5rem;
              font-weight: 700;
            "
          >
            {{ (school_info.school_name or 'Hillview School')[0] }}
          </div>
          {% endif %}

          <h1 class="login-title">
            {{ school_info.school_name or 'Hillview School' }}
          </h1>
          <p class="login-subtitle">
            Welcome back! Please sign in to your account.
          </p>
        </div>

        <!-- Role Selector -->
        <div class="role-selector">
          <button type="button" class="role-tab active" data-role="headteacher">
            <i class="fas fa-user-tie"></i>
            Headteacher
          </button>
          <button type="button" class="role-tab" data-role="classteacher">
            <i class="fas fa-chalkboard-teacher"></i>
            Class Teacher
          </button>
          <button type="button" class="role-tab" data-role="teacher">
            <i class="fas fa-graduation-cap"></i>
            Subject Teacher
          </button>
        </div>

        <!-- Error/Success Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %} {% if
        messages %} {% for category, message in messages %}
        <div
          class="alert alert-{{ 'error' if category == 'error' else 'success' if category == 'success' else 'warning' }}"
        >
          {{ message }}
        </div>
        {% endfor %} {% endif %} {% endwith %}

        <!-- Headteacher Login Form -->
        <form
          method="POST"
          action="{{ url_for('auth.admin_login') }}"
          class="login-form active"
          id="headteacher-form"
        >
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
          <div class="form-group">
            <label for="headteacher-username" class="form-label"
              >Username</label
            >
            <div class="form-input-group">
              <input
                type="text"
                id="headteacher-username"
                name="username"
                class="form-input"
                placeholder="Enter your username"
                required
                autocomplete="username"
              />
              <i class="fas fa-user form-icon"></i>
            </div>
          </div>

          <div class="form-group">
            <label for="headteacher-password" class="form-label"
              >Password</label
            >
            <div class="form-input-group">
              <input
                type="password"
                id="headteacher-password"
                name="password"
                class="form-input"
                placeholder="Enter your password"
                required
                autocomplete="current-password"
              />
              <i class="fas fa-lock form-icon"></i>
            </div>
          </div>

          <button type="submit" class="btn-primary">
            <i class="fas fa-sign-in-alt"></i>
            Sign In as Headteacher
          </button>
        </form>

        <!-- Class Teacher Login Form -->
        <form
          method="POST"
          action="{{ url_for('auth.classteacher_login') }}"
          class="login-form"
          id="classteacher-form"
        >
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
          <div class="form-group">
            <label for="classteacher-username" class="form-label"
              >Username</label
            >
            <div class="form-input-group">
              <input
                type="text"
                id="classteacher-username"
                name="username"
                class="form-input"
                placeholder="Enter your username"
                required
                autocomplete="username"
              />
              <i class="fas fa-user form-icon"></i>
            </div>
          </div>

          <div class="form-group">
            <label for="classteacher-password" class="form-label"
              >Password</label
            >
            <div class="form-input-group">
              <input
                type="password"
                id="classteacher-password"
                name="password"
                class="form-input"
                placeholder="Enter your password"
                required
                autocomplete="current-password"
              />
              <i class="fas fa-lock form-icon"></i>
            </div>
          </div>

          <button type="submit" class="btn-primary">
            <i class="fas fa-sign-in-alt"></i>
            Sign In as Class Teacher
          </button>
        </form>

        <!-- Subject Teacher Login Form -->
        <form
          method="POST"
          action="{{ url_for('auth.teacher_login') }}"
          class="login-form"
          id="teacher-form"
        >
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
          <div class="form-group">
            <label for="teacher-username" class="form-label">Username</label>
            <div class="form-input-group">
              <input
                type="text"
                id="teacher-username"
                name="username"
                class="form-input"
                placeholder="Enter your username"
                required
                autocomplete="username"
              />
              <i class="fas fa-user form-icon"></i>
            </div>
          </div>

          <div class="form-group">
            <label for="teacher-password" class="form-label">Password</label>
            <div class="form-input-group">
              <input
                type="password"
                id="teacher-password"
                name="password"
                class="form-input"
                placeholder="Enter your password"
                required
                autocomplete="current-password"
              />
              <i class="fas fa-lock form-icon"></i>
            </div>
          </div>

          <button type="submit" class="btn-primary">
            <i class="fas fa-sign-in-alt"></i>
            Sign In as Subject Teacher
          </button>
        </form>

        <!-- Footer -->
        <div class="login-footer">
          <a href="{{ url_for('auth.index') }}" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Back to Home
          </a>
        </div>
      </div>
    </div>

    <!-- Modern Premium Login JavaScript -->
    <script src="{{ url_for('static', filename='js/modern_premium_login.js') }}"></script>

    <!-- Additional JavaScript for enhanced functionality -->
    <script>
      // Auto-focus first input on page load
      document.addEventListener("DOMContentLoaded", function () {
        const firstInput = document.querySelector(
          ".login-form.active .form-input"
        );
        if (firstInput) {
          setTimeout(() => firstInput.focus(), 500);
        }
      });

      // Handle form submission errors
      window.addEventListener("load", function () {
        const urlParams = new URLSearchParams(window.location.search);
        const error = urlParams.get("error");

        if (error) {
          const modernLogin = new ModernLogin();
          let errorMessage = "Login failed. Please try again.";

          switch (error) {
            case "invalid_credentials":
              errorMessage = "Invalid username or password.";
              break;
            case "account_disabled":
              errorMessage = "Your account has been disabled.";
              break;
            case "too_many_attempts":
              errorMessage = "Too many login attempts. Please try again later.";
              break;
          }

          modernLogin.showAlert(errorMessage, "error");
        }
      });
    </script>
  </body>
</html>
