"""
Headteacher Universal Access Views - Provides headteacher with access to all classteacher functions
across all grades and streams with intelligent class structure detection.
"""
from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash
from ..services.headteacher_universal_service import HeadteacherUniversalService
from ..services.class_structure_service import ClassStructureService
from ..services.flexible_subject_service import FlexibleSubjectService
from ..services import is_authenticated, get_role
from ..models.academic import Stream
from functools import wraps

# Create blueprint
universal_bp = Blueprint('universal', __name__, url_prefix='/universal')

def headteacher_required(f):
    """Decorator to require headteacher authentication."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        print(f"=== HEADTEACHER_REQUIRED DEBUG ===")
        print(f"Function: {f.__name__}")
        print(f"Request path: {request.path}")
        print(f"Session teacher_id: {session.get('teacher_id')}")
        print(f"Session role: {session.get('role')}")
        print(f"Is authenticated: {is_authenticated(session)}")

        if not is_authenticated(session):
            print("❌ Authentication failed - redirecting to login")
            return redirect(url_for('auth.admin_login'))

        role = get_role(session)
        print(f"User role: {role}")

        if role != 'headteacher':
            print(f"❌ Role check failed - expected 'headteacher', got '{role}'")
            flash('Access denied. This feature is only available to headteachers.', 'error')
            return redirect(url_for('auth.admin_login'))

        print(f"✅ Headteacher access granted to function: {f.__name__}")
        return f(*args, **kwargs)
    return decorated_function



@universal_bp.route('/dashboard')
@headteacher_required
def dashboard():
    """Universal access dashboard - shows all grades and management functions."""
    try:
        # Set headteacher universal access flag
        session['headteacher_universal_access'] = True
        session.permanent = True  # Ensure session persists across redirects

        # Get comprehensive dashboard data
        dashboard_data = HeadteacherUniversalService.get_universal_dashboard_data()

        # Get school structure for class grid
        school_structure = ClassStructureService.get_school_structure()

        # Get class statistics
        class_statistics = ClassStructureService.get_class_statistics()

        # Prepare template data
        template_data = {
            'dashboard_data': {
                'class_statistics': class_statistics,
                'school_structure': school_structure,
                'recent_activity': dashboard_data.get('recent_activity', []),
                'performance_overview': dashboard_data.get('performance_overview', {}),
                'available_functions': dashboard_data.get('available_functions', [])
            },
            'page_title': 'Universal Class Management'
        }

        return render_template('headteacher_universal.html', **template_data)

    except Exception as e:
        print(f"Error loading universal dashboard: {e}")
        flash('Error loading universal dashboard. Redirecting to classteacher dashboard.', 'error')
        return redirect(url_for('classteacher.dashboard'))

@universal_bp.route('/api/school_structure')
@headteacher_required
def get_school_structure():
    """API endpoint to get school structure data."""
    try:
        structure = ClassStructureService.get_school_structure()
        return jsonify({'success': True, 'structure': structure})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@universal_bp.route('/api/class_selection')
@headteacher_required
def get_class_selection():
    """API endpoint to get all classes formatted for selection."""
    try:
        classes = ClassStructureService.get_all_classes_for_selection()
        return jsonify({'success': True, 'classes': classes})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@universal_bp.route('/api/class_data/<path:class_identifier>')
@headteacher_required
def get_class_data(class_identifier):
    """API endpoint to get comprehensive data for a specific class."""
    try:
        # Parse class identifier
        grade_name, stream_name = ClassStructureService.parse_class_identifier(class_identifier)
        
        # Get class data
        class_data = HeadteacherUniversalService.get_class_specific_data(grade_name, stream_name)
        
        if 'error' in class_data:
            return jsonify({'success': False, 'message': class_data['error']})
        
        return jsonify({'success': True, 'class_data': class_data})
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@universal_bp.route('/class_manager/<path:class_identifier>')
@headteacher_required
def class_manager(class_identifier):
    """Universal class manager for specific class."""
    try:
        # Parse class identifier
        grade_name, stream_name = ClassStructureService.parse_class_identifier(class_identifier)
        
        # Get class data
        class_data = HeadteacherUniversalService.get_class_specific_data(grade_name, stream_name)
        
        if 'error' in class_data:
            flash(f'Error: {class_data["error"]}', 'error')
            return redirect(url_for('universal.dashboard'))
        
        # Get school structure for navigation
        structure = ClassStructureService.get_school_structure()
        
        return render_template(
            'universal_class_manager.html',
            class_data=class_data,
            school_structure=structure,
            class_identifier=class_identifier,
            page_title=f"Managing {class_data['class_name']}"
        )
        
    except Exception as e:
        flash(f'Error loading class manager: {str(e)}', 'error')
        return redirect(url_for('universal.dashboard'))

@universal_bp.route('/api/switch_class', methods=['POST'])
@headteacher_required
def switch_class():
    """API endpoint to switch to a different class context."""
    try:
        data = request.get_json()
        class_identifier = data.get('class_identifier')
        
        if not class_identifier:
            return jsonify({'success': False, 'message': 'Class identifier required'})
        
        # Validate class exists
        grade_name, stream_name = ClassStructureService.parse_class_identifier(class_identifier)
        class_data = HeadteacherUniversalService.get_class_specific_data(grade_name, stream_name)
        
        if 'error' in class_data:
            return jsonify({'success': False, 'message': class_data['error']})
        
        # Store current class context in session
        session['universal_class_context'] = {
            'class_identifier': class_identifier,
            'grade_name': grade_name,
            'stream_name': stream_name,
            'class_name': class_data['class_name']
        }
        
        return jsonify({
            'success': True,
            'message': f'Switched to {class_data["class_name"]}',
            'redirect_url': url_for('universal.class_manager', class_identifier=class_identifier)
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@universal_bp.route('/api/class_statistics')
@headteacher_required
def get_class_statistics():
    """API endpoint to get comprehensive class statistics."""
    try:
        stats = ClassStructureService.get_class_statistics()
        return jsonify({'success': True, 'statistics': stats})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@universal_bp.route('/api/performance_overview')
@headteacher_required
def get_performance_overview():
    """API endpoint to get performance overview across all classes."""
    try:
        dashboard_data = HeadteacherUniversalService.get_universal_dashboard_data()
        performance = dashboard_data.get('performance_overview', {})
        return jsonify({'success': True, 'performance': performance})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@universal_bp.route('/api/recent_activity')
@headteacher_required
def get_recent_activity():
    """API endpoint to get recent activity across all classes."""
    try:
        dashboard_data = HeadteacherUniversalService.get_universal_dashboard_data()
        activity = dashboard_data.get('recent_activity', [])
        return jsonify({'success': True, 'activity': activity})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# ============================================================================
# COMPREHENSIVE PROXY ROUTES FOR ALL CLASSTEACHER FUNCTIONS
# ============================================================================

# STUDENT MANAGEMENT PROXIES
@universal_bp.route('/proxy/manage_students')
@headteacher_required
def proxy_manage_students():
    """Proxy to student management."""
    session['headteacher_universal_access'] = True
    return redirect(url_for('classteacher.manage_students'))

@universal_bp.route('/proxy/download_student_template')
@headteacher_required
def proxy_download_student_template():
    """Proxy to download student template."""
    session['headteacher_universal_access'] = True
    return redirect(url_for('classteacher.download_student_template'))

@universal_bp.route('/proxy/download_class_list')
@headteacher_required
def proxy_download_class_list():
    """Proxy to download class list."""
    session['headteacher_universal_access'] = True
    return redirect(url_for('classteacher.download_class_list'))

# SUBJECT MANAGEMENT PROXIES
@universal_bp.route('/proxy/manage_subjects')
@headteacher_required
def proxy_manage_subjects():
    """Proxy to subject management."""
    session['headteacher_universal_access'] = True
    return redirect(url_for('classteacher.manage_subjects'))

@universal_bp.route('/proxy/download_subject_template')
@headteacher_required
def proxy_download_subject_template():
    """Proxy to download subject template."""
    session['headteacher_universal_access'] = True
    return redirect(url_for('classteacher.download_subject_template'))

@universal_bp.route('/proxy/export_subjects')
@headteacher_required
def proxy_export_subjects():
    """Proxy to export subjects."""
    session['headteacher_universal_access'] = True
    return redirect(url_for('classteacher.export_subjects'))

# TEACHER MANAGEMENT PROXIES
@universal_bp.route('/proxy/teacher_management_hub')
@headteacher_required
def proxy_teacher_management_hub():
    """Proxy to teacher management hub."""
    session['headteacher_universal_access'] = True
    session.permanent = True  # Ensure session persists across redirects
    return redirect(url_for('classteacher.teacher_management_hub'))

@universal_bp.route('/proxy/manage_teachers')
@headteacher_required
def proxy_manage_teachers():
    """Proxy to teacher management."""
    session['headteacher_universal_access'] = True
    session.permanent = True  # Ensure session persists across redirects
    return redirect(url_for('classteacher.manage_teachers'))

@universal_bp.route('/proxy/assign_subjects')
@headteacher_required
def proxy_assign_subjects():
    """Proxy to subject assignment."""
    session['headteacher_universal_access'] = True
    session.permanent = True  # Ensure session persists across redirects
    return redirect(url_for('classteacher.assign_subjects'))

@universal_bp.route('/proxy/advanced_assignments')
@headteacher_required
def proxy_advanced_assignments():
    """Proxy to advanced assignments."""
    session['headteacher_universal_access'] = True
    return redirect(url_for('classteacher.advanced_assignments'))

# GRADE & STREAM MANAGEMENT PROXIES
@universal_bp.route('/proxy/manage_grades_streams')
@headteacher_required
def proxy_manage_grades_streams():
    """Proxy to grades and streams management."""
    session['headteacher_universal_access'] = True
    return redirect(url_for('classteacher.manage_grades_streams'))

# TERMS & ASSESSMENT MANAGEMENT PROXIES
@universal_bp.route('/proxy/manage_terms_assessments')
@headteacher_required
def proxy_manage_terms_assessments():
    """Proxy to terms and assessments management."""
    session['headteacher_universal_access'] = True
    return redirect(url_for('classteacher.manage_terms_assessments'))

# REPORT CONFIGURATION PROXIES
@universal_bp.route('/proxy/report_configuration')
@headteacher_required
def proxy_report_configuration():
    """Proxy to report configuration management."""
    session['headteacher_universal_access'] = True
    session.permanent = True  # Ensure session persists across redirects
    return redirect(url_for('classteacher.report_configuration'))

# MARKS MANAGEMENT PROXIES
@universal_bp.route('/proxy/classteacher_dashboard')
@headteacher_required
def proxy_classteacher_dashboard():
    """Proxy to classteacher dashboard for marks upload."""
    session['headteacher_universal_access'] = True
    return redirect(url_for('classteacher.dashboard'))



# DEBUG ROUTE FOR TESTING STREAMS API
@universal_bp.route('/debug/streams-test')
@headteacher_required
def debug_streams_test():
    """Debug page to test streams API endpoints."""
    return render_template('debug_streams_test.html')

@universal_bp.route('/debug/database-content')
@headteacher_required
def debug_database_content():
    """Debug route to check database content."""
    from ..models.academic import Grade, Stream, Student, Subject
    from ..models.user import Teacher

    # Get counts
    grades = Grade.query.all()
    streams = Stream.query.all()
    students = Student.query.all()
    subjects = Subject.query.all()
    teachers = Teacher.query.all()

    # Get class structure
    school_structure = ClassStructureService.get_school_structure()
    class_statistics = ClassStructureService.get_class_statistics()

    debug_info = {
        'database_counts': {
            'grades': len(grades),
            'streams': len(streams),
            'students': len(students),
            'subjects': len(subjects),
            'teachers': len(teachers)
        },
        'grades_list': [{'id': g.id, 'name': g.name} for g in grades],
        'streams_list': [{'id': s.id, 'name': s.name, 'grade_id': s.grade_id} for s in streams],
        'students_list': [{'id': st.id, 'name': st.name, 'stream_id': st.stream_id} for st in students[:10]],  # First 10
        'teachers_list': [{'id': t.id, 'username': t.username, 'role': t.role} for t in teachers],
        'school_structure': school_structure,
        'class_statistics': class_statistics
    }

    return jsonify(debug_info)

@universal_bp.route('/debug/initialize-database')
@headteacher_required
def debug_initialize_database():
    """Debug route to initialize database with sample data."""
    try:
        from ..utils.database_init import initialize_database_completely

        result = initialize_database_completely()

        return jsonify({
            'success': result.get('success', False),
            'message': result.get('message', 'Unknown result'),
            'details': result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@universal_bp.route('/debug/create-sample-data')
@headteacher_required
def debug_create_sample_data():
    """Debug route to create sample data manually."""
    try:
        from ..models.academic import Grade, Stream, Student, Subject, Term, AssessmentType
        from ..models.user import Teacher
        from ..extensions import db

        # Create grades if they don't exist
        grade_names = ['PP1', 'PP2', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9']
        grades_created = []

        for grade_name in grade_names:
            existing_grade = Grade.query.filter_by(name=grade_name).first()
            if not existing_grade:
                grade = Grade(name=grade_name)
                db.session.add(grade)
                grades_created.append(grade_name)

        db.session.commit()

        # Create streams for each grade
        streams_created = []
        grades = Grade.query.all()
        stream_names = ['A', 'B']

        for grade in grades:
            for stream_name in stream_names:
                existing_stream = Stream.query.filter_by(name=stream_name, grade_id=grade.id).first()
                if not existing_stream:
                    stream = Stream(name=stream_name, grade_id=grade.id)
                    db.session.add(stream)
                    streams_created.append(f"{grade.name} {stream_name}")

        db.session.commit()

        # Create some sample students
        students_created = []
        streams = Stream.query.all()

        for i, stream in enumerate(streams[:6]):  # Create students in first 6 streams
            for j in range(3):  # 3 students per stream
                student_name = f"Student {stream.grade.name} {stream.name} {j+1}"
                existing_student = Student.query.filter_by(name=student_name).first()
                if not existing_student:
                    student = Student(
                        name=student_name,
                        admission_number=f"ADM{i*3+j+1:03d}",
                        stream_id=stream.id,
                        gender='Male' if j % 2 == 0 else 'Female',
                        is_active=True
                    )
                    db.session.add(student)
                    students_created.append(student_name)

        db.session.commit()

        # Create basic subjects
        subjects_created = []
        subject_data = [
            {'name': 'English', 'education_level': 'primary'},
            {'name': 'Kiswahili', 'education_level': 'primary'},
            {'name': 'Mathematics', 'education_level': 'primary'},
            {'name': 'Science', 'education_level': 'primary'},
            {'name': 'Social Studies', 'education_level': 'primary'},
            {'name': 'English', 'education_level': 'junior_secondary'},
            {'name': 'Kiswahili', 'education_level': 'junior_secondary'},
            {'name': 'Mathematics', 'education_level': 'junior_secondary'},
            {'name': 'Science', 'education_level': 'junior_secondary'},
        ]

        for subj_data in subject_data:
            existing_subject = Subject.query.filter_by(
                name=subj_data['name'],
                education_level=subj_data['education_level']
            ).first()
            if not existing_subject:
                subject = Subject(**subj_data)
                db.session.add(subject)
                subjects_created.append(f"{subj_data['name']} ({subj_data['education_level']})")

        db.session.commit()

        # Create terms and assessment types
        terms_created = []
        assessments_created = []

        # Terms
        term_names = ['TERM 1', 'TERM 2', 'TERM 3']
        for term_name in term_names:
            existing_term = Term.query.filter_by(name=term_name).first()
            if not existing_term:
                term = Term(name=term_name, academic_year='2024')
                db.session.add(term)
                terms_created.append(term_name)

        # Assessment types
        assessment_names = ['CAT', 'End Term', 'KJSEA']
        for assessment_name in assessment_names:
            existing_assessment = AssessmentType.query.filter_by(name=assessment_name).first()
            if not existing_assessment:
                assessment = AssessmentType(name=assessment_name)
                db.session.add(assessment)
                assessments_created.append(assessment_name)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Sample data created successfully',
            'details': {
                'grades_created': grades_created,
                'streams_created': streams_created,
                'students_created': students_created,
                'subjects_created': subjects_created,
                'terms_created': terms_created,
                'assessments_created': assessments_created
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        })

# API ROUTES FOR HEADTEACHER ACCESS
@universal_bp.route('/api/streams/<int:grade_id>')
@headteacher_required
def get_streams_for_grade(grade_id):
    """API endpoint for headteachers to get streams for a grade."""
    try:
        streams = Stream.query.filter_by(grade_id=grade_id).all()
        return jsonify({
            'success': True,
            'streams': [{'id': stream.id, 'name': stream.name} for stream in streams]
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@universal_bp.route('/api/check-composite/<subject>/<education_level>')
@headteacher_required
def check_composite_subject(subject, education_level):
    """API endpoint for headteachers to check if a subject is composite."""
    try:
        # Get subject configuration
        config = FlexibleSubjectService.get_subject_configuration(subject, education_level)

        if config and config['is_composite']:
            # Get components
            components = FlexibleSubjectService.get_subject_components(subject, education_level)
            subject_type = FlexibleSubjectService.detect_subject_type(subject)

            return jsonify({
                'success': True,
                'is_composite': True,
                'subject_type': subject_type,
                'components': components,
                'config': config
            })

        return jsonify({
            'success': True,
            'is_composite': False,
            'subject_type': 'other',
            'components': [],
            'config': None
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@universal_bp.route('/proxy/download_marks_template')
@headteacher_required
def proxy_download_marks_template():
    """Proxy to download marks template."""
    session['headteacher_universal_access'] = True
    return redirect(url_for('classteacher.download_marks_template'))

@universal_bp.route('/proxy/collaborative_marks_dashboard')
@headteacher_required
def proxy_collaborative_marks_dashboard():
    """Proxy to collaborative marks dashboard."""
    session['headteacher_universal_access'] = True
    return redirect(url_for('classteacher.collaborative_marks_dashboard'))

# REPORT MANAGEMENT PROXIES
@universal_bp.route('/proxy/view_all_reports')
@headteacher_required
def proxy_view_all_reports():
    """Proxy to view all reports."""
    session['headteacher_universal_access'] = True
    return redirect(url_for('classteacher.view_all_reports'))

@universal_bp.route('/proxy/grade_reports_dashboard')
@headteacher_required
def proxy_grade_reports_dashboard():
    """Proxy to grade reports dashboard."""
    session['headteacher_universal_access'] = True
    return redirect(url_for('classteacher.grade_reports_dashboard'))

# STUDENT PROMOTION PROXY
@universal_bp.route('/proxy/student_promotion')
@headteacher_required
def proxy_student_promotion():
    """Proxy to student promotion management."""
    session['headteacher_universal_access'] = True
    session.permanent = True  # Ensure session persists across redirects
    return redirect(url_for('admin.student_promotion'))

# ASSIGNMENT MANAGEMENT PROXIES
@universal_bp.route('/proxy/manage_teacher_assignments')
@headteacher_required
def proxy_manage_teacher_assignments():
    """Proxy to teacher assignments management."""
    session['headteacher_universal_access'] = True
    return redirect(url_for('classteacher.manage_teacher_assignments'))

# CLASS-SPECIFIC PROXIES
@universal_bp.route('/proxy/students/<path:class_identifier>')
@headteacher_required
def proxy_students(class_identifier):
    """Proxy to student management for specific class."""
    try:
        # Set class context and redirect to classteacher student management
        grade_name, stream_name = ClassStructureService.parse_class_identifier(class_identifier)

        # Store context for the proxied request
        session['proxy_class_context'] = {
            'grade_name': grade_name,
            'stream_name': stream_name,
            'is_headteacher_proxy': True
        }
        session['headteacher_universal_access'] = True

        # Redirect to classteacher students view with context
        return redirect(url_for('classteacher.manage_students'))

    except Exception as e:
        flash(f'Error accessing student management: {str(e)}', 'error')
        return redirect(url_for('universal.dashboard'))

@universal_bp.route('/proxy/marks/<path:class_identifier>')
@headteacher_required
def proxy_marks(class_identifier):
    """Proxy to marks management for specific class."""
    try:
        # Set class context and redirect to classteacher marks management
        grade_name, stream_name = ClassStructureService.parse_class_identifier(class_identifier)

        # Store context for the proxied request
        session['proxy_class_context'] = {
            'grade_name': grade_name,
            'stream_name': stream_name,
            'is_headteacher_proxy': True
        }
        session['headteacher_universal_access'] = True

        # Redirect to classteacher marks view with context
        return redirect(url_for('classteacher.dashboard'))

    except Exception as e:
        flash(f'Error accessing marks management: {str(e)}', 'error')
        return redirect(url_for('universal.dashboard'))

@universal_bp.route('/proxy/reports/<path:class_identifier>')
@headteacher_required
def proxy_reports(class_identifier):
    """Proxy to reports generation for specific class."""
    try:
        # Set class context and redirect to classteacher reports
        grade_name, stream_name = ClassStructureService.parse_class_identifier(class_identifier)

        # Store context for the proxied request
        session['proxy_class_context'] = {
            'grade_name': grade_name,
            'stream_name': stream_name,
            'is_headteacher_proxy': True
        }
        session['headteacher_universal_access'] = True

        # Redirect to classteacher reports view with context
        return redirect(url_for('classteacher.view_all_reports'))

    except Exception as e:
        flash(f'Error accessing reports: {str(e)}', 'error')
        return redirect(url_for('universal.dashboard'))
