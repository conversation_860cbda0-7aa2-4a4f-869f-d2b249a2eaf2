<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Dropdown Test</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile_responsive_dashboard.css') }}">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .debug-info {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>Mobile Dropdown Test</h2>
        
        <div class="form-group">
            <label for="education_level">Education Level:</label>
            <select id="education_level" name="education_level">
                <option value="">Select Education Level</option>
                <option value="pre_primary">Pre-Primary (PP1-PP2)</option>
                <option value="lower_primary">Lower Primary (Grades 1-3)</option>
                <option value="upper_primary">Upper Primary (Grades 4-6)</option>
                <option value="junior_secondary">Junior Secondary (Grades 7-9)</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="grade">Grade:</label>
            <select id="grade" name="grade">
                <option value="">Select Grade</option>
                <option value="PP1">PP1</option>
                <option value="PP2">PP2</option>
                <option value="Grade 1">Grade 1</option>
                <option value="Grade 2">Grade 2</option>
                <option value="Grade 3">Grade 3</option>
                <option value="Grade 4">Grade 4</option>
                <option value="Grade 5">Grade 5</option>
                <option value="Grade 6">Grade 6</option>
                <option value="Grade 7">Grade 7</option>
                <option value="Grade 8">Grade 8</option>
                <option value="Grade 9">Grade 9</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="stream">Stream:</label>
            <select id="stream" name="stream">
                <option value="">Select Stream</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="subject">Subject:</label>
            <select id="subject" name="subject">
                <option value="">Select Subject</option>
                <option value="English">English</option>
                <option value="Mathematics">Mathematics</option>
                <option value="Science">Science</option>
                <option value="Social Studies">Social Studies</option>
                <option value="Kiswahili">Kiswahili</option>
            </select>
        </div>
        
        <button class="test-button" onclick="testDropdownRefresh()">Test Refresh</button>
        <button class="test-button" onclick="testGradeFilter()">Test Grade Filter</button>
        <button class="test-button" onclick="clearDebug()">Clear Debug</button>
        
        <div class="debug-info" id="debug-info">
            Debug information will appear here...
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/mobile_dropdown.js') }}"></script>
    <script>
        // Debug logging function
        function debugLog(message) {
            const debugDiv = document.getElementById('debug-info');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            debugDiv.scrollTop = debugDiv.scrollHeight;
            console.log(message);
        }
        
        // Clear debug info
        function clearDebug() {
            document.getElementById('debug-info').innerHTML = 'Debug cleared...<br>';
        }
        
        // Test dropdown refresh functionality
        function testDropdownRefresh() {
            debugLog('Testing dropdown refresh...');
            if (typeof window.refreshAllMobileDropdowns === 'function') {
                window.refreshAllMobileDropdowns();
                debugLog('✅ Refresh function called successfully');
            } else {
                debugLog('❌ Refresh function not available');
            }
        }
        
        // Test grade filtering functionality
        function testGradeFilter() {
            const educationLevel = document.getElementById('education_level').value;
            const gradeSelect = document.getElementById('grade');
            
            debugLog(`Testing grade filter for education level: ${educationLevel}`);
            
            if (educationLevel && typeof window.filterGradesByEducationLevel === 'function') {
                window.filterGradesByEducationLevel(educationLevel, gradeSelect);
                debugLog('✅ Grade filter function called');
            } else if (!educationLevel) {
                debugLog('❌ No education level selected');
            } else {
                debugLog('❌ Grade filter function not available');
            }
        }
        
        // Simulate the classteacher functions for testing
        window.originalGradeOptions = {};
        
        // Store original grade options
        function storeOriginalGradeOptions() {
            const gradeSelect = document.getElementById('grade');
            if (gradeSelect && !window.originalGradeOptions[gradeSelect.id]) {
                window.originalGradeOptions[gradeSelect.id] = Array.from(gradeSelect.options).map(opt => ({
                    value: opt.value,
                    textContent: opt.textContent
                }));
                debugLog(`Stored ${window.originalGradeOptions[gradeSelect.id].length} original grade options`);
            }
        }
        
        // Simplified grade filtering function for testing
        window.filterGradesByEducationLevel = function(educationLevel, gradeSelect) {
            debugLog(`Filtering grades for education level: ${educationLevel}`);
            
            const educationLevelGradeMapping = {
                'pre_primary': ['PP1', 'PP2'],
                'lower_primary': ['Grade 1', 'Grade 2', 'Grade 3'],
                'upper_primary': ['Grade 4', 'Grade 5', 'Grade 6'],
                'junior_secondary': ['Grade 7', 'Grade 8', 'Grade 9']
            };
            
            const gradesForLevel = educationLevelGradeMapping[educationLevel] || [];
            debugLog(`Grades for level: ${gradesForLevel.join(', ')}`);
            
            if (!window.originalGradeOptions[gradeSelect.id]) {
                storeOriginalGradeOptions();
            }
            
            const originalOptions = window.originalGradeOptions[gradeSelect.id];
            
            // Clear existing options
            gradeSelect.innerHTML = '<option value="">Select Grade</option>';
            
            // Add filtered options
            let optionsAdded = 0;
            originalOptions.forEach(originalOpt => {
                if (originalOpt.value === '') return;
                
                const shouldInclude = gradesForLevel.some(grade =>
                    originalOpt.value.trim().toLowerCase() === grade.trim().toLowerCase()
                );
                
                if (shouldInclude) {
                    const newOption = document.createElement('option');
                    newOption.value = originalOpt.value;
                    newOption.textContent = originalOpt.textContent;
                    gradeSelect.appendChild(newOption);
                    optionsAdded++;
                }
            });
            
            debugLog(`Added ${optionsAdded} filtered options`);
            
            // Refresh mobile dropdown if it exists
            if (typeof window.refreshMobileDropdown === 'function') {
                window.refreshMobileDropdown('grade');
                debugLog('✅ Mobile dropdown refreshed');
            } else {
                debugLog('❌ Mobile dropdown refresh function not available');
            }
        };
        
        // Add event listeners
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM loaded, initializing test page...');
            
            // Store original options
            storeOriginalGradeOptions();
            
            // Add change listeners
            document.getElementById('education_level').addEventListener('change', function() {
                debugLog(`Education level changed to: ${this.value}`);
                if (this.value) {
                    const gradeSelect = document.getElementById('grade');
                    window.filterGradesByEducationLevel(this.value, gradeSelect);
                }
            });
            
            document.getElementById('grade').addEventListener('change', function() {
                debugLog(`Grade changed to: ${this.value}`);
            });
            
            document.getElementById('stream').addEventListener('change', function() {
                debugLog(`Stream changed to: ${this.value}`);
            });
            
            document.getElementById('subject').addEventListener('change', function() {
                debugLog(`Subject changed to: ${this.value}`);
            });
            
            debugLog('✅ Test page initialized successfully');
        });
    </script>
</body>
</html>
