<svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="schoolGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="60" cy="60" r="58" fill="url(#schoolGradient)" stroke="white" stroke-width="4"/>
  
  <!-- School Building -->
  <g transform="translate(30, 35)">
    <!-- Main Building -->
    <rect x="10" y="25" width="40" height="30" fill="white" opacity="0.9"/>
    
    <!-- Roof -->
    <polygon points="5,25 30,10 55,25" fill="white" opacity="0.95"/>
    
    <!-- Door -->
    <rect x="25" y="40" width="10" height="15" fill="#667eea"/>
    
    <!-- Windows -->
    <rect x="15" y="32" width="6" height="6" fill="#667eea"/>
    <rect x="39" y="32" width="6" height="6" fill="#667eea"/>
    
    <!-- Flag Pole -->
    <line x1="55" y1="15" x2="55" y2="35" stroke="white" stroke-width="2"/>
    <rect x="55" y="15" width="8" height="5" fill="#ff6b6b"/>
  </g>
  
  <!-- School Text -->
  <text x="60" y="85" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">SCHOOL</text>
</svg>
