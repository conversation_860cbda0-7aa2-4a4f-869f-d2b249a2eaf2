"""
SSL/TLS Error Handler Middleware
Handles SSL handshake errors gracefully when running HTTP server
"""

import logging
from flask import Flask, request, jsonify
from werkzeug.exceptions import BadRequest

logger = logging.getLogger(__name__)

class SSLErrorHandler:
    """Middleware to handle SSL/TLS handshake errors gracefully."""
    
    def __init__(self, app: Flask = None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app: Flask):
        """Initialize the SSL error handler with the Flask app."""
        self.app = app
        
        # Register error handlers
        self._register_error_handlers(app)
        
        # Add request middleware
        self._register_middleware(app)
        
        # SSL Error Handler initialized silently
    
    def _register_error_handlers(self, app: Flask):
        """Register custom error handlers for SSL-related errors."""

        @app.errorhandler(400)
        def handle_bad_request(error):
            """Handle bad requests, including SSL handshake attempts."""

            # Check if this is an SSL handshake attempt
            if self._is_ssl_handshake_error(request):
                # Silently handle SSL attempts without logging

                # For SSL handshake attempts, return HTML redirect page
                html_response = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <title>HTTPS Not Supported - Hillview School</title>
                    <meta http-equiv="refresh" content="3;url=http://{request.host}{request.path}">
                    <style>
                        body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }}
                        .container {{ background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); max-width: 500px; margin: 0 auto; }}
                        .error-icon {{ font-size: 48px; color: #ff6b6b; margin-bottom: 20px; }}
                        h1 {{ color: #333; margin-bottom: 20px; }}
                        p {{ color: #666; margin-bottom: 20px; }}
                        .url {{ background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; word-break: break-all; }}
                        .btn {{ background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 20px; }}
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="error-icon">🔒</div>
                        <h1>HTTPS Not Supported</h1>
                        <p>This server only supports HTTP connections. You'll be automatically redirected to:</p>
                        <div class="url">http://{request.host}{request.path}</div>
                        <a href="http://{request.host}{request.path}" class="btn">Go to HTTP Version</a>
                        <p><small>Redirecting automatically in 3 seconds...</small></p>
                    </div>
                </body>
                </html>
                """
                return html_response, 400, {'Content-Type': 'text/html'}

            # Handle other bad requests normally
            return jsonify({
                'error': 'Bad Request',
                'message': 'The request could not be understood by the server'
            }), 400
    
    def _register_middleware(self, app: Flask):
        """Register middleware to detect and handle SSL attempts."""
        
        @app.before_request
        def detect_ssl_attempts():
            """Detect SSL handshake attempts before processing requests."""
            
            # Check for SSL handshake patterns in request data
            if hasattr(request, 'data') and request.data:
                if self._contains_ssl_handshake(request.data):
                    logger.warning(f"SSL handshake data detected from {request.remote_addr}")
                    # Let the error handler deal with it
                    return None
            
            # Check headers for SSL-related indicators
            if self._has_ssl_headers(request):
                logger.info(f"SSL-related headers detected from {request.remote_addr}")
            
            return None
    
    def _is_ssl_handshake_error(self, request):
        """Check if the error is likely due to an SSL handshake attempt."""
        
        # Check for common SSL handshake indicators
        ssl_indicators = [
            # TLS handshake starts with these bytes
            b'\x16\x03\x01',  # TLS 1.0
            b'\x16\x03\x02',  # TLS 1.1  
            b'\x16\x03\x03',  # TLS 1.2
            b'\x16\x03\x04',  # TLS 1.3
        ]
        
        # Check request data for SSL handshake patterns
        if hasattr(request, 'data') and request.data:
            for indicator in ssl_indicators:
                if request.data.startswith(indicator):
                    return True
        
        # Check if the request path or query suggests HTTPS
        if request.path.startswith('/https') or 'ssl' in request.args:
            return True
        
        return False
    
    def _contains_ssl_handshake(self, data):
        """Check if data contains SSL handshake patterns."""
        if not data:
            return False
        
        # TLS handshake record type (0x16) followed by version
        ssl_patterns = [
            b'\x16\x03\x01',  # TLS 1.0
            b'\x16\x03\x02',  # TLS 1.1
            b'\x16\x03\x03',  # TLS 1.2
            b'\x16\x03\x04',  # TLS 1.3
        ]
        
        for pattern in ssl_patterns:
            if data.startswith(pattern):
                return True
        
        return False
    
    def _has_ssl_headers(self, request):
        """Check if request has SSL-related headers."""
        ssl_headers = [
            'X-Forwarded-Proto',
            'X-Forwarded-SSL',
            'X-Forwarded-Scheme',
            'Front-End-Https'
        ]
        
        for header in ssl_headers:
            if header in request.headers:
                value = request.headers.get(header, '').lower()
                if value in ['https', 'ssl', 'on', '1', 'true']:
                    return True
        
        return False

def init_ssl_handler(app: Flask):
    """Initialize SSL error handler for the Flask app."""
    ssl_handler = SSLErrorHandler(app)
    return ssl_handler
