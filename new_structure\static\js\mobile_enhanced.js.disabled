/**
 * ENHANCED MOBILE NAVIGATION HANDLER
 * Fixes overlapping content, floating elements, and navigation issues
 */

(function () {
  "use strict";

  // Mobile Navigation Controller
  class MobileNavigationController {
    constructor() {
      this.navElement = document.getElementById("classteacherNav");
      this.toggleBtn = document.querySelector(".mobile-nav-toggle");
      this.toggleIcon = document.querySelector(".mobile-nav-toggle i");
      this.isOpen = false;
      this.init();
    }

    init() {
      if (!this.toggleBtn || !this.navElement) {
        console.warn("Mobile navigation elements not found");
        return;
      }

      this.setupEventListeners();
      this.setupMobileStyles();
      this.checkFontAwesome();

      console.log("Enhanced mobile navigation initialized");
    }

    setupEventListeners() {
      // Toggle button click
      this.toggleBtn.addEventListener("click", (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.toggleNavigation();
      });

      // Navigation link clicks
      this.navElement.addEventListener("click", (e) => {
        const link = e.target.closest("a");
        if (link) {
          const tabId = link.getAttribute("data-tab");
          if (tabId) {
            e.preventDefault();
            e.stopPropagation();

            // Check if switchMainTab function exists
            if (typeof window.switchMainTab === "function") {
              console.log(`Mobile nav: Switching to tab ${tabId}`);
              window.switchMainTab(tabId);
            } else {
              console.warn("switchMainTab function not found");
            }
          }

          // Close navigation after link click
          setTimeout(() => {
            this.closeNavigation();
          }, 300);
        }
      });

      // Close on outside click
      document.addEventListener("click", (e) => {
        if (
          this.isOpen &&
          !this.navElement.contains(e.target) &&
          !this.toggleBtn.contains(e.target)
        ) {
          this.closeNavigation();
        }
      });

      // Close on escape key
      document.addEventListener("keydown", (e) => {
        if (e.key === "Escape" && this.isOpen) {
          this.closeNavigation();
        }
      });

      // Handle window resize
      window.addEventListener("resize", () => {
        if (window.innerWidth > 768 && this.isOpen) {
          this.closeNavigation();
        }
      });

      // Handle orientation change
      window.addEventListener("orientationchange", () => {
        setTimeout(() => {
          if (this.isOpen) {
            this.adjustNavigationHeight();
          }
        }, 100);
      });
    }

    setupMobileStyles() {
      // Ensure mobile navigation is properly styled
      if (window.innerWidth <= 768) {
        this.navElement.style.display = "none";
        this.toggleBtn.style.display = "flex";
      } else {
        this.navElement.style.display = "flex";
        this.toggleBtn.style.display = "none";
      }
    }

    toggleNavigation() {
      if (this.isOpen) {
        this.closeNavigation();
      } else {
        this.openNavigation();
      }
    }

    openNavigation() {
      this.navElement.classList.add("mobile-nav-open");
      this.navElement.style.display = "flex";
      this.isOpen = true;

      // Update icon
      if (this.toggleIcon) {
        this.toggleIcon.className = "fas fa-times";
      }

      // Prevent body scroll
      document.body.classList.add("nav-open");
      document.body.style.overflow = "hidden";

      // Fix content positioning
      this.fixContentLayout();

      // Add ARIA attributes
      this.toggleBtn.setAttribute("aria-expanded", "true");
      this.navElement.setAttribute("aria-hidden", "false");

      console.log("Mobile navigation opened");
    }

    closeNavigation() {
      this.navElement.classList.remove("mobile-nav-open");
      this.navElement.style.display = "none";
      this.isOpen = false;

      // Update icon
      if (this.toggleIcon) {
        this.toggleIcon.className = "fas fa-bars";
      }

      // Restore body scroll
      document.body.classList.remove("nav-open");
      document.body.style.overflow = "";

      // Add ARIA attributes
      this.toggleBtn.setAttribute("aria-expanded", "false");
      this.navElement.setAttribute("aria-hidden", "true");

      console.log("Mobile navigation closed");
    }

    adjustNavigationHeight() {
      if (this.isOpen) {
        this.navElement.style.height = window.innerHeight + "px";
      }
    }

    fixContentLayout() {
      // Fix any floating elements
      const floatingElements = document.querySelectorAll(
        ".stat-number, .stat-value"
      );
      floatingElements.forEach((el) => {
        el.style.position = "relative";
        el.style.float = "none";
        el.style.display = "block";
      });

      // Fix grid layouts
      const gridElements = document.querySelectorAll(
        ".modern-grid, .dashboard-grid, .stats-grid"
      );
      gridElements.forEach((el) => {
        el.style.display = "grid";
        el.style.gridTemplateColumns = "1fr";
        el.style.gap = "1rem";
      });
    }

    checkFontAwesome() {
      // Check if Font Awesome is loaded
      setTimeout(() => {
        if (this.toggleIcon) {
          const computedStyle = window.getComputedStyle(this.toggleIcon);
          const fontFamily = computedStyle.getPropertyValue("font-family");

          if (
            !fontFamily ||
            !fontFamily.toLowerCase().includes("font awesome")
          ) {
            console.warn("Font Awesome not loaded, applying fallback");
            document.body.classList.add("no-fontawesome");

            // Apply Unicode fallback
            this.toggleIcon.style.fontFamily = "Arial, sans-serif";
            this.toggleIcon.textContent = "☰";
          }
        }
      }, 1000);
    }
  }

  // Content Layout Fixer
  class ContentLayoutFixer {
    constructor() {
      this.init();
    }

    init() {
      this.fixFloatingElements();
      this.fixOverflowingContent();
      this.setupResizeHandler();

      console.log("Content layout fixer initialized");
    }

    fixFloatingElements() {
      // Fix stat numbers and values that are floating
      const floatingElements = document.querySelectorAll(
        ".stat-number, .stat-value, .card-number, .metric-value"
      );

      floatingElements.forEach((el) => {
        el.style.position = "relative";
        el.style.float = "none";
        el.style.display = "block";
        el.style.textAlign = "center";
        el.style.margin = "0";
      });
    }

    fixOverflowingContent() {
      // Fix containers that overflow on mobile
      const containers = document.querySelectorAll(
        ".modern-container, .dashboard-container, .content-wrapper"
      );

      containers.forEach((container) => {
        container.style.padding = "1rem";
        container.style.margin = "0";
        container.style.width = "100%";
        container.style.boxSizing = "border-box";
      });

      // Fix cards and stat cards
      const cards = document.querySelectorAll(
        ".stat-card, .modern-card, .card"
      );
      cards.forEach((card) => {
        card.style.margin = "0 0 1rem 0";
        card.style.padding = "1rem";
        card.style.width = "100%";
        card.style.boxSizing = "border-box";
        card.style.position = "relative";
        card.style.float = "none";
      });
    }

    setupResizeHandler() {
      let resizeTimeout;
      window.addEventListener("resize", () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          this.fixFloatingElements();
          this.fixOverflowingContent();
        }, 300);
      });
    }
  }

  // Form Enhancement for Mobile
  class MobileFormEnhancer {
    constructor() {
      this.init();
    }

    init() {
      this.enhanceForms();
      this.enhanceTables();

      console.log("Mobile form enhancer initialized");
    }

    enhanceForms() {
      // Make form inputs touch-friendly
      const inputs = document.querySelectorAll("input, select, textarea");
      inputs.forEach((input) => {
        input.style.minHeight = "44px";
        input.style.fontSize = "16px";
        input.style.padding = "0.75rem";
        input.style.width = "100%";
        input.style.boxSizing = "border-box";
      });

      // Make buttons touch-friendly
      const buttons = document.querySelectorAll(".btn, .modern-btn, button");
      buttons.forEach((button) => {
        button.style.minHeight = "44px";
        button.style.padding = "0.75rem";
        button.style.fontSize = "16px";
        button.style.margin = "0.5rem 0";
        button.style.width = "100%";
        button.style.boxSizing = "border-box";
      });
    }

    enhanceTables() {
      // Make tables scrollable
      const tables = document.querySelectorAll("table");
      tables.forEach((table) => {
        const wrapper = document.createElement("div");
        wrapper.className = "table-wrapper";
        wrapper.style.overflowX = "auto";
        wrapper.style.webkitOverflowScrolling = "touch";
        wrapper.style.width = "100%";
        wrapper.style.margin = "1rem 0";

        table.parentNode.insertBefore(wrapper, table);
        wrapper.appendChild(table);

        table.style.minWidth = "600px";
        table.style.fontSize = "14px";
      });
    }
  }

  // Initialize all components when DOM is ready
  function initializeMobileEnhancements() {
    new MobileNavigationController();
    new ContentLayoutFixer();
    new MobileFormEnhancer();

    console.log("All mobile enhancements initialized");
  }

  // Initialize when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initializeMobileEnhancements);
  } else {
    initializeMobileEnhancements();
  }

  // Export for global access
  window.MobileEnhancements = {
    MobileNavigationController,
    ContentLayoutFixer,
    MobileFormEnhancer,
  };
})();
