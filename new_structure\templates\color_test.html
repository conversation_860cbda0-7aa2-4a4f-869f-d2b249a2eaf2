<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Light Mode Color Test - Hillview School</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- Custom Light Mode CSS -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/premium-color-palette.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/custom-light-mode.css') }}"
    />

    <!-- Font Awesome -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="container mt-4">
      <div class="row">
        <div class="col-12">
          <h1 class="mb-4">
            <i class="fas fa-palette"></i>
            Light Mode Color Test
          </h1>

          <div class="alert alert-info">
            <strong>Color Scheme Test:</strong> This page demonstrates your
            custom light mode colors.
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="card mb-4">
                <div class="card-header">
                  <h5 class="card-title mb-0">
                    <i class="fas fa-swatchbook"></i>
                    Color Palette
                  </h5>
                </div>
                <div class="card-body">
                  <p class="card-text">
                    This is primary text using your blue color (#263AD1)
                  </p>
                  <p class="text-secondary">
                    This is secondary text using your dark gray color (#484B6A)
                  </p>
                  <p class="text-custom-accent">
                    This is accent text using your cyan color (#17cfe0)
                  </p>
                  <p class="text-muted">
                    This is muted text for less important content
                  </p>

                  <div class="mt-3">
                    <h6>Background Colors:</h6>
                    <div class="p-3 bg-custom-primary border rounded">
                      Background: #FAFAFA (Light gray)
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="card mb-4">
                <div class="card-header">
                  <h5 class="card-title mb-0">
                    <i class="fas fa-mouse-pointer"></i>
                    Interactive Elements
                  </h5>
                </div>
                <div class="card-body">
                  <div class="mb-3">
                    <button class="btn btn-primary me-2">
                      <i class="fas fa-star"></i>
                      Primary Button (Yellow #F9CC48)
                    </button>
                    <button class="btn btn-secondary">
                      <i class="fas fa-heart"></i>
                      Secondary Button (Blue #263AD1)
                    </button>
                  </div>

                  <div class="mb-3">
                    <input
                      type="text"
                      class="form-control"
                      placeholder="Form input with your colors"
                    />
                  </div>

                  <div class="mb-3">
                    <select class="form-select">
                      <option>Dropdown with your colors</option>
                      <option>Option 1</option>
                      <option>Option 2</option>
                    </select>
                  </div>

                  <div class="mb-3">
                    <a href="#" class="me-3">
                      <i class="fas fa-link"></i>
                      Link (Cyan #17cfe0)
                    </a>
                    <a href="#" class="text-primary">
                      <i class="fas fa-external-link-alt"></i>
                      Primary Link (Blue #263AD1)
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-12">
              <div class="card">
                <div class="card-header">
                  <h5 class="card-title mb-0">
                    <i class="fas fa-table"></i>
                    Table Example
                  </h5>
                </div>
                <div class="card-body">
                  <table class="table table-striped table-hover">
                    <thead>
                      <tr>
                        <th>Column 1</th>
                        <th>Column 2</th>
                        <th>Column 3</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>Primary text color</td>
                        <td>Secondary text color</td>
                        <td>Accent text color</td>
                      </tr>
                      <tr>
                        <td>#263AD1 (Blue)</td>
                        <td>#484B6A (Dark Gray)</td>
                        <td>#17cfe0 (Cyan)</td>
                      </tr>
                      <tr>
                        <td>Background: #FAFAFA</td>
                        <td>Button: #F9CC48</td>
                        <td>All colors working!</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <div class="row mt-4">
            <div class="col-12">
              <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <strong>Success!</strong> Your custom light mode colors are now
                implemented.
              </div>

              <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Warning!</strong> This is how warning alerts look with
                your colors.
              </div>

              <div class="alert alert-danger">
                <i class="fas fa-times-circle"></i>
                <strong>Error!</strong> This is how error alerts look with your
                colors.
              </div>

              <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Info!</strong> This is how info alerts look with your
                cyan color.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  </body>
</html>
