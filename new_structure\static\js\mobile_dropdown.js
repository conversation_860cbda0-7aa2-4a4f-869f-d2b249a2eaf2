// Mobile Dropdown Enhancement Script
// Converts native select elements to custom mobile-friendly dropdowns

class MobileDropdown {
  constructor(selectElement) {
    this.selectElement = selectElement;
    this.wrapper = null;
    this.button = null;
    this.dropdown = null;
    this.isOpen = false;

    this.init();
  }

  init() {
    // Only initialize on mobile devices
    if (!this.isMobile()) {
      return;
    }

    // Store reference to this instance on the select element
    this.selectElement.mobileDropdownInstance = this;

    this.createCustomDropdown();
    this.bindEvents();
  }

  isMobile() {
    // More strict mobile detection - only activate on actual mobile devices or very small screens
    const isMobileDevice =
      /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      );
    const isSmallScreen = window.innerWidth <= 480; // Reduced from 768 to 480

    return isMobileDevice || isSmallScreen;
  }

  createCustomDropdown() {
    // Create wrapper
    this.wrapper = document.createElement("div");
    this.wrapper.className = "mobile-select-wrapper";

    // Create button
    this.button = document.createElement("button");
    this.button.type = "button";
    this.button.className = "mobile-select-button";
    this.button.textContent = this.getSelectedText();

    // Create dropdown
    this.dropdown = document.createElement("div");
    this.dropdown.className = "mobile-select-dropdown";

    // Populate options
    this.populateOptions();

    // Insert wrapper before select element
    this.selectElement.parentNode.insertBefore(
      this.wrapper,
      this.selectElement
    );

    // Move select element into wrapper and hide it
    this.wrapper.appendChild(this.selectElement);
    this.selectElement.classList.add("native-select");

    // Add custom elements to wrapper
    this.wrapper.appendChild(this.button);
    this.wrapper.appendChild(this.dropdown);
  }

  getSelectedText() {
    const selectedOption =
      this.selectElement.options[this.selectElement.selectedIndex];
    return selectedOption ? selectedOption.textContent : "Select an option";
  }

  populateOptions() {
    this.dropdown.innerHTML = "";

    Array.from(this.selectElement.options).forEach((option, index) => {
      if (option.value === "") return; // Skip empty options

      const optionElement = document.createElement("div");
      optionElement.className = "mobile-select-option";
      optionElement.textContent = option.textContent;
      optionElement.dataset.value = option.value;
      optionElement.dataset.index = index;

      if (option.selected) {
        optionElement.classList.add("selected");
      }

      optionElement.addEventListener("click", () => {
        this.selectOption(option, optionElement);
      });

      this.dropdown.appendChild(optionElement);
    });
  }

  selectOption(option, optionElement) {
    // Update native select
    const selectedIndex = parseInt(optionElement.dataset.index);
    this.selectElement.selectedIndex = selectedIndex;

    // Update button text
    this.button.textContent = optionElement.textContent;

    // Update selected state
    this.dropdown.querySelectorAll(".mobile-select-option").forEach((opt) => {
      opt.classList.remove("selected");
    });
    optionElement.classList.add("selected");

    // Close dropdown
    this.closeDropdown();

    // Trigger multiple events to ensure compatibility with existing code
    const events = ["change", "input"];
    events.forEach((eventType) => {
      const event = new Event(eventType, {
        bubbles: true,
        cancelable: true,
      });
      this.selectElement.dispatchEvent(event);
    });

    // Also trigger a custom event for debugging
    console.log(
      `Mobile dropdown: ${this.selectElement.id} changed to ${this.selectElement.value}`
    );

    // Call specific functions based on the select element ID with a small delay
    // to ensure the native select value is properly set
    setTimeout(() => {
      this.handleSpecificDropdownLogic();
    }, 10);
  }

  handleSpecificDropdownLogic() {
    const selectId = this.selectElement.id;
    const selectValue = this.selectElement.value;

    // Handle education level changes
    if (selectId === "education_level") {
      console.log("Education level changed via mobile dropdown:", selectValue);
      if (typeof window.updateEducationLevel === "function") {
        window.updateEducationLevel();
      }
      // Also try to call the specific function for filtering grades
      if (typeof window.filterGradesByEducationLevel === "function") {
        const gradeSelect = document.getElementById("grade");
        if (gradeSelect && selectValue) {
          window.filterGradesByEducationLevel(selectValue, gradeSelect);
        }
      }
    }

    // Handle grade changes
    if (selectId === "grade") {
      console.log("Grade changed via mobile dropdown:", selectValue);
      if (typeof window.fetchStreams === "function") {
        window.fetchStreams();
      }
      if (typeof window.updateStreams === "function") {
        window.updateStreams();
      }
    }

    // Handle bulk form education level changes
    if (selectId === "bulk_education_level") {
      console.log(
        "Bulk education level changed via mobile dropdown:",
        selectValue
      );
      if (typeof window.updateBulkEducationLevel === "function") {
        window.updateBulkEducationLevel();
      }
    }

    // Handle bulk grade changes
    if (selectId === "bulk_grade") {
      console.log("Bulk grade changed via mobile dropdown:", selectValue);
      if (typeof window.fetchBulkStreams === "function") {
        window.fetchBulkStreams();
      }
    }

    // Handle subject changes (for teacher portal)
    if (selectId === "subject") {
      console.log("Subject changed via mobile dropdown:", selectValue);
      if (typeof window.updateSubjectInfo === "function") {
        window.updateSubjectInfo();
      }
    }

    // Handle stream changes
    if (selectId === "stream") {
      console.log("Stream changed via mobile dropdown:", selectValue);
      // Any specific stream change logic can go here
    }
  }

  bindEvents() {
    this.button.addEventListener("click", (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.toggleDropdown();
    });

    // Close dropdown when clicking outside
    document.addEventListener("click", (e) => {
      if (!this.wrapper.contains(e.target)) {
        this.closeDropdown();
      }
    });

    // Handle native select changes (for programmatic updates)
    this.selectElement.addEventListener("change", () => {
      this.button.textContent = this.getSelectedText();
      this.updateSelectedOption();
    });

    // Close dropdown on escape key
    document.addEventListener("keydown", (e) => {
      if (e.key === "Escape" && this.isOpen) {
        this.closeDropdown();
      }
    });
  }

  toggleDropdown() {
    if (this.isOpen) {
      this.closeDropdown();
    } else {
      this.openDropdown();
    }
  }

  openDropdown() {
    // Close other dropdowns first
    MobileDropdown.closeAllDropdowns();

    this.dropdown.classList.add("show");
    this.button.classList.add("active");
    this.isOpen = true;

    // Add to active dropdowns list
    MobileDropdown.activeDropdowns.add(this);
  }

  closeDropdown() {
    this.dropdown.classList.remove("show");
    this.button.classList.remove("active");
    this.isOpen = false;

    // Remove from active dropdowns list
    MobileDropdown.activeDropdowns.delete(this);
  }

  updateSelectedOption() {
    this.dropdown.querySelectorAll(".mobile-select-option").forEach((opt) => {
      opt.classList.remove("selected");
      if (opt.dataset.value === this.selectElement.value) {
        opt.classList.add("selected");
      }
    });
  }

  // Update options when select element changes
  updateOptions() {
    this.populateOptions();
    this.button.textContent = this.getSelectedText();
  }

  // Method to refresh dropdown when native select is updated programmatically
  refresh() {
    if (!this.isMobile()) {
      // If no longer mobile, destroy the mobile dropdown
      this.destroy();
      return;
    }
    this.updateOptions();
  }

  destroy() {
    // Remove mobile dropdown elements and restore native select
    if (this.wrapper && this.wrapper.parentNode) {
      // Show the native select
      this.selectElement.style.display = "";
      this.selectElement.classList.remove("native-select");

      // Remove the wrapper
      this.wrapper.parentNode.insertBefore(this.selectElement, this.wrapper);
      this.wrapper.parentNode.removeChild(this.wrapper);

      // Clear the instance reference
      this.selectElement.mobileDropdownInstance = null;

      console.log(`Destroyed mobile dropdown for: ${this.selectElement.id}`);
    }
  }

  static activeDropdowns = new Set();

  static closeAllDropdowns() {
    this.activeDropdowns.forEach((dropdown) => {
      dropdown.closeDropdown();
    });
  }
}

// Initialize mobile dropdowns when DOM is ready
document.addEventListener("DOMContentLoaded", function () {
  // Find all select elements and convert them (mobile detection is done in the class)
  const selectElements = document.querySelectorAll("select");
  selectElements.forEach((select) => {
    new MobileDropdown(select);
  });
});

// Handle window resize - initialize or destroy mobile dropdowns as needed
window.addEventListener("resize", function () {
  const selectElements = document.querySelectorAll("select");
  selectElements.forEach((select) => {
    const isMobileDevice =
      /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      );
    const isSmallScreen = window.innerWidth <= 480;
    const shouldBeMobile = isMobileDevice || isSmallScreen;

    if (shouldBeMobile && !select.mobileDropdownInstance) {
      // Initialize mobile dropdown if not already done
      new MobileDropdown(select);
    } else if (!shouldBeMobile && select.mobileDropdownInstance) {
      // Destroy mobile dropdown if switching to desktop
      select.mobileDropdownInstance.destroy();
    }
  });
});

// Global function to refresh a specific mobile dropdown
window.refreshMobileDropdown = function (selectId) {
  const selectElement = document.getElementById(selectId);
  if (selectElement && selectElement.mobileDropdownInstance) {
    selectElement.mobileDropdownInstance.refresh();
    console.log(`Refreshed mobile dropdown for: ${selectId}`);
  }
};

// Global function to refresh all mobile dropdowns
window.refreshAllMobileDropdowns = function () {
  const selectElements = document.querySelectorAll("select.native-select");
  selectElements.forEach((select) => {
    if (select.mobileDropdownInstance) {
      select.mobileDropdownInstance.refresh();
    }
  });
  console.log("Refreshed all mobile dropdowns");
};

// Export for use in other scripts
window.MobileDropdown = MobileDropdown;
