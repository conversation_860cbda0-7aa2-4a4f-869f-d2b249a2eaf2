<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Analytics Dashboard - Hillview School</title>
    
    <!-- Modern Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          rel="stylesheet"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
          crossorigin="anonymous"
          referrerpolicy="no-referrer">
    
    <!-- CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/modern_classteacher.css') }}">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        .container-fluid {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        .card-header {
            background: #667eea;
            color: white;
            padding: 1.5rem;
            border-bottom: none;
        }
        .card-title {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .card-body {
            padding: 2rem;
        }
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: none;
        }
        .alert-info {
            background: #e3f2fd;
            color: #0277bd;
        }
        .alert-success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .alert-error {
            background: #ffebee;
            color: #c62828;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 1rem;
            text-align: center;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        .no-data {
            text-align: center;
            padding: 4rem 2rem;
            color: #666;
        }
        .no-data i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 1rem;
        }
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .navbar {
            background: #2c3e50;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        .navbar-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }
        .navbar-brand {
            color: white;
            font-size: 1.25rem;
            font-weight: 600;
            text-decoration: none;
        }
        .navbar-nav {
            display: flex;
            gap: 1rem;
        }
        .navbar-nav a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: background 0.3s ease;
        }
        .navbar-nav a:hover {
            background: rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="navbar-content">
            <a href="{{ url_for('classteacher.dashboard') }}" class="navbar-brand">
                <i class="fas fa-home"></i> Hillview School
            </a>
            <div class="navbar-nav">
                <a href="{{ url_for('classteacher.dashboard') }}">
                    <i class="fas fa-dashboard"></i> Dashboard
                </a>
                <a href="{{ url_for('auth.logout') }}">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'error' if category == 'error' else category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-chart-line"></i>
                    Academic Performance Analytics
                </h3>
            </div>
            <div class="card-body">
                {% if analytics_data and analytics_data.has_data %}
          <div class="row">
            <!-- Summary Statistics -->
            <div class="col-md-4 mb-4">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <h5>Total Students</h5>
                  <h3>{{ analytics_data.summary.total_students or 0 }}</h3>
                </div>
              </div>
            </div>

            <div class="col-md-4 mb-4">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <h5>Average Performance</h5>
                  <h3>
                    {{ "%.1f"|format(analytics_data.summary.average_performance
                    or 0) }}%
                  </h3>
                </div>
              </div>
            </div>

            <div class="col-md-4 mb-4">
              <div class="card bg-info text-white">
                <div class="card-body">
                  <h5>Subjects Analyzed</h5>
                  <h3>{{ analytics_data.summary.subjects_count or 0 }}</h3>
                </div>
              </div>
            </div>
          </div>

          <!-- Top Students -->
          {% if analytics_data.top_students %}
          <div class="row mb-4">
            <div class="col-12">
              <h4>Top Performing Students</h4>
              <div class="table-responsive">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>Rank</th>
                      <th>Student Name</th>
                      <th>Average Score</th>
                      <th>Grade</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for student in analytics_data.top_students %}
                    <tr>
                      <td>{{ loop.index }}</td>
                      <td>{{ student.name }}</td>
                      <td>{{ "%.1f"|format(student.average_score) }}%</td>
                      <td>{{ student.grade }}</td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          {% endif %}

          <!-- Subject Performance -->
          {% if analytics_data.subject_performance %}
          <div class="row">
            <div class="col-12">
              <h4>Subject Performance Overview</h4>
              <div class="table-responsive">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>Subject</th>
                      <th>Average Score</th>
                      <th>Students Count</th>
                      <th>Performance Level</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for subject in analytics_data.subject_performance %}
                    <tr>
                      <td>{{ subject.name }}</td>
                      <td>{{ "%.1f"|format(subject.average_score) }}%</td>
                      <td>{{ subject.students_count }}</td>
                      <td>
                        {% if subject.average_score >= 80 %}
                        <span class="badge bg-success">Excellent</span>
                        {% elif subject.average_score >= 60 %}
                        <span class="badge bg-primary">Good</span>
                        {% elif subject.average_score >= 40 %}
                        <span class="badge bg-warning">Average</span>
                        {% else %}
                        <span class="badge bg-danger">Needs Improvement</span>
                        {% endif %}
                      </td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          {% endif %} {% else %}
          <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <strong>No Data Available</strong>
            <p>
              There is no academic data available for analysis yet. Please
              ensure that:
            </p>
            <ul>
              <li>Students have been enrolled in your classes</li>
              <li>Marks have been uploaded for recent assessments</li>
              <li>Reports have been generated for the current term</li>
            </ul>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
