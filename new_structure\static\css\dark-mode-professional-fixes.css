/* ===================================================================
   HILLVIEW SCHOOL MANAGEMENT SYSTEM - PROFESSIONAL DARK MODE FIXES
   Enhanced visibility and contrast for all UI elements
   ================================================================== */

/* ===== BODY AND GLOBAL STYLES ===== */
[data-theme="dark"] body {
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .container,
[data-theme="dark"] .container-fluid {
  background: transparent !important;
  color: var(--text-primary) !important;
}

/* ===== NAVIGATION IMPROVEMENTS ===== */
[data-theme="dark"] .navbar {
  background: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border-primary) !important;
}

[data-theme="dark"] .navbar-brand {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .navbar-nav .nav-link {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .navbar-nav .nav-link:hover,
[data-theme="dark"] .navbar-nav .nav-link:focus {
  color: var(--primary-color) !important;
}

[data-theme="dark"] .navbar-nav .nav-link.active {
  color: var(--primary-color) !important;
}

/* ===== DROPDOWN IMPROVEMENTS ===== */
[data-theme="dark"] .dropdown-menu {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-primary) !important;
  box-shadow: var(--shadow-lg) !important;
}

[data-theme="dark"] .dropdown-item {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:focus {
  background: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .dropdown-divider {
  border-top-color: var(--border-primary) !important;
}

/* ===== CARD IMPROVEMENTS ===== */
[data-theme="dark"] .card {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .card-header {
  background: var(--bg-tertiary) !important;
  border-bottom: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .card-footer {
  background: var(--bg-tertiary) !important;
  border-top: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .card-title {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .card-text {
  color: var(--text-secondary) !important;
}

/* ===== BUTTON IMPROVEMENTS ===== */
[data-theme="dark"] .btn {
  border-width: 1px !important;
}

[data-theme="dark"] .btn-primary {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: #ffffff !important;
}

[data-theme="dark"] .btn-primary:hover,
[data-theme="dark"] .btn-primary:focus {
  background: var(--primary-dark) !important;
  border-color: var(--primary-dark) !important;
  color: #ffffff !important;
}

[data-theme="dark"] .btn-secondary {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .btn-secondary:hover,
[data-theme="dark"] .btn-secondary:focus {
  background: var(--bg-quaternary) !important;
  border-color: var(--border-secondary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .btn-outline-primary {
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  background: transparent !important;
}

[data-theme="dark"] .btn-outline-primary:hover,
[data-theme="dark"] .btn-outline-primary:focus {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: #ffffff !important;
}

[data-theme="dark"] .btn-outline-secondary {
  color: var(--text-primary) !important;
  border-color: var(--border-primary) !important;
  background: transparent !important;
}

[data-theme="dark"] .btn-outline-secondary:hover,
[data-theme="dark"] .btn-outline-secondary:focus {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

/* ===== FORM IMPROVEMENTS ===== */
[data-theme="dark"] .form-control {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .form-control:focus {
  background: var(--bg-secondary) !important;
  border-color: var(--primary-color) !important;
  color: var(--text-primary) !important;
  box-shadow: 0 0 0 0.2rem rgba(96, 165, 250, 0.25) !important;
}

[data-theme="dark"] .form-control::placeholder {
  color: var(--text-muted) !important;
}

[data-theme="dark"] .form-select {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .form-select:focus {
  background: var(--bg-secondary) !important;
  border-color: var(--primary-color) !important;
  color: var(--text-primary) !important;
  box-shadow: 0 0 0 0.2rem rgba(96, 165, 250, 0.25) !important;
}

[data-theme="dark"] .form-label {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .form-text {
  color: var(--text-tertiary) !important;
}

/* ===== TABLE IMPROVEMENTS ===== */
[data-theme="dark"] .table {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .table th {
  background: var(--bg-tertiary) !important;
  border-bottom: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .table td {
  border-top: 1px solid var(--border-primary) !important;
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .table-striped tbody tr:nth-of-type(odd) {
  background: rgba(255, 255, 255, 0.05) !important;
}

[data-theme="dark"] .table-hover tbody tr:hover {
  background: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

/* ===== MODAL IMPROVEMENTS ===== */
[data-theme="dark"] .modal-content {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .modal-header {
  border-bottom: 1px solid var(--border-primary) !important;
}

[data-theme="dark"] .modal-footer {
  border-top: 1px solid var(--border-primary) !important;
}

[data-theme="dark"] .modal-title {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .btn-close {
  filter: invert(1) grayscale(100%) brightness(200%);
}

/* ===== ALERT IMPROVEMENTS ===== */
[data-theme="dark"] .alert {
  border-width: 1px !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .alert-primary {
  background: rgba(96, 165, 250, 0.1) !important;
  border-color: rgba(96, 165, 250, 0.3) !important;
  color: #93c5fd !important;
}

[data-theme="dark"] .alert-success {
  background: rgba(52, 211, 153, 0.1) !important;
  border-color: rgba(52, 211, 153, 0.3) !important;
  color: #6ee7b7 !important;
}

[data-theme="dark"] .alert-warning {
  background: rgba(251, 191, 36, 0.1) !important;
  border-color: rgba(251, 191, 36, 0.3) !important;
  color: #fcd34d !important;
}

[data-theme="dark"] .alert-danger {
  background: rgba(248, 113, 113, 0.1) !important;
  border-color: rgba(248, 113, 113, 0.3) !important;
  color: #fca5a5 !important;
}

[data-theme="dark"] .alert-info {
  background: rgba(96, 165, 250, 0.1) !important;
  border-color: rgba(96, 165, 250, 0.3) !important;
  color: #93c5fd !important;
}

/* ===== PAGINATION IMPROVEMENTS ===== */
[data-theme="dark"] .pagination .page-link {
  background: var(--bg-secondary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .pagination .page-link:hover {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .pagination .page-item.active .page-link {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: #ffffff !important;
}

[data-theme="dark"] .pagination .page-item.disabled .page-link {
  background: var(--bg-secondary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-muted) !important;
}

/* ===== BREADCRUMB IMPROVEMENTS ===== */
[data-theme="dark"] .breadcrumb {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-primary) !important;
}

[data-theme="dark"] .breadcrumb-item a {
  color: var(--primary-color) !important;
}

[data-theme="dark"] .breadcrumb-item.active {
  color: var(--text-primary) !important;
}

/* ===== BADGE IMPROVEMENTS ===== */
[data-theme="dark"] .badge {
  color: #ffffff !important;
}

[data-theme="dark"] .badge.bg-primary {
  background: var(--primary-color) !important;
}

[data-theme="dark"] .badge.bg-secondary {
  background: var(--bg-quaternary) !important;
}

[data-theme="dark"] .badge.bg-success {
  background: var(--success-color) !important;
}

[data-theme="dark"] .badge.bg-warning {
  background: var(--warning-color) !important;
  color: #000000 !important;
}

[data-theme="dark"] .badge.bg-danger {
  background: var(--error-color) !important;
}

[data-theme="dark"] .badge.bg-info {
  background: var(--info-color) !important;
}

/* ===== ICON IMPROVEMENTS ===== */
[data-theme="dark"] i,
[data-theme="dark"] .fa,
[data-theme="dark"] .fas,
[data-theme="dark"] .far,
[data-theme="dark"] .fal,
[data-theme="dark"] .fab {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .text-primary i,
[data-theme="dark"] .text-primary .fa,
[data-theme="dark"] .text-primary .fas {
  color: var(--primary-color) !important;
}

[data-theme="dark"] .text-success i,
[data-theme="dark"] .text-success .fa,
[data-theme="dark"] .text-success .fas {
  color: var(--success-color) !important;
}

[data-theme="dark"] .text-warning i,
[data-theme="dark"] .text-warning .fa,
[data-theme="dark"] .text-warning .fas {
  color: var(--warning-color) !important;
}

[data-theme="dark"] .text-danger i,
[data-theme="dark"] .text-danger .fa,
[data-theme="dark"] .text-danger .fas {
  color: var(--error-color) !important;
}

/* ===== LINK IMPROVEMENTS ===== */
[data-theme="dark"] a {
  color: var(--primary-color) !important;
}

[data-theme="dark"] a:hover,
[data-theme="dark"] a:focus {
  color: var(--primary-light) !important;
}

/* ===== PROGRESS BAR IMPROVEMENTS ===== */
[data-theme="dark"] .progress {
  background: var(--bg-tertiary) !important;
  border: 1px solid var(--border-primary) !important;
}

[data-theme="dark"] .progress-bar {
  background: var(--primary-color) !important;
}

/* ===== TOOLTIP IMPROVEMENTS ===== */
[data-theme="dark"] .tooltip .tooltip-inner {
  background: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border-primary) !important;
}

[data-theme="dark"] .tooltip.bs-tooltip-top .tooltip-arrow::before {
  border-top-color: var(--bg-tertiary) !important;
}

[data-theme="dark"] .tooltip.bs-tooltip-bottom .tooltip-arrow::before {
  border-bottom-color: var(--bg-tertiary) !important;
}

[data-theme="dark"] .tooltip.bs-tooltip-start .tooltip-arrow::before {
  border-left-color: var(--bg-tertiary) !important;
}

[data-theme="dark"] .tooltip.bs-tooltip-end .tooltip-arrow::before {
  border-right-color: var(--bg-tertiary) !important;
}

/* ===== SPINNER IMPROVEMENTS ===== */
[data-theme="dark"] .spinner-border {
  color: var(--primary-color) !important;
}

[data-theme="dark"] .spinner-grow {
  color: var(--primary-color) !important;
}

/* ===== OFFCANVAS IMPROVEMENTS ===== */
[data-theme="dark"] .offcanvas {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .offcanvas-header {
  border-bottom: 1px solid var(--border-primary) !important;
}

[data-theme="dark"] .offcanvas-title {
  color: var(--text-primary) !important;
}

/* ===== ACCORDION IMPROVEMENTS ===== */
[data-theme="dark"] .accordion-item {
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-primary) !important;
}

[data-theme="dark"] .accordion-header .accordion-button {
  background: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .accordion-header .accordion-button:focus {
  box-shadow: 0 0 0 0.2rem rgba(96, 165, 250, 0.25) !important;
}

[data-theme="dark"] .accordion-header .accordion-button:not(.collapsed) {
  background: var(--bg-quaternary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .accordion-body {
  background: var(--bg-secondary) !important;
  color: var(--text-secondary) !important;
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */
@media (max-width: 768px) {
  [data-theme="dark"] .theme-toggle {
    background: var(--bg-secondary) !important;
    border-color: var(--border-primary) !important;
  }
}

/* ===== FOCUS IMPROVEMENTS ===== */
[data-theme="dark"] *:focus {
  outline: 2px solid var(--primary-color) !important;
  outline-offset: 2px !important;
}

[data-theme="dark"] .btn:focus,
[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
  box-shadow: 0 0 0 0.2rem rgba(96, 165, 250, 0.25) !important;
}

/* ===== SHADOW IMPROVEMENTS ===== */
[data-theme="dark"] .shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.5) !important;
}

[data-theme="dark"] .shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.5) !important;
}

[data-theme="dark"] .shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.5) !important;
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
[data-theme="dark"] .visually-hidden-focusable:focus,
[data-theme="dark"] .visually-hidden-focusable:focus-within {
  color: var(--text-primary) !important;
  background: var(--bg-secondary) !important;
}

/* ===== CUSTOM SCROLLBAR FOR DARK MODE ===== */
[data-theme="dark"] ::-webkit-scrollbar {
  width: 12px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: var(--bg-quaternary);
  border-radius: 6px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--border-tertiary);
}

/* ===== SELECTION IMPROVEMENTS ===== */
[data-theme="dark"] ::selection {
  background: var(--primary-color) !important;
  color: #ffffff !important;
}

[data-theme="dark"] ::-moz-selection {
  background: var(--primary-color) !important;
  color: #ffffff !important;
}
