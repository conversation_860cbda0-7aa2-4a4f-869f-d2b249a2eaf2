<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Debug Headteacher Issues</title>

    <!-- FontAwesome CDN -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />

    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background: #f5f5f5;
      }

      .test-section {
        background: white;
        padding: 20px;
        margin: 20px 0;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .icon-test {
        font-size: 24px;
        margin: 10px;
        padding: 10px;
        background: #f0f0f0;
        border-radius: 4px;
        display: inline-block;
      }

      .navbar-test {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1rem 0;
        margin: 20px 0;
        border-radius: 8px;
      }

      .navbar-test .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 1rem;
      }

      .navbar-brand {
        color: white;
        font-size: 1.5rem;
        font-weight: 700;
        text-decoration: none;
      }

      .navbar-nav {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 0.5rem;
      }

      .nav-link {
        color: rgba(255, 255, 255, 0.9);
        text-decoration: none;
        font-weight: 500;
        padding: 0.4rem 0.6rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.25rem;
      }

      .nav-link:hover {
        color: white;
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-1px);
      }

      .test-button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
      }

      .test-button:hover {
        background: #0056b3;
      }

      .status {
        padding: 10px;
        margin: 10px 0;
        border-radius: 4px;
      }

      .status.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .status.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .status.info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }
    </style>
  </head>
  <body>
    <h1>Debug Headteacher Issues</h1>

    <div class="test-section">
      <h2>FontAwesome Icons Test</h2>
      <p>Testing if FontAwesome icons are loading correctly:</p>

      <div class="icon-test">
        <i class="fas fa-users-cog"></i> Universal Access
      </div>
      <div class="icon-test"><i class="fas fa-chart-line"></i> Dashboard</div>
      <div class="icon-test">
        <i class="fas fa-cogs"></i> Subject Configuration
      </div>
      <div class="icon-test"><i class="fas fa-chart-bar"></i> Analytics</div>
      <div class="icon-test"><i class="fas fa-file-alt"></i> Reports</div>
      <div class="icon-test"><i class="fas fa-users"></i> Staff</div>
      <div class="icon-test">
        <i class="fas fa-graduation-cap"></i> Student Promotion
      </div>
      <div class="icon-test"><i class="fas fa-key"></i> Permissions</div>
      <div class="icon-test"><i class="fas fa-sign-out-alt"></i> Logout</div>

      <div id="fontawesome-status" class="status info">
        Checking FontAwesome...
      </div>
    </div>

    <div class="test-section">
      <h2>Navigation Test</h2>
      <p>Testing the navbar similar to headteacher dashboard:</p>

      <nav class="navbar-test">
        <div class="container">
          <a href="#" class="navbar-brand">
            <span>Hillview School</span>
          </a>
          <ul class="navbar-nav">
            <li>
              <a href="/universal/dashboard" class="nav-link">
                <i class="fas fa-users-cog"></i> <span>Universal Access</span>
              </a>
            </li>
            <li>
              <a href="/headteacher/" class="nav-link">
                <i class="fas fa-chart-line"></i> <span>Dashboard</span>
              </a>
            </li>
            <li>
              <a href="/subject-configuration" class="nav-link">
                <i class="fas fa-cogs"></i> <span>Subject Configuration</span>
              </a>
            </li>
            <li>
              <a
                href="/headteacher/analytics"
                class="nav-link"
                id="analytics-link"
              >
                <i class="fas fa-chart-bar"></i> <span>Analytics</span>
              </a>
            </li>
            <li>
              <a href="/headteacher/reports" class="nav-link">
                <i class="fas fa-file-alt"></i> <span>Reports</span>
              </a>
            </li>
          </ul>
        </div>
      </nav>

      <div id="navigation-status" class="status info">
        Click the Analytics link to test navigation
      </div>
    </div>

    <div class="test-section">
      <h2>Manual Tests</h2>
      <button class="test-button" onclick="testAnalyticsNavigation()">
        Test Analytics Navigation
      </button>
      <button class="test-button" onclick="testFontAwesome()">
        Test FontAwesome
      </button>
      <button class="test-button" onclick="checkConsoleErrors()">
        Check Console Errors
      </button>
      <button class="test-button" onclick="checkAuthentication()">
        Check Authentication
      </button>

      <div id="test-results" class="status info">
        Click buttons above to run tests
      </div>
    </div>

    <script>
      // FontAwesome detection
      document.addEventListener("DOMContentLoaded", function () {
        const statusEl = document.getElementById("fontawesome-status");

        // Test if FontAwesome CSS is loaded
        const testIcon = document.createElement("i");
        testIcon.className = "fas fa-home";
        testIcon.style.visibility = "hidden";
        testIcon.style.position = "absolute";
        document.body.appendChild(testIcon);

        setTimeout(() => {
          const computedStyle = window.getComputedStyle(testIcon, ":before");
          const content = computedStyle.getPropertyValue("content");
          const fontFamily = computedStyle.getPropertyValue("font-family");

          document.body.removeChild(testIcon);

          if (content && content !== "none" && content !== '""') {
            statusEl.className = "status success";
            statusEl.textContent = "✅ FontAwesome is working correctly";
          } else {
            statusEl.className = "status error";
            statusEl.textContent = "❌ FontAwesome not loading properly";
          }

          // Check for squares in icons
          const icons = document.querySelectorAll(".fa, .fas, .far, .fab");
          let hasSquares = false;

          icons.forEach((icon) => {
            if (icon.textContent === "□" || icon.textContent === "") {
              hasSquares = true;
            }
          });

          if (hasSquares) {
            statusEl.className = "status error";
            statusEl.textContent += " - Some icons showing as squares";
          }
        }, 100);
      });

      // Analytics navigation test
      document
        .getElementById("analytics-link")
        .addEventListener("click", function (e) {
          e.preventDefault();
          const statusEl = document.getElementById("navigation-status");
          statusEl.className = "status info";
          statusEl.textContent =
            "Analytics link clicked - checking navigation...";

          // Test if the URL would work
          fetch("/headteacher/analytics", { method: "HEAD" })
            .then((response) => {
              if (response.ok) {
                statusEl.className = "status success";
                statusEl.textContent =
                  "✅ Analytics route is accessible - navigation should work";
                // Actually navigate after confirming it works
                setTimeout(() => {
                  window.location.href = "/headteacher/analytics";
                }, 1000);
              } else {
                statusEl.className = "status error";
                statusEl.textContent =
                  "❌ Analytics route returned error: " + response.status;
              }
            })
            .catch((error) => {
              statusEl.className = "status error";
              statusEl.textContent =
                "❌ Error testing analytics route: " + error.message;
            });
        });

      function testAnalyticsNavigation() {
        const resultsEl = document.getElementById("test-results");
        resultsEl.className = "status info";
        resultsEl.textContent = "Testing analytics navigation...";

        // Test both HEAD and GET requests
        fetch("/headteacher/analytics", { method: "HEAD" })
          .then((response) => {
            if (response.ok) {
              resultsEl.className = "status success";
              resultsEl.textContent =
                "✅ Analytics route is accessible (HEAD request)";

              // Now test GET request
              return fetch("/headteacher/analytics", { method: "GET" });
            } else {
              throw new Error(`HEAD request failed: ${response.status}`);
            }
          })
          .then((response) => {
            if (response.ok) {
              resultsEl.textContent += " - GET request also successful";
              return response.text();
            } else {
              throw new Error(`GET request failed: ${response.status}`);
            }
          })
          .then((html) => {
            if (html.includes("analytics") || html.includes("Analytics")) {
              resultsEl.textContent += " - Page content looks correct";
            } else {
              resultsEl.className = "status error";
              resultsEl.textContent =
                "❌ Analytics page may be redirecting or showing wrong content";
            }
          })
          .catch((error) => {
            resultsEl.className = "status error";
            resultsEl.textContent = "❌ Error: " + error.message;
          });
      }

      function testFontAwesome() {
        const resultsEl = document.getElementById("test-results");

        // Count total icons and squares
        const allIcons = document.querySelectorAll(".fa, .fas, .far, .fab");
        let squareCount = 0;

        allIcons.forEach((icon) => {
          if (icon.textContent === "□" || icon.textContent === "") {
            squareCount++;
          }
        });

        if (squareCount === 0) {
          resultsEl.className = "status success";
          resultsEl.textContent = `✅ All ${allIcons.length} icons are displaying correctly`;
        } else {
          resultsEl.className = "status error";
          resultsEl.textContent = `❌ ${squareCount} out of ${allIcons.length} icons showing as squares`;
        }
      }

      function checkConsoleErrors() {
        const resultsEl = document.getElementById("test-results");
        resultsEl.className = "status info";
        resultsEl.textContent =
          "Check browser console (F12) for any JavaScript errors";
      }

      function checkAuthentication() {
        const resultsEl = document.getElementById("test-results");
        resultsEl.className = "status info";
        resultsEl.textContent = "Checking authentication status...";

        fetch("/debug-session")
          .then((response) => response.json())
          .then((data) => {
            if (data.is_authenticated && data.role === "headteacher") {
              resultsEl.className = "status success";
              resultsEl.textContent = `✅ Authenticated as headteacher (${data.username})`;
            } else if (data.is_authenticated) {
              resultsEl.className = "status error";
              resultsEl.textContent = `❌ Authenticated but wrong role: ${data.role}`;
            } else {
              resultsEl.className = "status error";
              resultsEl.textContent =
                "❌ Not authenticated - please log in as headteacher";
            }
          })
          .catch((error) => {
            resultsEl.className = "status error";
            resultsEl.textContent =
              "❌ Error checking authentication: " + error.message;
          });
      }
    </script>
  </body>
</html>
