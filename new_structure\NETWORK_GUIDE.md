# 🌐 Hillview Network Access Guide

## 📍 Current Server Information

Your Hillview School Management System is running on:

- **Local Access**: `http://localhost:8080`
- **Network Access**: `http://*************:8080`
- **Alternative Local**: `http://127.0.0.1:8080`

## 🔗 How to Access the Application

### From the Same Computer
```
http://localhost:8080
```

### From Mobile Devices or Other Computers on Same Network
```
http://*************:8080
```

## ⚠️ Common Issues and Solutions

### Issue 1: "This site can't be reached" or "Connection timeout"

**Cause**: Using wrong IP address or network connectivity issues

**Solutions**:
1. **Check the correct IP**: Run `python run.py` and use the IP shown in "Network:" line
2. **Try localhost first**: Use `http://localhost:8080` to verify server is running
3. **Check firewall**: Windows Firewall might be blocking the connection

### Issue 2: Wrong IP Address Displayed

**Cause**: Hardcoded IP in run.py script

**Solution**: ✅ **FIXED** - The script now automatically detects your current IP address

### Issue 3: Mobile Access Not Working

**Possible Causes & Solutions**:

1. **Firewall Blocking**:
   - Go to Windows Firewall settings
   - Allow Python through firewall
   - Or temporarily disable firewall for testing

2. **Wrong Network**:
   - Ensure mobile device is on same WiFi network
   - Check if mobile device can ping the computer

3. **Port Blocked**:
   - Try a different port (change 8080 to 8081 in run.py)
   - Check if antivirus is blocking the port

## 🛠️ Troubleshooting Tools

### 1. Network Test Script
```bash
python network_test.py
```
This will test all connectivity and show detailed diagnostics.

### 2. Manual IP Check
```bash
ipconfig
```
Look for "IPv4 Address" under your active network adapter.

### 3. Port Test
```bash
netstat -an | findstr :8080
```
Should show the port is listening.

## 📱 Mobile Device Setup

1. **Connect to same WiFi** as your computer
2. **Open browser** on mobile device
3. **Enter URL**: `http://*************:8080`
4. **If it doesn't work**:
   - Try `http://[YOUR_COMPUTER_IP]:8080`
   - Check Windows Firewall settings
   - Run network test script

## 🔧 Advanced Configuration

### Change Port (if 8080 is blocked)
Edit `run.py` line 74:
```python
app.run(debug=True, host='0.0.0.0', port=8081)  # Change 8080 to 8081
```

### Disable Firewall Temporarily (for testing)
1. Open Windows Security
2. Go to Firewall & network protection
3. Turn off firewall for Private network
4. Test access
5. **Remember to turn it back on!**

## ✅ Verification Steps

1. **Server Running**: Check terminal shows "Debugger is active!"
2. **Local Access**: Open `http://localhost:8080` in browser
3. **Network Access**: Open `http://*************:8080` in browser
4. **Mobile Access**: Use same network URL on mobile device

## 🆘 Quick Fix Commands

```bash
# Check if server is running
python run.py

# Test network connectivity
python network_test.py

# Check current IP
ipconfig

# Check if port is open
netstat -an | findstr :8080
```

## 📞 Support Information

If you continue having issues:

1. Run `python network_test.py` and share the output
2. Check Windows Firewall settings
3. Verify both devices are on same network
4. Try using localhost URL first to confirm server works

---

**Last Updated**: 2025-07-14  
**Server Status**: ✅ Running correctly with automatic IP detection
