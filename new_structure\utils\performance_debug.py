"""
Performance debugging utilities for Hillview School Management System.
This module provides tools to identify and fix performance issues.
"""

import time
import functools
from flask import g, request, current_app


def performance_monitor(f):
    """Decorator to monitor route performance."""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        result = f(*args, **kwargs)
        end_time = time.time()
        duration = end_time - start_time
        
        # Log slow requests
        if duration > 2.0:  # More than 2 seconds
            current_app.logger.warning(
                f"SLOW REQUEST: {request.endpoint} took {duration:.2f}s "
                f"for {request.method} {request.path}"
            )
        else:
            current_app.logger.info(
                f"REQUEST: {request.endpoint} took {duration:.2f}s"
            )
        
        return result
    return decorated_function


def add_performance_routes(app):
    """Add performance debugging routes to the application."""
    
    @app.route('/debug/performance')
    def performance_debug():
        """Debug route to check system performance."""
        import psutil
        import sys
        
        # Get system stats
        memory_info = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Get Python version
        python_version = sys.version
        
        # Get Flask app info
        app_info = {
            'debug': app.debug,
            'testing': app.testing,
            'env': app.env,
            'instance_path': app.instance_path,
            'config': {
                'SECRET_KEY': '***' if app.config.get('SECRET_KEY') else 'NOT SET',
                'SQLALCHEMY_DATABASE_URI': 'SET' if app.config.get('SQLALCHEMY_DATABASE_URI') else 'NOT SET',
                'SQLALCHEMY_TRACK_MODIFICATIONS': app.config.get('SQLALCHEMY_TRACK_MODIFICATIONS', 'NOT SET'),
            }
        }
        
        debug_info = {
            'system': {
                'memory_total': f"{memory_info.total / (1024**3):.2f} GB",
                'memory_available': f"{memory_info.available / (1024**3):.2f} GB",
                'memory_percent': f"{memory_info.percent:.1f}%",
                'cpu_percent': f"{cpu_percent:.1f}%",
            },
            'python': {
                'version': python_version,
                'executable': sys.executable,
            },
            'flask': app_info,
            'request': {
                'method': request.method,
                'path': request.path,
                'user_agent': request.headers.get('User-Agent', 'Unknown'),
                'remote_addr': request.remote_addr,
            }
        }
        
        return f"""
        <html>
        <head>
            <title>Performance Debug - Hillview School</title>
            <style>
                body {{ font-family: monospace; margin: 20px; }}
                .section {{ margin: 20px 0; padding: 10px; border: 1px solid #ccc; }}
                .good {{ color: green; }}
                .warning {{ color: orange; }}
                .error {{ color: red; }}
                pre {{ background: #f5f5f5; padding: 10px; }}
            </style>
        </head>
        <body>
            <h1>Hillview School Performance Debug</h1>
            
            <div class="section">
                <h2>System Performance</h2>
                <p>Memory Usage: <span class="{'good' if memory_info.percent < 80 else 'warning' if memory_info.percent < 95 else 'error'}">{memory_info.percent:.1f}%</span></p>
                <p>CPU Usage: <span class="{'good' if cpu_percent < 80 else 'warning' if cpu_percent < 95 else 'error'}">{cpu_percent:.1f}%</span></p>
                <p>Available Memory: {memory_info.available / (1024**3):.2f} GB</p>
            </div>
            
            <div class="section">
                <h2>Application Status</h2>
                <p>Debug Mode: <span class="{'warning' if app.debug else 'good'}">{app.debug}</span></p>
                <p>Environment: {app.env}</p>
                <p>Database: <span class="{'good' if app.config.get('SQLALCHEMY_DATABASE_URI') else 'error'}">{'Connected' if app.config.get('SQLALCHEMY_DATABASE_URI') else 'Not configured'}</span></p>
            </div>
            
            <div class="section">
                <h2>Detailed Information</h2>
                <pre>{debug_info}</pre>
            </div>
            
            <div class="section">
                <h2>Performance Tips</h2>
                <ul>
                    <li>Clear browser cache (Ctrl+Shift+Delete)</li>
                    <li>Disable browser extensions</li>
                    <li>Check network connection</li>
                    <li>Restart the Flask server</li>
                </ul>
            </div>
        </body>
        </html>
        """
    
    @app.route('/debug/clear-cache')
    def clear_cache():
        """Route to force cache clearing."""
        import os
        cache_dir = os.path.join(app.instance_path, 'cache')
        if os.path.exists(cache_dir):
            import shutil
            shutil.rmtree(cache_dir)
            os.makedirs(cache_dir)
            return "Cache cleared successfully"
        return "No cache to clear"
    
    return app
