/* Mobile Responsive Dashboard CSS - Subject Teacher Page */
/* Designed for smartphones and tablets - Progressive Enhancement */

/* Base Mobile-First Approach */
* {
  box-sizing: border-box;
}

/* Mobile Navigation Improvements */
@media screen and (max-width: 768px) {
  /* Header and Navigation */
  .dashboard-header {
    padding: 1rem !important;
    text-align: center;
  }

  .dashboard-header h1 {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
    line-height: 1.2;
  }

  .dashboard-header .subtitle {
    font-size: 0.9rem !important;
    margin-bottom: 1rem !important;
  }

  /* Navigation Links */
  .nav-links {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 0.5rem !important;
    justify-content: center !important;
    margin-top: 1rem !important;
  }

  .nav-links a {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.85rem !important;
    border-radius: 6px !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    min-width: auto !important;
    flex: 0 0 auto !important;
  }

  /* Main Container */
  .dashboard-container {
    padding: 1rem !important;
    margin: 0 !important;
    max-width: 100% !important;
  }

  /* Form Sections */
  .form-section {
    margin-bottom: 1.5rem !important;
    padding: 1rem !important;
    border-radius: 8px !important;
    background: rgba(255, 255, 255, 0.95) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }

  .form-section h3 {
    font-size: 1.1rem !important;
    margin-bottom: 1rem !important;
    color: #1e293b !important;
    font-weight: 600 !important;
  }

  /* Form Groups */
  .form-group {
    margin-bottom: 1rem !important;
  }

  .form-group label {
    display: block !important;
    margin-bottom: 0.5rem !important;
    font-weight: 500 !important;
    font-size: 0.9rem !important;
    color: #374151 !important;
  }

  /* Form Controls */
  .form-control,
  select,
  input[type="text"],
  input[type="number"],
  textarea {
    width: 100% !important;
    padding: 0.75rem !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    font-size: 1rem !important;
    background: white !important;
    color: #1f2937 !important;
    -webkit-appearance: none !important;
    appearance: none !important;
  }

  /* Select Dropdowns - Enhanced Mobile Styling */
  select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
    background-position: right 0.5rem center !important;
    background-repeat: no-repeat !important;
    background-size: 1.5em 1.5em !important;
    padding-right: 2.5rem !important;
    cursor: pointer !important;
    position: relative !important;
    z-index: 1 !important;
    max-width: 100% !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }

  /* Mobile Select Dropdown Options Styling */
  select option {
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    line-height: 1.5 !important;
    background: white !important;
    color: #1f2937 !important;
    border: none !important;
    min-height: 44px !important;
    display: block !important;
  }

  /* Enhanced Select Focus State */
  select:focus {
    outline: 2px solid #3b82f6 !important;
    outline-offset: 2px !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    z-index: 10 !important;
  }

  /* Select Dropdown Container */
  .form-group select {
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Mobile-specific dropdown improvements */
  @media (max-width: 768px) {
    select {
      font-size: 16px !important; /* Prevents zoom on iOS */
      -webkit-appearance: none !important;
      -moz-appearance: none !important;
      appearance: none !important;
    }

    /* Ensure dropdown options are readable */
    select option {
      font-size: 16px !important;
      padding: 12px 16px !important;
      background-color: white !important;
      color: #1f2937 !important;
    }

    /* Style for selected option */
    select option:checked {
      background-color: #3b82f6 !important;
      color: white !important;
    }

    /* Hover state for options */
    select option:hover {
      background-color: #f3f4f6 !important;
    }

    /* Prevent dropdown overflow */
    .form-group {
      position: relative !important;
      overflow: visible !important;
    }

    /* Ensure dropdowns stay within viewport */
    select {
      max-height: 200px !important;
      overflow-y: auto !important;
    }
  }

  /* Buttons */
  .btn,
  button,
  input[type="submit"] {
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    margin-bottom: 0.5rem !important;
    border: none !important;
    border-radius: 6px !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    text-align: center !important;
    display: block !important;
  }

  .btn-primary {
    background: #3b82f6 !important;
    color: white !important;
  }

  .btn-primary:hover {
    background: #2563eb !important;
  }

  .btn-success {
    background: #10b981 !important;
    color: white !important;
  }

  .btn-success:hover {
    background: #059669 !important;
  }

  .btn-secondary {
    background: #6b7280 !important;
    color: white !important;
  }

  .btn-secondary:hover {
    background: #4b5563 !important;
  }

  /* Button Groups */
  .button-group {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
    margin-top: 1rem !important;
  }

  /* Tables */
  .table-responsive {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
    margin-bottom: 1rem !important;
  }

  table {
    width: 100% !important;
    min-width: 600px !important;
    font-size: 0.85rem !important;
  }

  table th,
  table td {
    padding: 0.5rem !important;
    text-align: left !important;
    border-bottom: 1px solid #e5e7eb !important;
  }

  table th {
    background: #f9fafb !important;
    font-weight: 600 !important;
    color: #374151 !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
  }

  /* Cards */
  .card {
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: 8px !important;
    padding: 1rem !important;
    margin-bottom: 1rem !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e5e7eb !important;
  }

  .card-header {
    margin-bottom: 1rem !important;
    padding-bottom: 0.5rem !important;
    border-bottom: 1px solid #e5e7eb !important;
  }

  .card-header h4 {
    font-size: 1rem !important;
    margin: 0 !important;
    color: #1e293b !important;
    font-weight: 600 !important;
  }

  /* Alert Messages */
  .alert {
    padding: 0.75rem !important;
    margin-bottom: 1rem !important;
    border-radius: 6px !important;
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
  }

  .alert-success {
    background: #d1fae5 !important;
    color: #065f46 !important;
    border: 1px solid #a7f3d0 !important;
  }

  .alert-error,
  .alert-danger {
    background: #fee2e2 !important;
    color: #991b1b !important;
    border: 1px solid #fca5a5 !important;
  }

  .alert-info {
    background: #dbeafe !important;
    color: #1e40af !important;
    border: 1px solid #93c5fd !important;
  }

  /* Loading States */
  .loading {
    text-align: center !important;
    padding: 2rem !important;
    color: #6b7280 !important;
  }

  /* Touch Improvements */
  .touch-target {
    min-height: 44px !important;
    min-width: 44px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* Spacing Utilities */
  .mb-mobile {
    margin-bottom: 1rem !important;
  }

  .mt-mobile {
    margin-top: 1rem !important;
  }

  .p-mobile {
    padding: 1rem !important;
  }

  /* Hide on Mobile */
  .hide-mobile {
    display: none !important;
  }

  /* Show only on Mobile */
  .show-mobile {
    display: block !important;
  }
}

/* Tablet Adjustments */
@media screen and (min-width: 769px) and (max-width: 1024px) {
  .dashboard-container {
    padding: 1.5rem !important;
  }

  .form-section {
    padding: 1.5rem !important;
  }

  .btn {
    width: auto !important;
    min-width: 120px !important;
    display: inline-block !important;
    margin-right: 0.5rem !important;
  }

  .button-group {
    flex-direction: row !important;
    flex-wrap: wrap !important;
  }
}

/* Large Mobile Devices */
@media screen and (max-width: 480px) {
  .dashboard-header h1 {
    font-size: 1.25rem !important;
  }

  .nav-links a {
    font-size: 0.8rem !important;
    padding: 0.4rem 0.6rem !important;
  }

  .form-control,
  select,
  input {
    font-size: 16px !important; /* Prevents zoom on iOS */
  }

  table {
    min-width: 500px !important;
    font-size: 0.8rem !important;
  }

  table th,
  table td {
    padding: 0.4rem !important;
  }
}

/* Mobile-Specific Enhancements */

/* Scroll Indicators for Tables */
.table-responsive {
  position: relative !important;
}

.scroll-indicator {
  position: absolute !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  background: rgba(0, 0, 0, 0.7) !important;
  color: white !important;
  padding: 0.5rem !important;
  border-radius: 50% !important;
  font-size: 1.2rem !important;
  z-index: 100 !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
  pointer-events: none !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.scroll-indicator-left {
  left: 10px !important;
}

.scroll-indicator-right {
  right: 10px !important;
}

/* Mobile Alert Styles */
.mobile-alert {
  animation: slideInFromTop 0.3s ease-out !important;
}

@keyframes slideInFromTop {
  from {
    transform: translateY(-100%) !important;
    opacity: 0 !important;
  }
  to {
    transform: translateY(0) !important;
    opacity: 1 !important;
  }
}

/* Touch Feedback */
.touch-feedback {
  transition: transform 0.1s ease, opacity 0.1s ease !important;
}

.touch-feedback:active {
  transform: scale(0.98) !important;
  opacity: 0.8 !important;
}

/* Improved Focus States for Mobile */
@media screen and (max-width: 768px) {
  input:focus,
  select:focus,
  textarea:focus,
  button:focus {
    outline: 2px solid #3b82f6 !important;
    outline-offset: 2px !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  }
}

/* Landscape Orientation Adjustments */
@media screen and (max-width: 768px) and (orientation: landscape) {
  .dashboard-header {
    padding: 0.75rem !important;
  }

  .dashboard-header h1 {
    font-size: 1.25rem !important;
  }

  .form-section {
    padding: 0.75rem !important;
  }

  .nav-links {
    margin-top: 0.5rem !important;
  }
}

/* High DPI Display Adjustments */
@media screen and (max-width: 768px) and (-webkit-min-device-pixel-ratio: 2) {
  .form-control,
  select,
  input,
  textarea {
    border-width: 0.5px !important;
  }

  .btn {
    border-width: 0.5px !important;
  }
}

/* Dark Mode Support for Mobile */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
  .form-section {
    background: rgba(31, 41, 55, 0.95) !important;
    color: #f9fafb !important;
  }

  .form-control,
  select,
  input,
  textarea {
    background: #374151 !important;
    color: #f9fafb !important;
    border-color: #4b5563 !important;
  }

  .card {
    background: rgba(31, 41, 55, 0.95) !important;
    color: #f9fafb !important;
    border-color: #4b5563 !important;
  }
}

/* Accessibility Improvements */
@media screen and (max-width: 768px) {
  /* Larger touch targets for accessibility */
  .btn,
  button,
  input[type="submit"],
  select,
  input {
    min-height: 44px !important;
  }

  /* Better contrast for links */
  a {
    color: #2563eb !important;
    text-decoration: underline !important;
  }

  a:visited {
    color: #7c3aed !important;
  }

  /* Improved readability */
  body {
    line-height: 1.6 !important;
  }

  p,
  li {
    font-size: 1rem !important;
    line-height: 1.5 !important;
  }
}

/* Performance Optimizations */
@media screen and (max-width: 768px) {
  * {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1) !important;
    -webkit-touch-callout: none !important;
  }

  img {
    max-width: 100% !important;
    height: auto !important;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth !important;
  }

  /* Optimize animations for mobile */
  * {
    animation-duration: 0.2s !important;
    transition-duration: 0.2s !important;
  }
}

/* Upload Marks Page Specific Styling */
@media screen and (max-width: 768px) {
  /* Upload marks form container */
  .upload-marks-container {
    padding: 1rem !important;
    margin: 0 !important;
  }

  /* Education level, subject, and other dropdowns */
  .upload-marks-container select {
    width: 100% !important;
    margin-bottom: 1rem !important;
    font-size: 16px !important;
    padding: 0.75rem !important;
    border-radius: 8px !important;
    border: 1px solid #d1d5db !important;
    background-color: white !important;
  }

  /* Dropdown labels */
  .upload-marks-container label {
    font-weight: 600 !important;
    margin-bottom: 0.5rem !important;
    display: block !important;
    color: #374151 !important;
  }

  /* Form sections spacing */
  .upload-marks-container .form-group {
    margin-bottom: 1.5rem !important;
  }

  /* Upload button styling */
  .upload-marks-container .btn-primary {
    background-color: #3b82f6 !important;
    border: none !important;
    padding: 1rem !important;
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    width: 100% !important;
    margin-top: 1rem !important;
  }

  /* Debug toggle button */
  .upload-marks-container .debug-toggle {
    font-size: 0.9rem !important;
    padding: 0.5rem 1rem !important;
    margin-bottom: 1rem !important;
  }

  /* Custom Mobile Dropdown Styling */
  .mobile-select-wrapper {
    position: relative !important;
    width: 100% !important;
  }

  .mobile-select-button {
    width: 100% !important;
    padding: 0.75rem 2.5rem 0.75rem 0.75rem !important;
    border: 1px solid #d1d5db !important;
    border-radius: 8px !important;
    background: white !important;
    font-size: 16px !important;
    text-align: left !important;
    cursor: pointer !important;
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
  }

  .mobile-select-button::after {
    content: "▼" !important;
    position: absolute !important;
    right: 0.75rem !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    font-size: 0.8rem !important;
    color: #6b7280 !important;
  }

  .mobile-select-button.active::after {
    content: "▲" !important;
  }

  .mobile-select-dropdown {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    background: white !important;
    border: 1px solid #d1d5db !important;
    border-top: none !important;
    border-radius: 0 0 8px 8px !important;
    max-height: 200px !important;
    overflow-y: auto !important;
    z-index: 1000 !important;
    display: none !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
  }

  .mobile-select-dropdown.show {
    display: block !important;
  }

  .mobile-select-option {
    padding: 0.75rem !important;
    cursor: pointer !important;
    border-bottom: 1px solid #f3f4f6 !important;
    font-size: 16px !important;
    transition: background-color 0.2s !important;
  }

  .mobile-select-option:hover {
    background-color: #f3f4f6 !important;
  }

  .mobile-select-option:last-child {
    border-bottom: none !important;
  }

  .mobile-select-option.selected {
    background-color: #3b82f6 !important;
    color: white !important;
  }

  /* Hide native select on mobile when custom dropdown is active */
  .mobile-select-wrapper .native-select {
    display: none !important;
  }
}
