<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=yes, maximum-scale=5.0"
    />
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <meta
      name="description"
      content="Class Teacher Dashboard - {{ school_info.school_name|default('Hillview School') }} Management System"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="theme-color" content="#3b82f6" />

    <title>
      Class Teacher Dashboard - {{ school_info.school_name|default('Hillview
      School Management System') }}
    </title>

    <!-- Modern Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Font Awesome Icons -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />

    <style>
      /* 🎨 PREMIUM CLASSTEACHER COLOR SCHEME - REFINED */
      :root {
        /* Professional color scheme */
        --text-color: #1e293b;
        --text-secondary: #64748b;
        --bg-color: #f8fafc;
        --primary-color: #3b82f6;
        --secondary-color: #ef4444;
        --accent-color: #f59e0b;
        --success-color: #10b981;

        /* Extended palette for glassmorphism */
        --glass-bg: rgba(255, 255, 255, 0.85);
        --glass-border: rgba(59, 130, 246, 0.15);
        --glass-shadow: rgba(59, 130, 246, 0.08);
        --card-bg: rgba(255, 255, 255, 0.95);
        --card-hover: rgba(59, 130, 246, 0.05);

        /* Dark mode support */
        --dark-bg: #0f172a;
        --dark-text: #f1f5f9;
        --dark-glass: rgba(15, 23, 42, 0.85);
      }

      /* 🌟 MODERN GLASSMORPHISM BASE */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Inter", system-ui, -apple-system, sans-serif;
        background: linear-gradient(
          135deg,
          var(--bg-color) 0%,
          #f1f5f9 50%,
          var(--bg-color) 100%
        );
        color: var(--text-color);
        min-height: 100vh;
        line-height: 1.6;
      }

      /* 🏗️ LAYOUT STRUCTURE */
      .premium-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 2rem;
        min-height: 100vh;
      }

      /* 📱 MOBILE RESPONSIVE */
      @media (max-width: 768px) {
        .premium-container {
          padding: 1rem;
        }
      }

      /* 🎯 PREMIUM HEADER */
      .premium-header {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: 24px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px var(--glass-shadow);
        position: relative;
        overflow: hidden;
      }

      .premium-header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(
          90deg,
          var(--primary-color),
          var(--success-color),
          var(--primary-color)
        );
        border-radius: 24px 24px 0 0;
      }

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
      }

      .header-info h1 {
        font-size: 2.5rem;
        font-weight: 800;
        color: var(--text-color);
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 255, 250, 0.1);
      }

      .header-info .subtitle {
        font-size: 1.1rem;
        color: var(--text-color);
        opacity: 0.8;
      }

      .header-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
      }

      .premium-badge {
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--success-color)
        );
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.9rem;
        box-shadow: 0 4px 12px rgba(8, 255, 168, 0.3);
      }

      /* 📊 STATS CARDS GRID */
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
      }

      .stat-card {
        background: var(--card-bg);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: 20px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 40px var(--glass-shadow);
        background: var(--card-hover);
      }

      .stat-card .icon {
        width: 60px;
        height: 60px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--success-color)
        );
        color: white;
      }

      .stat-card .value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-color);
        margin-bottom: 0.5rem;
      }

      .stat-card .label {
        color: var(--text-color);
        opacity: 0.7;
        font-weight: 500;
      }

      /* 🚀 ACTION CARDS */
      .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
      }

      .action-card {
        background: var(--card-bg);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: 20px;
        padding: 2rem;
        text-decoration: none;
        color: inherit;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .action-card:hover {
        transform: translateY(-6px);
        box-shadow: 0 16px 48px var(--glass-shadow);
        background: var(--card-hover);
        text-decoration: none;
        color: inherit;
      }

      .action-card .icon {
        width: 70px;
        height: 70px;
        border-radius: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--success-color)
        );
        color: white;
      }

      .action-card h3 {
        font-size: 1.4rem;
        font-weight: 700;
        color: var(--text-color);
        margin-bottom: 0.5rem;
      }

      .action-card p {
        color: var(--text-color);
        opacity: 0.8;
        line-height: 1.5;
      }

      /* 🎨 ANIMATIONS */
      .fade-in {
        animation: fadeIn 0.6s ease-out;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .slide-up {
        animation: slideUp 0.8s ease-out;
      }

      @keyframes slideUp {
        from {
          opacity: 0;
          transform: translateY(40px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 📱 COMPREHENSIVE MOBILE RESPONSIVENESS */

      /* Mobile First - Small devices (phones) */
      @media (max-width: 480px) {
        .premium-container {
          padding: 1rem;
          margin: 0;
          min-height: 100vh;
        }

        .premium-header {
          padding: 1.5rem;
          border-radius: 16px;
          margin-bottom: 1.5rem;
        }

        .header-content {
          flex-direction: column;
          gap: 1rem;
          text-align: center;
        }

        .header-info h1 {
          font-size: 1.6rem;
          margin-bottom: 0.5rem;
        }

        .header-info h1 i {
          font-size: 1.4rem;
          margin-right: 0.5rem;
        }

        .subtitle {
          font-size: 0.9rem;
          line-height: 1.4;
        }

        .premium-badge {
          font-size: 0.8rem;
          padding: 0.4rem 0.8rem;
          margin-top: 0.5rem;
          display: inline-block;
        }

        /* Stats Grid - 2x2 layout on mobile */
        .stats-grid {
          grid-template-columns: 1fr 1fr;
          gap: 1rem;
          margin-bottom: 1.5rem;
        }

        .stat-card {
          padding: 1.2rem;
          border-radius: 16px;
        }

        .stat-card .icon {
          width: 50px;
          height: 50px;
          font-size: 1.3rem;
          margin-bottom: 0.8rem;
        }

        .stat-card .value {
          font-size: 1.6rem;
          margin-bottom: 0.3rem;
        }

        .stat-card .label {
          font-size: 0.8rem;
          font-weight: 600;
        }

        /* Actions Grid - Single column */
        .actions-grid {
          grid-template-columns: 1fr;
          gap: 1rem;
        }

        .action-card {
          padding: 1.5rem;
          border-radius: 16px;
        }

        .action-card .icon {
          width: 60px;
          height: 60px;
          font-size: 1.5rem;
          margin-bottom: 1rem;
        }

        .action-card h3 {
          font-size: 1.2rem;
          margin-bottom: 0.5rem;
        }

        .action-card p {
          font-size: 0.9rem;
          line-height: 1.4;
        }

        /* Touch-friendly hover effects */
        .action-card:active {
          transform: translateY(-2px) scale(0.98);
        }
      }

      /* Tablet devices */
      @media (min-width: 481px) and (max-width: 768px) {
        .premium-container {
          padding: 1.5rem;
        }

        .header-content {
          flex-direction: column;
          text-align: center;
          gap: 1.5rem;
        }

        .header-info h1 {
          font-size: 2rem;
        }

        /* Stats Grid - 2x2 layout */
        .stats-grid {
          grid-template-columns: 1fr 1fr;
          gap: 1.2rem;
        }

        /* Actions Grid - 2 columns */
        .actions-grid {
          grid-template-columns: 1fr 1fr;
          gap: 1.2rem;
        }

        .action-card {
          padding: 1.8rem;
        }
      }

      /* Large tablets and small desktops */
      @media (min-width: 769px) and (max-width: 1024px) {
        .stats-grid {
          grid-template-columns: repeat(4, 1fr);
          gap: 1.2rem;
        }

        .actions-grid {
          grid-template-columns: repeat(2, 1fr);
          gap: 1.5rem;
        }
      }

      /* Touch device optimizations */
      @media (hover: none) and (pointer: coarse) {
        .action-card:hover {
          transform: none;
        }

        .action-card:active {
          transform: translateY(-4px) scale(0.98);
          transition: transform 0.1s ease;
        }

        /* Larger touch targets */
        .premium-badge {
          min-height: 44px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      /* Landscape orientation on mobile */
      @media (max-width: 768px) and (orientation: landscape) {
        .premium-header {
          padding: 1rem 1.5rem;
        }

        .header-info h1 {
          font-size: 1.4rem;
        }

        .stats-grid {
          grid-template-columns: repeat(4, 1fr);
          gap: 0.8rem;
        }

        .stat-card {
          padding: 1rem;
        }

        .stat-card .value {
          font-size: 1.4rem;
        }

        .actions-grid {
          grid-template-columns: repeat(2, 1fr);
          gap: 1rem;
        }

        .action-card {
          padding: 1.2rem;
        }
      }

      /* 🖥️ DESKTOP - HIDE MOBILE MENU COMPLETELY */
      @media (min-width: 769px) {
        .mobile-menu {
          display: none !important; /* Force hide on desktop */
        }

        .mobile-nav-toggle {
          display: none !important; /* Force hide hamburger on desktop */
        }
      }

      /* 🎯 PREMIUM TABS STYLING */
      .premium-tab-button {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 16px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 16px;
        text-align: left;
        color: var(--text-color);
        position: relative;
        overflow: hidden;
      }

      .premium-tab-button:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(0, 255, 250, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 255, 250, 0.15);
      }

      .premium-tab-button.active {
        background: linear-gradient(135deg, rgba(0, 255, 250, 0.15), rgba(8, 255, 168, 0.15));
        border-color: rgba(0, 255, 250, 0.5);
        box-shadow: 0 8px 25px rgba(0, 255, 250, 0.2);
      }

      .tab-icon {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #00fffa, #08ffa8);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        flex-shrink: 0;
      }

      .tab-content {
        flex: 1;
      }

      .tab-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 4px;
        color: var(--text-color);
      }

      .tab-subtitle {
        font-size: 14px;
        opacity: 0.7;
        color: var(--text-color);
      }

      /* Tab Content Containers - CONSOLIDATED */
      .tab-content-container {
        display: none;
        animation: fadeInUp 0.5s ease-out;
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: 20px;
        padding: 24px;
        margin: 24px 0;
        backdrop-filter: var(--glass-backdrop);
        box-shadow: var(--shadow-lg);
      }

      .tab-content-container.active {
        display: block;
      }

      /* Premium Form Styling */
      .premium-form .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--text-color);
        font-size: 14px;
      }

      .premium-form .form-select,
      .premium-form .form-input {
        width: 100%;
        padding: 12px 16px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        color: var(--text-color);
        font-size: 14px;
        transition: all 0.3s ease;
      }

      .premium-form .form-select:focus,
      .premium-form .form-input:focus {
        outline: none;
        border-color: rgba(0, 255, 250, 0.5);
        box-shadow: 0 0 0 3px rgba(0, 255, 250, 0.1);
        background: rgba(255, 255, 255, 0.08);
      }

      .premium-btn {
        padding: 12px 24px;
        border-radius: 12px;
        font-weight: 500;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
        border: none;
      }

      .premium-btn.btn-primary {
        background: linear-gradient(135deg, #00fffa, #08ffa8);
        color: white;
      }

      .premium-btn.btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 255, 250, 0.3);
      }

      .premium-btn.btn-outline {
        background: transparent;
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: var(--text-color);
      }

      .premium-btn.btn-outline:hover {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(0, 255, 250, 0.3);
      }

      .premium-btn.btn-success {
        background: linear-gradient(135deg, #08ffa8, #00ff88);
        color: white;
      }

      .premium-btn.btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(8, 255, 168, 0.3);
      }

      /* 📑 TAB CONTENT SECTIONS - REMOVED DUPLICATE */

      /* 🎯 TAB BUTTON STYLES */
      .premium-tab-button {
        transition: all 0.3s ease;
      }

      .premium-tab-button.active {
        background: rgba(8, 255, 168, 0.2) !important;
        border-color: rgba(8, 255, 168, 0.5) !important;
        color: var(--text-color) !important;
        box-shadow: 0 4px 12px rgba(8, 255, 168, 0.2);
      }

      .premium-tab-button:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        border-color: rgba(8, 255, 168, 0.3) !important;
      }

      /* Management Cards */
      .management-card:hover {
        background: rgba(255, 255, 255, 0.08) !important;
        border-color: rgba(0, 255, 250, 0.3) !important;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 255, 250, 0.15);
      }

      /* Mobile Responsive */
      @media (max-width: 768px) {
        .form-grid {
          grid-template-columns: 1fr !important;
        }

        .reports-grid,
        .management-grid {
          grid-template-columns: 1fr !important;
        }

        .premium-tab-nav {
          grid-template-columns: 1fr !important;
        }

        .premium-tab-button {
          padding: 16px !important;
        }

        .tab-icon {
          width: 40px !important;
          height: 40px !important;
          font-size: 18px !important;
        }
      }

      /* 🌙 DARK MODE SUPPORT */
      @media (prefers-color-scheme: dark) {
        :root {
          --bg-color: var(--dark-bg);
          --glass-bg: var(--dark-glass);
          --card-bg: rgba(255, 255, 255, 0.05);
        }
      }

      /* 📱 MOBILE NAVIGATION ENHANCEMENTS */
      .mobile-nav-toggle {
        display: none; /* Hidden by default on desktop */
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        font-size: 1.5rem;
        color: white;
        cursor: pointer;
        padding: 0.8rem;
        border-radius: 12px;
        transition: all 0.3s ease;
        min-width: 48px;
        min-height: 48px;
        align-items: center;
        justify-content: center;
      }

      .mobile-nav-toggle:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.05);
      }

      .mobile-nav-toggle:active {
        transform: scale(0.95);
      }

      @media (max-width: 768px) {
        .mobile-nav-toggle {
          display: flex !important; /* FORCE SHOW ON MOBILE */
          position: fixed;
          top: 1rem;
          right: 1rem;
          z-index: 1001;
        }

        .desktop-only {
          display: none !important;
        }

        .header-actions {
          position: relative;
        }

        /* Mobile menu overlay - HIDDEN BY DEFAULT */
        .mobile-menu {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(59, 130, 246, 0.98);
          backdrop-filter: blur(20px);
          z-index: 9999;
          display: none; /* Completely hidden by default */
          flex-direction: column;
          justify-content: center;
          align-items: center;
          gap: 1.5rem;
          opacity: 0;
          visibility: hidden;
          transition: all 0.3s ease;
          padding: 2rem;
          overflow-y: auto;
        }

        .mobile-menu.show {
          display: flex !important; /* Show when toggled */
          opacity: 1;
          visibility: visible;
        }

        .mobile-menu-close {
          position: absolute;
          top: 2rem;
          right: 2rem;
          background: none;
          border: none;
          color: white;
          font-size: 2rem;
          cursor: pointer;
          padding: 0.5rem;
          border-radius: 50%;
          transition: background-color 0.3s ease;
        }

        .mobile-menu-close:hover {
          background: rgba(255, 255, 255, 0.1);
        }

        .mobile-menu-item {
          color: white;
          text-decoration: none;
          font-size: 1.2rem;
          font-weight: 600;
          padding: 1rem 2rem;
          border-radius: 12px;
          background: rgba(255, 255, 255, 0.1);
          border: 2px solid rgba(255, 255, 255, 0.2);
          transition: all 0.3s ease;
          min-width: 200px;
          text-align: center;
        }

        .mobile-menu-item:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
          color: white;
          text-decoration: none;
        }
      }

      /* 🔧 PERFORMANCE OPTIMIZATIONS */
      .premium-container {
        will-change: transform;
      }

      .action-card {
        will-change: transform;
      }

      /* Reduce motion for users who prefer it */
      @media (prefers-reduced-motion: reduce) {
        * {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }

      /* 🎯 ACCESSIBILITY IMPROVEMENTS */
      .action-card:focus {
        outline: 3px solid var(--primary-color);
        outline-offset: 2px;
      }

      .premium-badge:focus {
        outline: 2px solid rgba(255, 255, 255, 0.8);
        outline-offset: 2px;
      }

      /* High contrast mode support */
      @media (prefers-contrast: high) {
        .action-card {
          border: 2px solid var(--text-color);
        }

        .stat-card {
          border: 2px solid var(--text-color);
        }
      }
    </style>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar" style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1rem 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        border-radius: 12px;
    ">
        <a href="#" class="navbar-brand" style="
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        ">
            {% if school_info.logo_url and school_info.logo_url != '/static/images/default_logo.png' %}
            <img
                src="{{ school_info.logo_url }}"
                alt="School Logo"
                style="
                    width: 32px;
                    height: 32px;
                    border-radius: 4px;
                    object-fit: cover;
                    margin-right: 8px;
                "
            />
            {% endif %}
            {{ school_info.school_name or 'Hillview School' }} - Class Teacher
        </a>
        <ul class="navbar-nav" style="
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: 1rem;
            align-items: center;
        ">
            <li>
                <a href="#" onclick="switchMainTab('upload-marks'); return false;" class="nav-link" style="
                    color: rgba(255, 255, 255, 0.9);
                    text-decoration: none;
                    font-weight: 500;
                    padding: 0.5rem 1rem;
                    border-radius: 0.5rem;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                ">
                    <i class="fas fa-upload"></i> <span>Upload Marks</span>
                </a>
            </li>
            <li>
                <a href="#" onclick="switchMainTab('recent-reports'); return false;" class="nav-link" style="
                    color: rgba(255, 255, 255, 0.9);
                    text-decoration: none;
                    font-weight: 500;
                    padding: 0.5rem 1rem;
                    border-radius: 0.5rem;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                ">
                    <i class="fas fa-chart-line"></i> <span>Recent Reports</span>
                </a>
            </li>
            <li>
                <a href="{{ url_for('classteacher.analytics_dashboard') }}" class="nav-link" style="
                    color: rgba(255, 255, 255, 0.9);
                    text-decoration: none;
                    font-weight: 500;
                    padding: 0.5rem 1rem;
                    border-radius: 0.5rem;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                ">
                    <i class="fas fa-chart-pie"></i> <span>Analytics</span>
                </a>
            </li>
            <li>
                <a href="{{ url_for('auth.logout_route') }}" class="logout-btn" style="
                    color: rgba(255, 255, 255, 0.9);
                    text-decoration: none;
                    font-weight: 500;
                    padding: 0.5rem 1rem;
                    border-radius: 0.5rem;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    background: rgba(220, 53, 69, 0.2);
                    border: 1px solid rgba(220, 53, 69, 0.3);
                ">
                    <i class="fas fa-sign-out-alt"></i> <span>Logout</span>
                </a>
            </li>
        </ul>
    </nav>

    <div class="premium-container">
      <!-- Premium Header -->
      <header class="premium-header fade-in">
        <div class="header-content">
          <div class="header-info">
            <h1>
              <i class="fas fa-chalkboard-teacher"></i>
              Class Teacher Dashboard
            </h1>
            <p class="subtitle">
              Welcome back,
              <strong>{{ session.get('username', 'Teacher') }}</strong>!
              <span class="premium-badge">
                <i class="fas fa-star"></i>
                {{ session.get('role', 'teacher').replace('_', ' ').title() }}
              </span>
            </p>
          </div>
          <div class="header-actions">
            <!-- Desktop logout button -->
            <a
              href="{{ url_for('auth.logout_route') }}"
              class="premium-badge desktop-only"
              style="text-decoration: none; color: inherit"
            >
              <i class="fas fa-sign-out-alt"></i>
              Logout
            </a>

            <!-- Mobile menu toggle -->
            <button
              class="mobile-nav-toggle"
              onclick="toggleMobileMenu()"
              aria-label="Open menu"
            >
              <i class="fas fa-bars"></i>
            </button>
          </div>
        </div>
      </header>

      <!-- Role-Based Assignments Section -->
      {% if assignment_summary and assignment_summary.teacher %}
      <div class="assignments-overview slide-up" style="
          background: var(--glass-bg);
          border: 1px solid var(--glass-border);
          border-radius: 20px;
          padding: 24px;
          margin: 24px 0;
          backdrop-filter: var(--glass-backdrop);
          box-shadow: var(--shadow-lg);
      ">
          <div class="section-header" style="margin-bottom: 24px;">
              <h3 style="margin: 0 0 8px 0; color: var(--text-color); display: flex; align-items: center; gap: 12px;">
                  <i class="fas fa-user-graduate" style="color: #08ffa8;"></i>
                  My Teaching Assignments
              </h3>
              <div style="display: flex; align-items: center; gap: 12px;">
                  <span style="
                      background: rgba(8, 255, 168, 0.2);
                      color: #08ffa8;
                      padding: 4px 12px;
                      border-radius: 20px;
                      font-size: 14px;
                      font-weight: 600;
                  ">
                      {{ assignment_summary.role.title() }}
                  </span>
              </div>
          </div>

          <!-- Assignment Summary Stats -->
          <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px;">
              <div class="stat-card" style="
                  background: rgba(8, 255, 168, 0.1);
                  border: 1px solid rgba(8, 255, 168, 0.2);
                  border-radius: 12px;
                  padding: 20px;
                  text-align: center;
              ">
                  <div class="stat-icon" style="
                      width: 48px;
                      height: 48px;
                      background: linear-gradient(135deg, #08ffa8, #00fffa);
                      border-radius: 12px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin: 0 auto 12px;
                      font-size: 20px;
                      color: white;
                  ">
                      <i class="fas fa-book"></i>
                  </div>
                  <div class="stat-number" style="font-size: 28px; font-weight: 700; color: #08ffa8; margin-bottom: 4px;">
                      {{ subject_assignments|length if subject_assignments else 0 }}
                  </div>
                  <div class="stat-label" style="color: var(--text-color); opacity: 0.8; font-size: 14px;">
                      Subjects Taught
                  </div>
              </div>

              {% if can_manage_classes %}
              <div class="stat-card" style="
                  background: rgba(0, 255, 250, 0.1);
                  border: 1px solid rgba(0, 255, 250, 0.2);
                  border-radius: 12px;
                  padding: 20px;
                  text-align: center;
              ">
                  <div class="stat-icon" style="
                      width: 48px;
                      height: 48px;
                      background: linear-gradient(135deg, #00fffa, #08ffa8);
                      border-radius: 12px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin: 0 auto 12px;
                      font-size: 20px;
                      color: white;
                  ">
                      <i class="fas fa-chalkboard"></i>
                  </div>
                  <div class="stat-number" style="font-size: 28px; font-weight: 700; color: #00fffa; margin-bottom: 4px;">
                      {{ class_teacher_assignments|length if class_teacher_assignments else 0 }}
                  </div>
                  <div class="stat-label" style="color: var(--text-color); opacity: 0.8; font-size: 14px;">
                      Classes Managed
                  </div>
              </div>
              {% endif %}

              <div class="stat-card" style="
                  background: rgba(246, 231, 28, 0.1);
                  border: 1px solid rgba(246, 231, 28, 0.2);
                  border-radius: 12px;
                  padding: 20px;
                  text-align: center;
              ">
                  <div class="stat-icon" style="
                      width: 48px;
                      height: 48px;
                      background: linear-gradient(135deg, #f6e71c, #ffd700);
                      border-radius: 12px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin: 0 auto 12px;
                      font-size: 20px;
                      color: white;
                  ">
                      <i class="fas fa-layer-group"></i>
                  </div>
                  <div class="stat-number" style="font-size: 28px; font-weight: 700; color: #f6e71c; margin-bottom: 4px;">
                      {{ assignment_summary.grades_involved|length if assignment_summary.grades_involved else 0 }}
                  </div>
                  <div class="stat-label" style="color: var(--text-color); opacity: 0.8; font-size: 14px;">
                      Grades Involved
                  </div>
              </div>

              <div class="stat-card" style="
                  background: rgba(210, 28, 0, 0.1);
                  border: 1px solid rgba(210, 28, 0, 0.2);
                  border-radius: 12px;
                  padding: 20px;
                  text-align: center;
              ">
                  <div class="stat-icon" style="
                      width: 48px;
                      height: 48px;
                      background: linear-gradient(135deg, #d21c00, #ff4444);
                      border-radius: 12px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin: 0 auto 12px;
                      font-size: 20px;
                      color: white;
                  ">
                      <i class="fas fa-stream"></i>
                  </div>
                  <div class="stat-number" style="font-size: 28px; font-weight: 700; color: #d21c00; margin-bottom: 4px;">
                      {{ assignment_summary.streams_involved|length if assignment_summary.streams_involved else 0 }}
                  </div>
                  <div class="stat-label" style="color: var(--text-color); opacity: 0.8; font-size: 14px;">
                      Streams Involved
                  </div>
              </div>
          </div>

          <!-- Detailed Subject Assignments -->
          {% if subject_assignments %}
          <div style="margin-bottom: 24px;">
              <h4 style="margin: 0 0 16px 0; color: var(--text-color); display: flex; align-items: center; gap: 12px;">
                  <i class="fas fa-book-open" style="color: #08ffa8;"></i>
                  My Subject Teaching Assignments
                  <span style="
                      background: rgba(8, 255, 168, 0.2);
                      color: #08ffa8;
                      padding: 4px 12px;
                      border-radius: 20px;
                      font-size: 12px;
                      font-weight: 600;
                  ">
                      {{ subject_assignments|length }} Total
                  </span>
              </h4>

              <div class="subjects-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 12px;">
                  {% for assignment in subject_assignments %}
                  <div class="subject-assignment-card" style="
                      background: rgba(255, 255, 255, 0.05);
                      border: 1px solid var(--glass-border);
                      border-radius: 12px;
                      padding: 16px;
                      display: flex;
                      align-items: center;
                      gap: 12px;
                  ">
                      <div class="subject-icon" style="
                          width: 40px;
                          height: 40px;
                          background: linear-gradient(135deg, #08ffa8, #00fffa);
                          border-radius: 8px;
                          display: flex;
                          align-items: center;
                          justify-content: center;
                          font-size: 16px;
                          color: white;
                      ">
                          <i class="fas fa-book"></i>
                      </div>
                      <div class="subject-details" style="flex: 1;">
                          <div class="subject-name" style="
                              font-weight: 600;
                              color: var(--text-color);
                              margin-bottom: 4px;
                          ">
                              {{ assignment.subject_name }}
                              {% if assignment.is_composite %}
                              <i class="fas fa-layer-group" style="color: #f6e71c; margin-left: 4px;" title="Composite Subject"></i>
                              {% endif %}
                          </div>
                          <div class="subject-meta" style="
                              font-size: 12px;
                              color: var(--text-color);
                              opacity: 0.7;
                          ">
                              {{ assignment.grade_name }} {{ assignment.stream_name }}
                              {% if assignment.role == 'class_teacher' %}
                              <span style="
                                  background: rgba(8, 255, 168, 0.2);
                                  color: #08ffa8;
                                  padding: 2px 6px;
                                  border-radius: 4px;
                                  font-size: 10px;
                                  margin-left: 4px;
                              ">CLASS TEACHER</span>
                              {% else %}
                              <span style="
                                  background: rgba(0, 255, 250, 0.2);
                                  color: #00fffa;
                                  padding: 2px 6px;
                                  border-radius: 4px;
                                  font-size: 10px;
                                  margin-left: 4px;
                              ">SUBJECT TEACHER</span>
                              {% endif %}
                          </div>
                      </div>
                  </div>
                  {% endfor %}
              </div>
          </div>
          {% endif %}

          <!-- Class Teacher Assignments -->
          {% if can_manage_classes and class_teacher_assignments %}
          <div style="margin-bottom: 24px;">
              <h4 style="margin: 0 0 16px 0; color: var(--text-color); display: flex; align-items: center; gap: 12px;">
                  <i class="fas fa-chalkboard-teacher" style="color: #00fffa;"></i>
                  Class Teacher Responsibilities
              </h4>

              <div class="class-assignments-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px;">
                  {% for assignment in class_teacher_assignments %}
                  <div class="class-assignment-card" style="
                      background: rgba(0, 255, 250, 0.1);
                      border: 1px solid rgba(0, 255, 250, 0.2);
                      border-radius: 12px;
                      padding: 20px;
                      text-align: center;
                  ">
                      <div class="class-icon" style="
                          width: 48px;
                          height: 48px;
                          background: linear-gradient(135deg, #00fffa, #08ffa8);
                          border-radius: 12px;
                          display: flex;
                          align-items: center;
                          justify-content: center;
                          margin: 0 auto 12px;
                          font-size: 20px;
                          color: white;
                      ">
                          <i class="fas fa-chalkboard"></i>
                      </div>
                      <div class="class-title" style="
                          font-weight: 600;
                          color: var(--text-color);
                          margin-bottom: 4px;
                          font-size: 16px;
                      ">
                          {{ assignment.grade_level }}
                      </div>
                      {% if assignment.stream_name %}
                      <div class="class-subtitle" style="
                          color: var(--text-color);
                          opacity: 0.7;
                          font-size: 14px;
                          margin-bottom: 8px;
                      ">
                          Stream {{ assignment.stream_name }}
                      </div>
                      {% endif %}
                      <span style="
                          background: rgba(0, 255, 250, 0.2);
                          color: #00fffa;
                          padding: 4px 12px;
                          border-radius: 20px;
                          font-size: 12px;
                          font-weight: 600;
                      ">
                          Class Teacher
                      </span>
                  </div>
                  {% endfor %}
              </div>
          </div>
          {% endif %}

          <!-- Access Control Note -->
          <div style="
              padding: 16px;
              background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
              border-radius: 12px;
              border: 1px solid rgba(59, 130, 246, 0.2);
          ">
              <div style="font-size: 14px; color: var(--text-color);">
                  <strong style="color: #3b82f6;"><i class="fas fa-info-circle"></i> Access Control:</strong>
                  {% if assignment_summary.role == 'teacher' %}
                  As a subject teacher, you can upload marks for your assigned subjects only.
                  {% elif assignment_summary.role == 'classteacher' %}
                  As a class teacher, you can upload marks for all your subjects and manage your assigned classes.
                  {% elif assignment_summary.role == 'headteacher' %}
                  As a headteacher, you have full access to all subjects and classes in the system.
                  {% endif %}
                  The forms below are filtered to show only your accessible options.
              </div>
          </div>
      </div>
      {% endif %}

      <!-- Permission Status Widget -->
      <div class="permission-status-widget slide-up" style="
          background: var(--glass-bg);
          border: 1px solid var(--glass-border);
          border-radius: 20px;
          padding: 24px;
          margin: 24px 0;
          backdrop-filter: var(--glass-backdrop);
          box-shadow: var(--shadow-lg);
      ">
          <div class="widget-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
              <h3 style="margin: 0; color: var(--text-color); display: flex; align-items: center; gap: 12px;">
                  <i class="fas fa-key" style="color: #08ffa8;"></i>
                  Class Permissions
              </h3>
              <button onclick="refreshPermissionStatus()" class="premium-btn btn-outline" style="padding: 8px 16px; font-size: 12px;">
                  <i class="fas fa-sync-alt"></i>
                  Refresh
              </button>
          </div>
          <div id="permission-status-content" style="
              padding: 20px;
              background: rgba(255, 255, 255, 0.05);
              border-radius: 12px;
              text-align: center;
          ">
              <div style="color: var(--text-color); opacity: 0.7;">
                  <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 12px; color: #08ffa8;"></i>
                  <p style="margin: 0;">Loading permission status...</p>
              </div>
          </div>
      </div>

      <!-- 🎯 PREMIUM TABS NAVIGATION - UNIFIED DESIGN -->
      <div class="premium-tabs-container slide-up" style="
          background: var(--glass-bg);
          border: 1px solid var(--glass-border);
          padding: 24px;
          margin: 24px 0;
          border-radius: 20px;
          box-shadow: var(--shadow-lg);
          backdrop-filter: var(--glass-backdrop);
          position: relative;
          overflow: hidden;
      ">
          <!-- Premium Header -->
          <div class="premium-header" style="
              text-align: center;
              margin-bottom: 24px;
              position: relative;
          ">
              <h2 style="
                  margin: 0;
                  background: linear-gradient(135deg, #00fffa, #08ffa8);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                  background-clip: text;
                  font-size: 20px;
                  font-weight: 700;
                  letter-spacing: -0.025em;
              ">📚 Class Teacher Dashboard</h2>
              <p style="
                  margin: 8px 0 0 0;
                  color: var(--text-color);
                  font-size: 14px;
                  font-weight: 500;
              ">Select your preferred mode</p>
          </div>

          <!-- Premium Tab Navigation -->
          <div class="premium-tab-nav" style="
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 12px;
              max-width: 1000px;
              margin: 0 auto;
          ">
              <button class="premium-tab-button {% if active_tab == 'upload-marks' %}active{% endif %}"
                      onclick="console.log('BUTTON CLICKED!'); debugToTerminal('Upload Marks button clicked!'); switchMainTab('upload-marks')"
                      data-tab="upload-marks">
                  <div class="tab-icon">
                      <i class="fas fa-upload"></i>
                  </div>
                  <div class="tab-content">
                      <div class="tab-title">Upload Marks</div>
                      <div class="tab-subtitle">Enter student marks</div>
                  </div>
              </button>

              <button class="premium-tab-button {% if active_tab == 'recent-reports' %}active{% endif %}"
                      onclick="switchMainTab('recent-reports')"
                      data-tab="recent-reports">
                  <div class="tab-icon">
                      <i class="fas fa-clock"></i>
                  </div>
                  <div class="tab-content">
                      <div class="tab-title">Recent Reports</div>
                      <div class="tab-subtitle">View generated reports</div>
                  </div>
              </button>

              <button class="premium-tab-button {% if active_tab == 'generate-reports' %}active{% endif %}"
                      onclick="switchMainTab('generate-reports')"
                      data-tab="generate-reports">
                  <div class="tab-icon">
                      <i class="fas fa-file-pdf"></i>
                  </div>
                  <div class="tab-content">
                      <div class="tab-title">Generate Reports</div>
                      <div class="tab-subtitle">Create new reports</div>
                  </div>
              </button>

              <button class="premium-tab-button {% if active_tab == 'analytics' %}active{% endif %}"
                      onclick="switchMainTab('analytics')"
                      data-tab="analytics">
                  <div class="tab-icon">
                      <i class="fas fa-chart-line"></i>
                  </div>
                  <div class="tab-content">
                      <div class="tab-title">Analytics</div>
                      <div class="tab-subtitle">Performance insights</div>
                  </div>
              </button>

              <button class="premium-tab-button {% if active_tab == 'bulk-upload' %}active{% endif %}"
                      onclick="switchMainTab('bulk-upload')"
                      data-tab="bulk-upload">
                  <div class="tab-icon">
                      <i class="fas fa-file-upload"></i>
                  </div>
                  <div class="tab-content">
                      <div class="tab-title">Bulk Upload</div>
                      <div class="tab-subtitle">Upload marks via file</div>
                  </div>
              </button>

              <button class="premium-tab-button {% if active_tab == 'management' %}active{% endif %}"
                      onclick="switchMainTab('management')"
                      data-tab="management">
                  <div class="tab-icon">
                      <i class="fas fa-cogs"></i>
                  </div>
                  <div class="tab-content">
                      <div class="tab-title">Management</div>
                      <div class="tab-subtitle">Admin tools</div>
                  </div>
              </button>
          </div>
      </div>

      <!-- 📋 TAB CONTENT SECTIONS -->

      <!-- Upload Marks Tab -->
      <div id="upload-marks-tab" class="tab-content-container {% if active_tab == 'upload-marks' %}active{% endif %}">
          <div id="upload-marks-section" class="modern-card slide-up">
              <div class="card-header">
                  <h2 class="card-title">
                      <i class="fas fa-upload card-icon"></i>
                      Upload Class Marks
                      {% if portal_summary %}
                          <small style="font-weight: normal; color: #666; margin-left: 10px;">
                              ({{ portal_summary.portal_type }} - {{ portal_summary.total_classes }} classes, {{ portal_summary.total_subjects }} subjects)
                          </small>
                      {% endif %}
                  </h2>
                  <div class="card-actions">
                      {% if portal_summary and portal_summary.is_subject_only_teacher %}
                          <span class="modern-badge badge-warning">Subject Teacher Access</span>
                      {% else %}
                          <span class="modern-badge badge-info">Class Teacher Access</span>
                      {% endif %}
                  </div>
              </div>

              <!-- Multi-Class Assignment Overview -->
              {% if portal_summary and portal_summary.class_assignments %}
              <div class="multi-class-overview" style="padding: 20px; background: #f8f9fa; border-bottom: 1px solid #e0e0e0;">
                  <h4 style="margin: 0 0 15px 0; color: #333;">
                      <i class="fas fa-school"></i> Your Class & Subject Assignments
                  </h4>

                  {% if portal_summary.is_subject_only_teacher %}
                  <div class="info-banner" style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 6px; margin-bottom: 15px;">
                      <i class="fas fa-info-circle" style="color: #856404;"></i>
                      <strong>Subject Teacher Access:</strong> You can upload marks for your assigned subjects across multiple classes.
                  </div>
                  {% endif %}

                  <div class="class-assignments-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                      {% for class_assignment in portal_summary.class_assignments %}
                      <div class="class-assignment-card" style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 15px; background: white;">
                          <div class="class-header" style="display: flex; justify-content: between; align-items: center; margin-bottom: 10px;">
                              <h5 style="margin: 0; color: #007bff;">
                                  {{ class_assignment.grade_name }} Stream {{ class_assignment.stream_name }}
                              </h5>
                              {% if class_assignment.is_class_teacher %}
                                  <span class="assignment-type" style="background: #28a745; color: white; padding: 2px 8px; border-radius: 12px; font-size: 10px;">
                                      CLASS TEACHER
                                  </span>
                              {% else %}
                                  <span class="assignment-type" style="background: #17a2b8; color: white; padding: 2px 8px; border-radius: 12px; font-size: 10px;">
                                      SUBJECT TEACHER
                                  </span>
                              {% endif %}
                          </div>

                          <div class="subjects-list" style="margin-bottom: 10px;">
                              <strong>Subjects:</strong>
                              <div style="display: flex; flex-wrap: wrap; gap: 5px; margin-top: 5px;">
                                  {% for subject in class_assignment.subjects %}
                                  <span class="subject-tag" style="background: #e9ecef; padding: 2px 6px; border-radius: 10px; font-size: 11px;">
                                      {{ subject }}
                                  </span>
                                  {% endfor %}
                              </div>
                          </div>

                          <div class="class-actions" style="margin-top: 10px;">
                              <button onclick="selectClassForUpload('{{ class_assignment.grade_name }}', '{{ class_assignment.stream_name }}', '{{ class_assignment.education_level }}')"
                                      class="modern-btn btn-primary" style="font-size: 12px; padding: 6px 12px;">
                                  <i class="fas fa-upload"></i> Upload Marks
                              </button>
                          </div>
                      </div>
                      {% endfor %}
                  </div>
              </div>
              {% endif %}

              <!-- Upload Form -->
              {% if not show_students %}
              <!-- Modern Upload Method Selection -->
              <div class="modern-tabs">
                  <div class="tab-nav">
                      <button class="tab-button active" data-tab="manual-entry">
                          <i class="fas fa-keyboard"></i>
                          Manual Entry
                      </button>
                      <button class="tab-button" data-tab="bulk-upload">
                          <i class="fas fa-file-upload"></i>
                          Bulk Upload
                      </button>
                  </div>
                  <div class="tab-content">
                      <div class="tab-pane active" id="manual-entry">
                          <form id="upload-form" method="POST" action="{{ url_for('classteacher.dashboard') }}" class="modern-form">
                              <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

                              <div class="modern-grid grid-cols-2">
                                  <div class="form-group">
                                      <label for="education_level" class="form-label">Education Level</label>
                                      <select id="education_level" name="education_level" required onchange="updateSubjects()" class="form-select">
                                      <option value="">Select Education Level</option>
                                      <option value="lower_primary" {% if education_level == 'lower_primary' %}selected{% endif %}>Lower Primary</option>
                                      <option value="upper_primary" {% if education_level == 'upper_primary' %}selected{% endif %}>Upper Primary</option>
                                      <option value="junior_secondary" {% if education_level == 'junior_secondary' %}selected{% endif %}>Junior Secondary</option>
                                  </select>
                              </div>

                              <div class="form-group">
                                  <label for="term" class="form-label">Term</label>
                                  <select id="term" name="term" required class="form-select">
                                      <option value="">Select Term</option>
                                      {% for term_option in terms %}
                                      <option value="{{ term_option }}" {% if term == term_option %}selected{% endif %}>{{ term_option.replace('_', ' ').title() }}</option>
                                      {% endfor %}
                                  </select>
                              </div>

                              <div class="form-group">
                                  <label for="assessment_type" class="form-label">Assessment Type</label>
                                  <select id="assessment_type" name="assessment_type" required class="form-select">
                                      <option value="">Select Assessment Type</option>
                                      {% for assessment_option in assessment_types %}
                                      <option value="{{ assessment_option }}" {% if assessment_type == assessment_option %}selected{% endif %}>{{ assessment_option.replace('_', ' ').title() }}</option>
                                      {% endfor %}
                                  </select>
                              </div>

                              <div class="form-group">
                                  <label for="grade" class="form-label">Grade</label>
                                  <select id="grade" name="grade" required onchange="fetchStreams()" class="form-select">
                                      <option value="">Select Grade</option>
                                      {% for grade_option in grades %}
                                      <option value="{{ grade_option }}" {% if grade == grade_option %}selected{% endif %}>{{ grade_option }}</option>
                                      {% endfor %}
                                  </select>
                              </div>

                              <div class="form-group">
                                  <label for="stream" class="form-label">Stream</label>
                                  <select id="stream" name="stream" required class="form-select">
                                      <option value="">Select Stream</option>
                                      <!-- Streams will be populated dynamically via JavaScript -->
                                  </select>
                              </div>
                          </div>

                          <!-- Hidden field for total marks -->
                          <input type="hidden" id="total_marks" name="total_marks" value="100">

                          <div style="text-align: center; margin-top: var(--space-6);">
                              <button type="submit" name="upload_marks" value="1" class="modern-btn btn-primary" id="upload-btn">
                                  <i class="fas fa-users"></i>
                                  Load Students & Subjects
                              </button>
                          </div>
                      </form>
                  </div>
              </div>

              {% else %}
              <!-- Form for entering student marks for all subjects -->
              <div id="pupils-list" class="pupils-list" style="grid-column: span 2;
              background: var(--glass-bg);
              border: 1px solid var(--glass-border);
              border-radius: 20px;
              padding: 32px;
              margin: 32px 0;
              backdrop-filter: var(--glass-backdrop);
              box-shadow: var(--shadow-xl);
              animation: slideInUp 0.6s ease-out;
          ">
              <!-- Enhanced Section Header -->
              <div class="premium-section-header" style="
                  display: flex;
                  align-items: center;
                  gap: 20px;
                  margin-bottom: 32px;
                  padding-bottom: 20px;
                  border-bottom: 2px solid var(--glass-border);
                  position: relative;
              ">
                  <div class="header-icon-container" style="
                      width: 64px;
                      height: 64px;
                      background: linear-gradient(135deg, #00fffa, #08ffa8);
                      border-radius: 16px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      color: #000;
                      font-size: 24px;
                      box-shadow: 0 8px 25px rgba(0, 255, 250, 0.3);
                  ">
                      <i class="fas fa-users"></i>
                  </div>
                  <div class="header-content">
                      <h3 style="
                          margin: 0;
                          background: linear-gradient(135deg, #00fffa, #08ffa8);
                          -webkit-background-clip: text;
                          -webkit-text-fill-color: transparent;
                          background-clip: text;
                          font-size: 28px;
                          font-weight: 700;
                          letter-spacing: -0.025em;
                      ">Enter Marks for Grade {{ grade }} Stream {{ stream }}</h3>
                      <div style="
                          display: flex;
                          gap: 24px;
                          margin-top: 8px;
                          flex-wrap: wrap;
                      ">
                          <div class="info-badge" style="
                              background: rgba(0, 255, 250, 0.1);
                              border: 1px solid rgba(0, 255, 250, 0.3);
                              padding: 6px 12px;
                              border-radius: 20px;
                              font-size: 12px;
                              font-weight: 500;
                              color: #00fffa;
                              display: flex;
                              align-items: center;
                              gap: 6px;
                          ">
                              <i class="fas fa-user-graduate"></i>
                              {{ students|length }} Students
                          </div>
                          <div class="info-badge" style="
                              background: rgba(8, 255, 168, 0.1);
                              border: 1px solid rgba(8, 255, 168, 0.3);
                              padding: 6px 12px;
                              border-radius: 20px;
                              font-size: 12px;
                              font-weight: 500;
                              color: #08ffa8;
                              display: flex;
                              align-items: center;
                              gap: 6px;
                          ">
                              <i class="fas fa-book"></i>
                              {{ subjects|length }} Subjects
                          </div>
                          <div class="info-badge" style="
                              background: rgba(246, 231, 28, 0.1);
                              border: 1px solid rgba(246, 231, 28, 0.3);
                              padding: 6px 12px;
                              border-radius: 20px;
                              font-size: 12px;
                              font-weight: 500;
                              color: #f6e71c;
                              display: flex;
                              align-items: center;
                              gap: 6px;
                          ">
                              <i class="fas fa-calendar"></i>
                              {{ term }} {{ assessment_type }}
                          </div>
                      </div>
                  </div>

                  <!-- Progress Indicator -->
                  <div class="progress-indicator" style="
                      position: absolute;
                      top: -1px;
                      left: 0;
                      right: 0;
                      height: 3px;
                      background: rgba(255, 255, 255, 0.1);
                      border-radius: 2px;
                      overflow: hidden;
                  ">
                      <div class="progress-bar" style="
                          height: 100%;
                          width: 0%;
                          background: linear-gradient(90deg, #00fffa, #08ffa8);
                          transition: width 0.3s ease;
                      " id="marks-progress"></div>
                  </div>
              </div>

                  <h3>Enter Marks for Grade {{ grade }} Stream {{ stream }} - All Subjects</h3>

                  <form id="marks-form" method="POST" action="{{ url_for('classteacher.dashboard') }}">
                      <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                      <!-- Keep existing form fields as hidden fields -->
                      <input type="hidden" name="education_level" value="{{ education_level }}"/>
                      <input type="hidden" name="grade" value="{{ grade }}"/>
                      <input type="hidden" name="stream" value="{{ stream }}"/>
                      <input type="hidden" name="term" value="{{ term }}"/>
                      <input type="hidden" name="assessment_type" value="{{ assessment_type }}"/>
                      <input type="hidden" name="total_marks" value="{{ total_marks }}"/>

                  <!-- Enhanced Subject Upload Status Dashboard -->
                  {% if session.get('all_subjects_status') %}
                  <div class="premium-status-dashboard" style="
                      background: rgba(255, 255, 255, 0.03);
                      border: 1px solid var(--glass-border);
                      border-radius: 16px;
                      padding: 24px;
                      margin-bottom: 32px;
                  ">
                      <h4 style="
                          margin: 0 0 16px 0;
                          color: var(--text-color);
                          display: flex;
                          align-items: center;
                          gap: 12px;
                          font-size: 18px;
                      ">
                          <i class="fas fa-chart-pie" style="color: #08ffa8;"></i>
                          Subject Upload Status Overview
                      </h4>
                      <p style="
                          margin: 0 0 20px 0;
                          color: var(--text-color);
                          opacity: 0.8;
                          font-size: 14px;
                      ">View which subjects have been uploaded and by whom. You can only upload subjects assigned to you.</p>

                      <div class="premium-status-grid" style="
                          display: grid;
                          grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
                          gap: 16px;
                          margin-bottom: 20px;
                      ">
                          {% for subject_info in session.get('all_subjects_status', []) %}
                          <div class="premium-status-card {% if subject_info.assigned_to_me %}assigned-to-me{% endif %}" style="
                              background: {% if subject_info.assigned_to_me %}rgba(0, 255, 250, 0.05){% else %}rgba(255, 255, 255, 0.02){% endif %};
                              border: 1px solid {% if subject_info.assigned_to_me %}rgba(0, 255, 250, 0.3){% else %}var(--glass-border){% endif %};
                              border-radius: 12px;
                              padding: 20px;
                              transition: all 0.3s ease;
                          ">
                              <div class="status-header" style="
                                  display: flex;
                                  justify-content: space-between;
                                  align-items: center;
                                  margin-bottom: 12px;
                              ">
                                  <h5 style="
                                      margin: 0;
                                      color: var(--text-color);
                                      font-size: 16px;
                                      font-weight: 600;
                                  ">{{ subject_info.name }}</h5>
                                  {% if subject_info.assigned_to_me %}
                                  <span class="assignment-badge" style="
                                      background: linear-gradient(135deg, #00fffa, #08ffa8);
                                      color: #000;
                                      padding: 4px 12px;
                                      border-radius: 20px;
                                      font-size: 10px;
                                      font-weight: 600;
                                      text-transform: uppercase;
                                      letter-spacing: 0.5px;
                                  ">ASSIGNED TO YOU</span>
                                  {% endif %}
                              </div>

                              <div class="status-info">
                                  {% if subject_info.upload_status == 'uploaded_by_me' %}
                                  <div class="status-item" style="
                                      color: #08ffa8;
                                      font-weight: 600;
                                      display: flex;
                                      align-items: center;
                                      gap: 8px;
                                      margin-bottom: 8px;
                                  ">
                                      <i class="fas fa-check-circle"></i>
                                      Uploaded by you
                                  </div>
                                  <div class="marks-count" style="
                                      font-size: 12px;
                                      color: var(--text-color);
                                      opacity: 0.7;
                                  ">{{ subject_info.marks_count }} student marks</div>
                                  {% elif subject_info.upload_status == 'uploaded_by_subject_teacher' %}
                                  <div class="status-item" style="
                                      color: #00fffa;
                                      font-weight: 600;
                                      display: flex;
                                      align-items: center;
                                      gap: 8px;
                                      margin-bottom: 8px;
                                  ">
                                      <i class="fas fa-user-check"></i>
                                      Uploaded by {{ subject_info.uploaded_by.username if subject_info.uploaded_by else 'Subject Teacher' }}
                                  </div>
                                  <div class="marks-count" style="
                                      font-size: 12px;
                                      color: var(--text-color);
                                      opacity: 0.7;
                                  ">{{ subject_info.marks_count }} student marks</div>
                                  {% elif subject_info.upload_status == 'not_uploaded' %}
                                  <div class="status-item" style="
                                      color: #f6e71c;
                                      font-weight: 600;
                                      display: flex;
                                      align-items: center;
                                      gap: 8px;
                                      margin-bottom: 8px;
                                  ">
                                      <i class="fas fa-exclamation-triangle"></i>
                                      Not uploaded yet
                                  </div>
                                  {% if subject_info.assigned_to_me %}
                                  <div class="action-hint" style="
                                      font-size: 12px;
                                      color: #08ffa8;
                                      display: flex;
                                      align-items: center;
                                      gap: 6px;
                                  ">
                                      <i class="fas fa-arrow-down"></i>
                                      You can upload this subject below
                                  </div>
                                  {% else %}
                                  <div class="action-hint" style="
                                      font-size: 12px;
                                      color: var(--text-color);
                                      opacity: 0.6;
                                  ">Waiting for subject teacher</div>
                                  {% endif %}
                                  {% endif %}
                              </div>
                          </div>
                          {% endfor %}
                      </div>

                      <div class="premium-status-legend" style="
                          display: flex;
                          gap: 24px;
                          font-size: 12px;
                          color: var(--text-color);
                          opacity: 0.8;
                          border-top: 1px solid var(--glass-border);
                          padding-top: 16px;
                          flex-wrap: wrap;
                      ">
                          <div style="display: flex; align-items: center; gap: 6px;">
                              <i class="fas fa-check-circle" style="color: #08ffa8;"></i>
                              Marks uploaded
                          </div>
                          <div style="display: flex; align-items: center; gap: 6px;">
                              <i class="fas fa-exclamation-triangle" style="color: #f6e71c;"></i>
                              Pending upload
                          </div>
                          <div style="display: flex; align-items: center; gap: 6px;">
                              <span style="
                                  background: linear-gradient(135deg, #00fffa, #08ffa8);
                                  color: #000;
                                  padding: 2px 8px;
                                  border-radius: 10px;
                                  font-weight: 600;
                                  font-size: 10px;
                              ">ASSIGNED</span>
                              You can upload
                          </div>
                      </div>
                  </div>
                  {% endif %}

                  <!-- Enhanced Subject Selection and Configuration -->
                  <div class="premium-subject-config" style="margin-bottom: 32px;">
                      <h4 style="
                          margin: 0 0 16px 0;
                          color: var(--text-color);
                          display: flex;
                          align-items: center;
                          gap: 12px;
                          font-size: 18px;
                      ">
                          <i class="fas fa-cogs" style="color: #08ffa8;"></i>
                          Select Your Assigned Subjects and Set Maximum Raw Marks
                      </h4>
                      <p style="
                          margin: 0 0 24px 0;
                          color: var(--text-color);
                          opacity: 0.8;
                          font-size: 14px;
                          line-height: 1.5;
                      ">
                          <strong style="color: #08ffa8;">📚 Flexible Subject Upload:</strong> Only subjects assigned to you are shown below.
                          This reduces your workload by allowing you to upload only the subjects you teach.
                          Subject teachers will upload their own subjects separately.
                      </p>

                      <div class="premium-selection-header" style="
                          display: flex;
                          justify-content: space-between;
                          align-items: center;
                          margin-bottom: 20px;
                          padding: 16px 20px;
                          background: rgba(255, 255, 255, 0.03);
                          border: 1px solid var(--glass-border);
                          border-radius: 12px;
                      ">
                          <h5 style="
                              margin: 0;
                              color: var(--text-color);
                              font-size: 16px;
                              font-weight: 600;
                              display: flex;
                              align-items: center;
                              gap: 8px;
                          ">
                              <i class="fas fa-check-square" style="color: #08ffa8;"></i>
                              Subject Selection
                          </h5>
                          <div class="selection-controls" style="display: flex; gap: 12px;">
                              <button type="button" class="premium-btn btn-outline" style="
                                  padding: 8px 16px;
                                  font-size: 12px;
                                  border-radius: 8px;
                                  transition: all 0.2s ease;
                              " onclick="selectAllSubjects()">
                                  <i class="fas fa-check-double"></i>
                                  Select All
                              </button>
                              <button type="button" class="premium-btn btn-outline" style="
                                  padding: 8px 16px;
                                  font-size: 12px;
                                  border-radius: 8px;
                                  transition: all 0.2s ease;
                              " onclick="deselectAllSubjects()">
                                  <i class="fas fa-times"></i>
                                  Deselect All
                              </button>
                          </div>
                      </div>

                      <div class="subject-marks-grid" style="display: grid; gap: 16px;">
                          {% for subject in subjects %}
                          <div class="subject-mark-item {% if subject.is_composite %}composite-subject-item{% endif %}" style="
                              background: rgba(255, 255, 255, 0.05);
                              border: 1px solid var(--glass-border);
                              border-radius: 12px;
                              padding: 16px;
                          ">
                              <div class="subject-selection" style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                                  <input
                                      type="checkbox"
                                      id="include_subject_{{ loop.index0 }}"
                                      name="include_subject_{{ loop.index0 }}"
                                      value="{{ subject.id }}"
                                      checked
                                      class="subject-checkbox"
                                      data-subject-id="{{ subject.id }}"
                                      data-is-composite="{{ 'true' if subject.is_composite else 'false' }}"
                                      onchange="toggleSubjectVisibility(this, '{{ subject.id }}')"
                                      style="transform: scale(1.2);"
                                  >
                                  <label for="include_subject_{{ loop.index0 }}" class="checkbox-label" style="
                                      color: var(--text-color);
                                      font-weight: 500;
                                      cursor: pointer;
                                      display: flex;
                                      align-items: center;
                                      gap: 8px;
                                  ">
                                      {{ subject.name }}
                                      {% if subject.upload_status %}
                                          {% if subject.upload_status == 'uploaded_by_me' %}
                                              <span class="upload-status uploaded-by-me" style="
                                                  background: rgba(8, 255, 168, 0.2);
                                                  color: #08ffa8;
                                                  padding: 2px 8px;
                                                  border-radius: 12px;
                                                  font-size: 11px;
                                                  font-weight: 600;
                                              " title="You have uploaded marks for this subject">
                                                  ✓ Uploaded by you
                                              </span>
                                          {% elif subject.upload_status == 'uploaded_by_subject_teacher' %}
                                              <span class="upload-status uploaded-by-other" style="
                                                  background: rgba(0, 255, 250, 0.2);
                                                  color: #00fffa;
                                                  padding: 2px 8px;
                                                  border-radius: 12px;
                                                  font-size: 11px;
                                                  font-weight: 600;
                                              " title="Subject teacher has uploaded marks">
                                                  ✓ Uploaded by {{ subject.uploaded_by.username if subject.uploaded_by else 'Subject Teacher' }}
                                              </span>
                                          {% elif subject.upload_status == 'not_uploaded' %}
                                              <span class="upload-status not-uploaded" style="
                                                  background: rgba(246, 231, 28, 0.2);
                                                  color: #f6e71c;
                                                  padding: 2px 8px;
                                                  border-radius: 12px;
                                                  font-size: 11px;
                                                  font-weight: 600;
                                              " title="No marks uploaded yet">
                                                  ⚠ Not uploaded
                                              </span>
                                          {% endif %}
                                      {% endif %}
                                  </label>
                              </div>

                              {% if subject.is_composite %}
                              <div class="composite-marks-container" style="
                                  background: rgba(0, 255, 250, 0.05);
                                  border: 2px solid rgba(0, 255, 250, 0.2);
                                  border-radius: 12px;
                                  padding: 16px;
                                  margin-bottom: 20px;
                              ">
                                  <div class="composite-marks-header" style="
                                      display: flex;
                                      justify-content: space-between;
                                      align-items: center;
                                      margin-bottom: 16px;
                                      padding-bottom: 12px;
                                      border-bottom: 2px solid rgba(0, 255, 250, 0.3);
                                  ">
                                      <span style="
                                          color: #00fffa;
                                          font-weight: 700;
                                          font-size: 16px;
                                          text-transform: uppercase;
                                          letter-spacing: 1px;
                                      ">📊 Component Max Marks Configuration</span>
                                      <small style="
                                          color: #08ffa8;
                                          font-weight: 600;
                                          font-size: 14px;
                                          background: rgba(8, 255, 168, 0.1);
                                          padding: 4px 8px;
                                          border-radius: 6px;
                                          border: 1px solid rgba(8, 255, 168, 0.3);
                                      ">Total: <span id="total_composite_{{ loop.index0 }}">{{ total_marks }}</span></small>
                                  </div>

                                  {% set subject_index = loop.index0 %}
                                  {% set components = subject.get_components() %}
                                  <div class="component-grid" style="display: grid; gap: 12px;">
                                      {% for component in components %}
                                      <div class="component-marks-row" style="
                                          display: grid;
                                          grid-template-columns: 1fr auto auto;
                                          align-items: center;
                                          gap: 16px;
                                          background: rgba(255, 255, 255, 0.02);
                                          padding: 12px;
                                          border-radius: 8px;
                                          border: 1px solid rgba(0, 255, 250, 0.1);
                                      ">
                                          <label for="component_marks_{{ subject_index }}_{{ component.id }}" class="component-label" style="
                                              color: #00fffa;
                                              font-size: 14px;
                                              font-weight: 600;
                                          ">{{ component.name }}:</label>

                                          <div class="component-marks-input" style="display: flex; align-items: center; gap: 8px;">
                                              <input
                                                  type="number"
                                                  id="component_marks_{{ subject_index }}_{{ component.id }}"
                                                  name="component_max_marks_{{ subject_index }}_{{ component.id }}"
                                                  value="{{ component.max_raw_mark or 100 }}"
                                                  min="1"
                                                  max="100"
                                                  class="form-input component-max-marks"
                                                  data-subject="{{ subject.id }}"
                                                  data-component="{{ component.id }}"
                                                  data-subject-index="{{ subject_index }}"
                                                  data-component-weight="{{ component.weight }}"
                                                  onchange="validateMaxMarks(this); updateComponentMaxMarks('{{ subject.id }}', '{{ component.id }}', this)"
                                                  oninput="validateMaxMarks(this)"
                                                  title="Maximum raw marks for {{ component.name }} component (Max: 100)"
                                                  style="
                                                      width: 80px;
                                                      text-align: center;
                                                      background: rgba(255, 255, 255, 0.1);
                                                      border: 2px solid rgba(0, 255, 250, 0.3);
                                                      border-radius: 8px;
                                                      color: var(--text-color);
                                                      font-weight: 700;
                                                      font-size: 16px;
                                                      padding: 8px;
                                                  "
                                              >
                                              <span style="
                                                  color: #08ffa8;
                                                  font-weight: 600;
                                                  font-size: 12px;
                                              ">marks</span>
                                          </div>

                                          <small class="component-proportion" id="proportion_{{ subject.id }}_{{ component.id }}" style="
                                              color: #f6e71c;
                                              font-weight: 600;
                                              font-size: 12px;
                                              background: rgba(246, 231, 28, 0.1);
                                              padding: 4px 8px;
                                              border-radius: 6px;
                                              border: 1px solid rgba(246, 231, 28, 0.3);
                                              text-align: center;
                                              min-width: 80px;
                                          ">
                                              Weight: calculating...
                                          </small>
                                      </div>
                                      {% endfor %}
                                  </div>

                                  <input
                                      type="hidden"
                                      id="total_marks_{{ loop.index0 }}"
                                      name="total_marks_{{ loop.index0 }}"
                                      value="{{ total_marks }}"
                                      data-subject="{{ subject.id }}"
                                  >
                              </div>
                              {% else %}
                              <div class="marks-input-container" style="
                                  display: grid;
                                  grid-template-columns: auto 1fr;
                                  align-items: center;
                                  gap: 12px;
                              ">
                                  <label for="total_marks_{{ loop.index0 }}" class="marks-label" style="
                                      color: var(--text-color);
                                      font-size: 14px;
                                  ">Max Marks:</label>
                                  <input
                                      type="number"
                                      id="total_marks_{{ loop.index0 }}"
                                      name="total_marks_{{ loop.index0 }}"
                                      value="{{ total_marks }}"
                                      min="1"
                                      max="100"
                                      class="form-input subject-total-marks"
                                      data-subject="{{ subject.id }}"
                                      onchange="updateAllMarksForSubject('{{ subject.id }}', {{ loop.index0 }}); validateMaxMarks(this)"
                                      oninput="validateMaxMarks(this)"
                                      title="Maximum raw marks for this subject (Max: 100)"
                                      style="width: 80px; text-align: center;"
                                  >
                              </div>
                              {% endif %}
                          </div>
                          {% endfor %}
                      </div>
                  </div>

                  <!-- Students Marks Entry Table -->
                  <div class="premium-students-table" style="
                      background: rgba(255, 255, 255, 0.02);
                      border: 1px solid var(--glass-border);
                      border-radius: 16px;
                      padding: 24px;
                      margin-top: 32px;
                      overflow-x: auto;
                  ">
                      <div class="table-header" style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                          margin-bottom: 20px;
                          padding-bottom: 16px;
                          border-bottom: 1px solid var(--glass-border);
                      ">
                          <h4 style="
                              margin: 0;
                              color: var(--text-color);
                              display: flex;
                              align-items: center;
                              gap: 12px;
                              font-size: 18px;
                          ">
                              <i class="fas fa-table" style="color: #08ffa8;"></i>
                              Enter Student Marks
                          </h4>
                          <div class="table-actions" style="display: flex; gap: 12px;">
                              <button type="button" onclick="clearAllMarks()" class="btn-secondary" style="
                                  background: rgba(255, 255, 255, 0.1);
                                  border: 1px solid var(--glass-border);
                                  color: var(--text-color);
                                  padding: 8px 16px;
                                  border-radius: 8px;
                                  font-size: 12px;
                                  cursor: pointer;
                              ">
                                  <i class="fas fa-eraser"></i> Clear All
                              </button>
                              <button type="submit" name="submit_marks" value="1" class="btn-primary" style="
                                  background: linear-gradient(135deg, #00fffa, #08ffa8);
                                  border: none;
                                  color: #000;
                                  padding: 8px 16px;
                                  border-radius: 8px;
                                  font-size: 12px;
                                  font-weight: 600;
                                  cursor: pointer;
                              ">
                                  <i class="fas fa-save"></i> Submit Marks
                              </button>
                          </div>
                      </div>

                      <div class="table-container" style="overflow-x: auto;">
                          <table class="premium-marks-table" style="
                              width: 100%;
                              border-collapse: collapse;
                              background: rgba(255, 255, 255, 0.02);
                              border-radius: 12px;
                              overflow: hidden;
                          ">
                              <thead>
                                  <tr style="background: rgba(0, 255, 250, 0.1);">
                                      <th style="
                                          padding: 16px 12px;
                                          text-align: left;
                                          color: var(--text-color);
                                          font-weight: 600;
                                          border-bottom: 1px solid var(--glass-border);
                                          position: sticky;
                                          left: 0;
                                          background: rgba(0, 255, 250, 0.1);
                                          z-index: 10;
                                      ">Student Name</th>
                                      {% for subject in subjects %}
                                      <th data-subject-id="{{ subject.id }}" style="
                                          padding: 16px 12px;
                                          text-align: center;
                                          color: var(--text-color);
                                          font-weight: 600;
                                          border-bottom: 1px solid var(--glass-border);
                                          min-width: 120px;
                                      ">
                                          {{ subject.name }}
                                          {% if subject.is_composite %}
                                          <br><small style="color: var(--text-secondary);">(Composite)</small>
                                          {% endif %}
                                      </th>
                                      {% endfor %}
                                  </tr>
                              </thead>
                              <tbody>
                                  {% for student in students %}
                                  <tr style="border-bottom: 1px solid var(--glass-border);">
                                      <td style="
                                          padding: 12px;
                                          color: var(--text-color);
                                          font-weight: 500;
                                          position: sticky;
                                          left: 0;
                                          background: var(--glass-bg);
                                          z-index: 5;
                                      ">{{ student.name }}</td>
                                      {% for subject in subjects %}
                                      <td data-subject-id="{{ subject.id }}" style="
                                          padding: 8px;
                                          text-align: center;
                                      ">
                                          {% if subject.is_composite %}
                                          <div class="composite-subject-entry" style="
                                              display: flex;
                                              flex-direction: column;
                                              gap: 4px;
                                          ">
                                              {% set subject_index = loop.index0 %}
                                              {% set components = subject.get_components() %}
                                              {% for component in components %}
                                              <div class="component-entry" style="
                                                  display: flex;
                                                  align-items: center;
                                                  gap: 4px;
                                                  font-size: 11px;
                                              ">
                                                  <label style="
                                                      color: var(--text-secondary);
                                                      min-width: 40px;
                                                      text-align: left;
                                                  ">{{ component.name }}:</label>
                                                  <input
                                                      type="number"
                                                      name="marks_{{ student.id }}_{{ subject.id }}_{{ component.id }}"
                                                      min="0"
                                                      max="{{ component.max_raw_mark or 100 }}"
                                                      step="0.1"
                                                      class="component-mark-input"
                                                      data-student="{{ student.id }}"
                                                      data-subject="{{ subject.id }}"
                                                      data-component="{{ component.id }}"
                                                      style="
                                                          width: 50px;
                                                          padding: 2px 4px;
                                                          border: 1px solid var(--glass-border);
                                                          border-radius: 4px;
                                                          background: rgba(255, 255, 255, 0.05);
                                                          color: var(--text-color);
                                                          text-align: center;
                                                          font-size: 11px;
                                                      "
                                                      onchange="updateCompositeTotal({{ student.id }}, {{ subject.id }})"
                                                  >
                                              </div>
                                              {% endfor %}
                                              <div class="composite-total" style="
                                                  margin-top: 4px;
                                                  padding-top: 4px;
                                                  border-top: 1px solid var(--glass-border);
                                                  font-weight: 600;
                                                  color: var(--text-color);
                                                  font-size: 12px;
                                              ">
                                                  Total: <span id="total_{{ student.id }}_{{ subject.id }}">0</span>
                                              </div>
                                          </div>
                                          {% else %}
                                          <input
                                              type="number"
                                              name="marks_{{ student.id }}_{{ subject.id }}"
                                              min="0"
                                              max="100"
                                              step="0.1"
                                              class="mark-input"
                                              data-student="{{ student.id }}"
                                              data-subject="{{ subject.id }}"
                                              style="
                                                  width: 80px;
                                                  padding: 8px;
                                                  border: 1px solid var(--glass-border);
                                                  border-radius: 6px;
                                                  background: rgba(255, 255, 255, 0.05);
                                                  color: var(--text-color);
                                                  text-align: center;
                                                  font-size: 14px;
                                              "
                                              onchange="validateMark(this)"
                                          >
                                          {% endif %}
                                      </td>
                                      {% endfor %}
                                  </tr>
                                  {% endfor %}
                              </tbody>
                          </table>
                      </div>

                      <div class="table-footer" style="
                          margin-top: 20px;
                          padding-top: 16px;
                          border-top: 1px solid var(--glass-border);
                          display: flex;
                          justify-content: space-between;
                          align-items: center;
                      ">
                          <div class="progress-info" style="
                              color: var(--text-secondary);
                              font-size: 12px;
                          ">
                              <span id="marks-entered">0</span> of <span id="total-marks-fields">{{ students|length * subjects|length }}</span> marks entered
                          </div>
                          <button type="submit" name="submit_marks" value="1" class="btn-primary" style="
                              background: linear-gradient(135deg, #00fffa, #08ffa8);
                              border: none;
                              color: #000;
                              padding: 12px 24px;
                              border-radius: 8px;
                              font-size: 14px;
                              font-weight: 600;
                              cursor: pointer;
                              box-shadow: 0 4px 12px rgba(0, 255, 250, 0.3);
                          ">
                              <i class="fas fa-save"></i> Submit All Marks
                          </button>
                      </div>
                  </div>
                  </form>
              </div>
              {% endif %}
          </div>
      </div>

      <!-- Generate Reports Tab -->
      <div id="generate-reports-tab" class="tab-content-section">
          <div class="tab-header">
              <h3 style="margin: 0 0 16px 0; color: var(--text-color); display: flex; align-items: center; gap: 12px;">
                  <i class="fas fa-file-pdf" style="color: #08ffa8;"></i>
                  Generate Reports
              </h3>
              <p style="margin: 0 0 24px 0; color: var(--text-color); opacity: 0.8;">
                  Create individual and class reports with CBC-compliant formatting.
              </p>
          </div>

          <div class="reports-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
              <!-- Class Report -->
              <div class="report-card" style="
                  background: rgba(255, 255, 255, 0.05);
                  border: 1px solid var(--glass-border);
                  border-radius: 16px;
                  padding: 24px;
                  text-align: center;
              ">
                  <div class="report-icon" style="
                      width: 64px;
                      height: 64px;
                      background: linear-gradient(135deg, #00fffa, #08ffa8);
                      border-radius: 16px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin: 0 auto 16px;
                      font-size: 24px;
                      color: white;
                  ">
                      <i class="fas fa-users"></i>
                  </div>
                  <h4 style="margin: 0 0 8px 0; color: var(--text-color);">Class Report</h4>
                  <p style="margin: 0 0 16px 0; color: var(--text-color); opacity: 0.7; font-size: 14px;">
                      Generate comprehensive class performance reports
                  </p>
                  <a href="{{ url_for('classteacher.all_reports') }}" class="premium-btn btn-primary">
                      <i class="fas fa-file-pdf"></i>
                      Generate Class Report
                  </a>
              </div>

              <!-- Individual Report -->
              <div class="report-card" style="
                  background: rgba(255, 255, 255, 0.05);
                  border: 1px solid var(--glass-border);
                  border-radius: 16px;
                  padding: 24px;
                  text-align: center;
              ">
                  <div class="report-icon" style="
                      width: 64px;
                      height: 64px;
                      background: linear-gradient(135deg, #08ffa8, #00ff88);
                      border-radius: 16px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin: 0 auto 16px;
                      font-size: 24px;
                      color: white;
                  ">
                      <i class="fas fa-user"></i>
                  </div>
                  <h4 style="margin: 0 0 8px 0; color: var(--text-color);">Individual Report</h4>
                  <p style="margin: 0 0 16px 0; color: var(--text-color); opacity: 0.7; font-size: 14px;">
                      Create detailed individual student reports
                  </p>
                  <a href="{{ url_for('classteacher.all_reports') }}" class="premium-btn btn-success">
                      <i class="fas fa-user-graduate"></i>
                      Generate Individual Report
                  </a>
              </div>

              <!-- Analytics Report -->
              <div class="report-card" style="
                  background: rgba(255, 255, 255, 0.05);
                  border: 1px solid var(--glass-border);
                  border-radius: 16px;
                  padding: 24px;
                  text-align: center;
              ">
                  <div class="report-icon" style="
                      width: 64px;
                      height: 64px;
                      background: linear-gradient(135deg, #f6e71c, #ffa500);
                      border-radius: 16px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin: 0 auto 16px;
                      font-size: 24px;
                      color: white;
                  ">
                      <i class="fas fa-chart-bar"></i>
                  </div>
                  <h4 style="margin: 0 0 8px 0; color: var(--text-color);">Analytics Report</h4>
                  <p style="margin: 0 0 16px 0; color: var(--text-color); opacity: 0.7; font-size: 14px;">
                      View performance analytics and insights
                  </p>
                  <a href="{{ url_for('classteacher.analytics_dashboard') }}" class="premium-btn btn-outline">
                      <i class="fas fa-chart-line"></i>
                      View Analytics
                  </a>
              </div>
          </div>
      </div>

      <!-- Analytics Tab -->
      <div id="analytics-tab" class="tab-content-section" style="
          background: var(--glass-bg);
          border: 1px solid var(--glass-border);
          border-radius: 20px;
          padding: 24px;
          margin: 24px 0;
          backdrop-filter: var(--glass-backdrop);
          box-shadow: var(--shadow-lg);
      ">
          <div class="tab-header">
              <h3 style="margin: 0 0 16px 0; color: var(--text-color); display: flex; align-items: center; gap: 12px;">
                  <i class="fas fa-chart-line" style="color: #08ffa8;"></i>
                  Performance Analytics
              </h3>
              <p style="margin: 0 0 24px 0; color: var(--text-color); opacity: 0.8;">
                  View comprehensive analytics with donut charts and performance insights.
              </p>
          </div>

          <div class="analytics-preview" style="text-align: center; padding: 40px;">
              <div style="
                  width: 120px;
                  height: 120px;
                  background: linear-gradient(135deg, #00fffa, #08ffa8);
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin: 0 auto 24px;
                  font-size: 48px;
                  color: white;
              ">
                  <i class="fas fa-chart-pie"></i>
              </div>
              <h4 style="margin: 0 0 16px 0; color: var(--text-color);">Advanced Analytics Dashboard</h4>
              <p style="margin: 0 0 24px 0; color: var(--text-color); opacity: 0.7;">
                  Access detailed performance analytics, donut charts, and actionable insights for your classes.
              </p>
              <a href="{{ url_for('classteacher.analytics_dashboard') }}" class="premium-btn btn-primary">
                  <i class="fas fa-external-link-alt"></i>
                  Open Analytics Dashboard
              </a>
          </div>
      </div>

      <!-- Management Tab -->
      <div id="management-tab" class="tab-content-section" style="
          background: var(--glass-bg);
          border: 1px solid var(--glass-border);
          border-radius: 20px;
          padding: 24px;
          margin: 24px 0;
          backdrop-filter: var(--glass-backdrop);
          box-shadow: var(--shadow-lg);
      ">
          <div class="tab-header">
              <h3 style="margin: 0 0 16px 0; color: var(--text-color); display: flex; align-items: center; gap: 12px;">
                  <i class="fas fa-cogs" style="color: #08ffa8;"></i>
                  Management Tools
              </h3>
              <p style="margin: 0 0 24px 0; color: var(--text-color); opacity: 0.8;">
                  Access advanced management tools and administrative functions.
              </p>
          </div>

          <div class="management-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px;">
              <!-- Student Management -->
              <a href="{{ url_for('classteacher.manage_students') }}" class="management-card" style="
                  background: rgba(255, 255, 255, 0.05);
                  border: 1px solid var(--glass-border);
                  border-radius: 12px;
                  padding: 20px;
                  text-decoration: none;
                  color: var(--text-color);
                  transition: all 0.3s ease;
                  display: block;
              ">
                  <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                      <i class="fas fa-user-graduate" style="color: #08ffa8; font-size: 20px;"></i>
                      <h5 style="margin: 0; font-weight: 600;">Manage Students</h5>
                  </div>
                  <p style="margin: 0; font-size: 14px; opacity: 0.7;">
                      View and manage student information and assignments
                  </p>
              </a>

              <!-- Collaborative Dashboard -->
              <a href="{{ url_for('classteacher.collaborative_marks_dashboard') }}" class="management-card" style="
                  background: rgba(255, 255, 255, 0.05);
                  border: 1px solid var(--glass-border);
                  border-radius: 12px;
                  padding: 20px;
                  text-decoration: none;
                  color: var(--text-color);
                  transition: all 0.3s ease;
                  display: block;
              ">
                  <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                      <i class="fas fa-users-cog" style="color: #00fffa; font-size: 20px;"></i>
                      <h5 style="margin: 0; font-weight: 600;">Collaborative Dashboard</h5>
                  </div>
                  <p style="margin: 0; font-size: 14px; opacity: 0.7;">
                      Monitor marks upload status and coordinate with teachers
                  </p>
              </a>

              <!-- Teacher Management -->
              <a href="{{ url_for('classteacher.teacher_management_hub') }}" class="management-card" style="
                  background: rgba(255, 255, 255, 0.05);
                  border: 1px solid var(--glass-border);
                  border-radius: 12px;
                  padding: 20px;
                  text-decoration: none;
                  color: var(--text-color);
                  transition: all 0.3s ease;
                  display: block;
              ">
                  <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                      <i class="fas fa-chalkboard-teacher" style="color: #f6e71c; font-size: 20px;"></i>
                      <h5 style="margin: 0; font-weight: 600;">Teacher Management</h5>
                  </div>
                  <p style="margin: 0; font-size: 14px; opacity: 0.7;">
                      Advanced teacher management and administrative tools
                  </p>
              </a>
          </div>
      </div>











      <!-- Bulk Upload Tab -->
      <div id="bulk-upload-tab" class="tab-content-section" style="
          background: var(--glass-bg);
          border: 1px solid var(--glass-border);
          border-radius: 20px;
          padding: 24px;
          margin: 24px 0;
          backdrop-filter: var(--glass-backdrop);
          box-shadow: var(--shadow-lg);
      ">
          <div class="tab-header">
              <h3 style="margin: 0 0 16px 0; color: var(--text-color); display: flex; align-items: center; gap: 12px;">
                  <i class="fas fa-file-upload" style="color: #08ffa8;"></i>
                  Bulk Upload Marks
              </h3>
              <p style="margin: 0 0 24px 0; color: var(--text-color); opacity: 0.8;">
                  Upload marks for multiple students using Excel or CSV files.
              </p>
          </div>

          <!-- Bulk Upload Form -->
          <form method="POST" action="{{ url_for('classteacher.dashboard') }}" enctype="multipart/form-data" class="premium-form">
              <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
              <input type="hidden" name="bulk_upload" value="1"/>

              <div class="form-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px;">
                  <div class="form-group">
                      <label for="bulk_grade" class="form-label">Grade Level</label>
                      <select name="grade" id="bulk_grade" class="form-select" required>
                          <option value="">Select Grade</option>
                          {% for grade_option in grades %}
                          <option value="{{ grade_option }}">{{ grade_option }}</option>
                          {% endfor %}
                      </select>
                  </div>

                  <div class="form-group">
                      <label for="bulk_stream" class="form-label">Stream</label>
                      <select name="stream" id="bulk_stream" class="form-select" required>
                          <option value="">Select Stream</option>
                      </select>
                  </div>

                  <div class="form-group">
                      <label for="bulk_term" class="form-label">Term</label>
                      <select name="term" id="bulk_term" class="form-select" required>
                          <option value="">Select Term</option>
                          {% for term_option in terms %}
                          <option value="{{ term_option }}">{{ term_option }}</option>
                          {% endfor %}
                      </select>
                  </div>

                  <div class="form-group">
                      <label for="bulk_assessment_type" class="form-label">Assessment Type</label>
                      <select name="assessment_type" id="bulk_assessment_type" class="form-select" required>
                          <option value="">Select Assessment</option>
                          {% for assessment_option in assessment_types %}
                          <option value="{{ assessment_option }}">{{ assessment_option }}</option>
                          {% endfor %}
                      </select>
                  </div>
              </div>

              <!-- File Upload Area -->
              <div class="file-upload-container" style="
                  background: rgba(255, 255, 255, 0.05);
                  border: 2px dashed var(--glass-border);
                  border-radius: 16px;
                  padding: 40px;
                  text-align: center;
                  margin-bottom: 24px;
                  transition: all 0.3s ease;
                  cursor: pointer;
              " onclick="document.getElementById('marks_file').click()">
                  <div class="file-upload-icon" style="
                      width: 80px;
                      height: 80px;
                      background: linear-gradient(135deg, #08ffa8, #00fffa);
                      border-radius: 50%;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin: 0 auto 16px;
                      font-size: 32px;
                      color: white;
                  ">
                      <i class="fas fa-cloud-upload-alt"></i>
                  </div>
                  <h4 style="margin: 0 0 8px 0; color: var(--text-color);">Drop marks file here or click to upload</h4>
                  <p style="margin: 0 0 16px 0; color: var(--text-color); opacity: 0.7;">
                      Supports: Excel (.xlsx, .xls), CSV files
                  </p>
                  <input type="file" id="marks_file" name="marks_file" accept=".xlsx,.xls,.csv" required style="display: none;">

                  <div class="file-restrictions" style="
                      background: rgba(246, 231, 28, 0.1);
                      border: 1px solid rgba(246, 231, 28, 0.2);
                      border-radius: 8px;
                      padding: 12px;
                      margin-top: 16px;
                      font-size: 14px;
                      color: var(--text-color);
                  ">
                      <strong style="color: #f6e71c;"><i class="fas fa-info-circle"></i> File Requirements:</strong><br>
                      Excel or CSV format with student names/admission numbers as rows and subjects as columns
                  </div>
              </div>

              <div class="form-actions" style="display: flex; gap: 12px; justify-content: flex-end;">
                  <button type="submit" class="premium-btn btn-success">
                      <i class="fas fa-upload"></i>
                      Upload Bulk Marks
                  </button>
                  <button type="button" onclick="downloadTemplate()" class="premium-btn btn-outline">
                      <i class="fas fa-download"></i>
                      Download Template
                  </button>
              </div>
          </form>
      </div>

      <!-- Recent Reports Tab -->
      <div id="recent-reports-tab" class="tab-content-section" style="
          background: var(--glass-bg);
          border: 1px solid var(--glass-border);
          border-radius: 20px;
          padding: 24px;
          margin: 24px 0;
          backdrop-filter: var(--glass-backdrop);
          box-shadow: var(--shadow-lg);
      ">
          <div class="tab-header">
              <h3 style="margin: 0 0 16px 0; color: var(--text-color); display: flex; align-items: center; gap: 12px;">
                  <i class="fas fa-clock" style="color: #08ffa8;"></i>
                  Recent Reports
              </h3>
              <p style="margin: 0 0 24px 0; color: var(--text-color); opacity: 0.8;">
                  View and download recently generated reports.
              </p>
          </div>

          {% if recent_reports %}
          <div class="reports-grid" style="display: grid; gap: 16px;">
              {% for report in recent_reports[:5] %}
              <div class="report-item" style="
                  background: rgba(255, 255, 255, 0.05);
                  border: 1px solid var(--glass-border);
                  border-radius: 12px;
                  padding: 16px;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
              ">
                  <div>
                      <h5 style="margin: 0 0 4px 0; color: var(--text-color);">{{ report.grade }} {{ report.stream }}</h5>
                      <p style="margin: 0; color: var(--text-color); opacity: 0.7; font-size: 14px;">
                          {{ report.term }} - {{ report.assessment_type }} ({{ report.date }})
                      </p>
                  </div>
                  <div style="display: flex; gap: 8px;">
                      <a href="{{ url_for('classteacher.preview_class_report', grade=report.grade, stream=report.stream, term=report.term, assessment_type=report.assessment_type) }}"
                         class="premium-btn btn-sm btn-outline">
                          <i class="fas fa-eye"></i>
                      </a>
                      <a href="{{ url_for('classteacher.download_class_report', grade=report.grade, stream=report.stream, term=report.term, assessment_type=report.assessment_type) }}"
                         class="premium-btn btn-sm btn-primary">
                          <i class="fas fa-download"></i>
                      </a>
                  </div>
              </div>
              {% endfor %}
          </div>
          {% else %}
          <div class="recent-reports-placeholder" style="text-align: center; padding: 40px;">
              <div style="
                  width: 80px;
                  height: 80px;
                  background: rgba(255, 255, 255, 0.1);
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin: 0 auto 16px;
                  font-size: 32px;
                  color: var(--text-color);
                  opacity: 0.5;
              ">
                  <i class="fas fa-file-alt"></i>
              </div>
              <h4 style="margin: 0 0 8px 0; color: var(--text-color);">No Recent Reports</h4>
              <p style="margin: 0 0 16px 0; color: var(--text-color); opacity: 0.7;">
                  Generate your first report to see it here.
              </p>
              <button onclick="switchMainTab('generate-reports')" class="premium-btn btn-outline">
                  <i class="fas fa-plus"></i>
                  Generate Report
              </button>
          </div>
          {% endif %}
      </div>

      <!-- Mobile Menu Overlay -->
      <div class="mobile-menu" id="mobileMenu">
        <button
          class="mobile-menu-close"
          onclick="toggleMobileMenu()"
          aria-label="Close menu"
        >
          <i class="fas fa-times"></i>
        </button>

        <div class="mobile-menu-content">
          <a
            href="{{ url_for('classteacher.dashboard') }}"
            class="mobile-menu-item"
          >
            <i class="fas fa-upload"></i> Upload Marks
          </a>
          <a
            href="{{ url_for('classteacher.analytics_dashboard') }}"
            class="mobile-menu-item"
          >
            <i class="fas fa-chart-pie"></i> Analytics
          </a>
          <a
            href="{{ url_for('classteacher.all_reports') }}"
            class="mobile-menu-item"
          >
            <i class="fas fa-file-pdf"></i> Reports
          </a>
          <a
            href="{{ url_for('classteacher.manage_students') }}"
            class="mobile-menu-item"
          >
            <i class="fas fa-user-graduate"></i> Students
          </a>
          <a
            href="{{ url_for('classteacher.collaborative_marks_dashboard') }}"
            class="mobile-menu-item"
          >
            <i class="fas fa-users-cog"></i> Collaboration
          </a>
          <a
            href="{{ url_for('classteacher.teacher_management_hub') }}"
            class="mobile-menu-item"
          >
            <i class="fas fa-cogs"></i> Management
          </a>
          <a
            href="{{ url_for('auth.logout_route') }}"
            class="mobile-menu-item"
            style="
              background: rgba(239, 68, 68, 0.2);
              border-color: rgba(239, 68, 68, 0.4);
            "
          >
            <i class="fas fa-sign-out-alt"></i> Logout
          </a>
        </div>
      </div>
    </div>

    <!-- Premium JavaScript -->
    <script>
      // 📱 MOBILE NAVIGATION FUNCTIONALITY
      function toggleMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        const isOpen = mobileMenu.classList.contains("show");

        if (isOpen) {
          mobileMenu.classList.remove("show");
          document.body.style.overflow = "";
        } else {
          mobileMenu.classList.add("show");
          document.body.style.overflow = "hidden";
        }
      }

      // Close mobile menu when clicking outside
      document.addEventListener("click", function (event) {
        const mobileMenu = document.getElementById("mobileMenu");
        const toggleButton = document.querySelector(".mobile-nav-toggle");

        if (
          mobileMenu.classList.contains("show") &&
          !mobileMenu.contains(event.target) &&
          !toggleButton.contains(event.target)
        ) {
          toggleMobileMenu();
        }
      });

      // Close mobile menu on escape key
      document.addEventListener("keydown", function (event) {
        if (event.key === "Escape") {
          const mobileMenu = document.getElementById("mobileMenu");
          if (mobileMenu.classList.contains("show")) {
            toggleMobileMenu();
          }
        }
      });

      // Handle orientation change
      window.addEventListener("orientationchange", function () {
        setTimeout(function () {
          const mobileMenu = document.getElementById("mobileMenu");
          if (mobileMenu.classList.contains("show")) {
            mobileMenu.style.height = window.innerHeight + "px";
          }
        }, 100);
      });

      // Mobile navigation ready

      // Add smooth scroll behavior
      document.documentElement.style.scrollBehavior = "smooth";

      // Add loading states to action cards
      document.querySelectorAll(".action-card").forEach((card) => {
        card.addEventListener("click", function () {
          this.style.opacity = "0.7";
          this.style.transform = "scale(0.98)";
        });
      });

      // Add notification system
      function showNotification(message, type = "info") {
        const notification = document.createElement("div");
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--card-bg);
                backdrop-filter: blur(20px);
                border: 1px solid var(--glass-border);
                border-radius: 12px;
                padding: 1rem 1.5rem;
                color: var(--text-color);
                z-index: 1000;
                animation: slideInRight 0.3s ease-out;
            `;
        document.body.appendChild(notification);

        setTimeout(() => {
          notification.remove();
        }, 5000);
      }

      // Debug function to send messages to terminal
      function debugToTerminal(message) {
        fetch('/classteacher/debug', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ message: message })
        }).catch(err => {
          // Fallback to console if fetch fails
          console.log('Debug:', message);
        });
      }

      // 🎯 TAB FUNCTIONALITY
      function switchMainTab(tabName) {
        debugToTerminal('switchMainTab called with: ' + tabName);

        // Hide all tab content containers
        const allSections = document.querySelectorAll('.tab-content-container');
        debugToTerminal('Found ' + allSections.length + ' tab content sections');
        allSections.forEach(section => {
          section.classList.remove('active');
        });

        // Remove active class from all tab buttons
        const allButtons = document.querySelectorAll('.premium-tab-button');
        debugToTerminal('Found ' + allButtons.length + ' tab buttons');
        allButtons.forEach(button => {
          button.classList.remove('active');
        });

        // Show selected tab content
        const targetTab = document.getElementById(tabName + '-tab');
        debugToTerminal('Looking for tab with ID: ' + tabName + '-tab');
        if (targetTab) {
          targetTab.classList.add('active');
          debugToTerminal('SUCCESS: Added active class to tab: ' + tabName + '-tab');
        } else {
          debugToTerminal('ERROR: Tab not found: ' + tabName + '-tab');
        }

        // Add active class to clicked button
        const targetButton = document.querySelector(`[data-tab="${tabName}"]`);
        debugToTerminal('Looking for button with data-tab: ' + tabName);
        if (targetButton) {
          targetButton.classList.add('active');
          debugToTerminal('SUCCESS: Added active class to button: ' + tabName);
        } else {
          debugToTerminal('ERROR: Button not found with data-tab: ' + tabName);
        }

        // Smooth scroll to tab content
        if (targetTab) {
          targetTab.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }

      // 📊 FORM FUNCTIONALITY
      function fetchStreams() {
        const gradeSelect = document.getElementById('grade');
        const streamSelect = document.getElementById('stream');
        const reportStreamSelect = document.getElementById('report_stream');

        if (!gradeSelect.value) {
          streamSelect.innerHTML = '<option value="">Select Stream</option>';
          if (reportStreamSelect) reportStreamSelect.innerHTML = '<option value="">Select Stream</option>';
          return;
        }

        // Fetch streams for the selected grade
        fetch(`/classteacher/get_streams_by_level/${gradeSelect.value}`)
          .then(response => response.json())
          .then(data => {
            streamSelect.innerHTML = '<option value="">Select Stream</option>';
            if (reportStreamSelect) reportStreamSelect.innerHTML = '<option value="">Select Stream</option>';

            if (data.streams) {
              data.streams.forEach(stream => {
                streamSelect.innerHTML += `<option value="${stream}">${stream}</option>`;
                if (reportStreamSelect) reportStreamSelect.innerHTML += `<option value="${stream}">${stream}</option>`;
              });
            }
          })
          .catch(error => {
            console.error('Error fetching streams:', error);
            showNotification('Error loading streams', 'error');
          });
      }

      // 📄 REPORT GENERATION
      function generateReport(action) {
        const grade = document.getElementById('report_grade').value;
        const stream = document.getElementById('report_stream').value;
        const term = document.getElementById('report_term').value;
        const assessmentType = document.getElementById('report_assessment_type').value;

        if (!grade || !stream || !term || !assessmentType) {
          showNotification('Please fill in all fields', 'error');
          return;
        }

        const url = action === 'preview'
          ? `/classteacher/preview_class_report/${grade}/${stream}/${term}/${assessmentType}`
          : `/classteacher/download_class_report/${grade}/${stream}/${term}/${assessmentType}`;

        if (action === 'preview') {
          window.open(url, '_blank');
        } else {
          window.location.href = url;
        }
      }

      // 📥 TEMPLATE DOWNLOAD
      function downloadTemplate() {
        window.location.href = '{{ url_for("classteacher.download_marks_template") }}';
      }

      // 🔄 CASCADING DROPDOWN FUNCTIONALITY
      // Store original grade options globally
      window.originalGradeOptions = {};

      // Function to store original grade options
      function storeOriginalGradeOptions() {
          const gradeSelects = ['grade'];
          gradeSelects.forEach(selectId => {
              const select = document.getElementById(selectId);
              if (select && !window.originalGradeOptions[selectId]) {
                  window.originalGradeOptions[selectId] = Array.from(select.options).map(opt => ({
                      value: opt.value,
                      textContent: opt.textContent
                  }));
                  console.log(`Stored ${window.originalGradeOptions[selectId].length} original options for ${selectId}`);
              }
          });
      }

      // Function to update subject options based on education level
      function updateSubjects() {
          const educationLevel = document.getElementById('education_level').value;
          const formGroup = document.getElementById('education_level').closest('.form-group');
          const gradeSelect = document.getElementById('grade');

          if (educationLevel) {
              formGroup.classList.add('success');
              formGroup.classList.remove('error');

              // Store the selected education level in a data attribute for later use
              gradeSelect.dataset.educationLevel = educationLevel;

              // Fetch subjects for this education level
              fetchSubjectsForEducationLevel(educationLevel);

              // Filter grades based on education level
              filterGradesByEducationLevel(educationLevel, gradeSelect);

              console.log("Updated subjects and filtered grades for education level:", educationLevel);
          } else {
              formGroup.classList.remove('success');

              // If no education level is selected, restore all grade options
              if (window.originalGradeOptions && window.originalGradeOptions['grade']) {
                  restoreOriginalGradeOptions(gradeSelect);
              }
          }
      }

      // Function to filter grades based on education level
      function filterGradesByEducationLevel(educationLevel, gradeSelect) {
          console.log("Filtering grades for education level:", educationLevel);

          // Define the mapping between education levels and grades
          const educationLevelGradeMapping = {
              'lower_primary': ['Grade 1', 'Grade 2', 'Grade 3'],
              'upper_primary': ['Grade 4', 'Grade 5', 'Grade 6'],
              'junior_secondary': ['Grade 7', 'Grade 8', 'Grade 9']
          };

          // Get the grades for the selected education level
          const gradesForLevel = educationLevelGradeMapping[educationLevel] || [];
          console.log("Grades for level:", gradesForLevel);

          // Make sure we have the original options stored
          if (!window.originalGradeOptions[gradeSelect.id]) {
              console.log("Original options not found, storing now");
              storeOriginalGradeOptions();
          }

          const originalOptions = window.originalGradeOptions[gradeSelect.id] || [];
          console.log("Original options:", originalOptions);

          // Clear current options
          gradeSelect.innerHTML = '';

          // Add the empty option first
          const emptyOption = document.createElement('option');
          emptyOption.value = '';
          emptyOption.textContent = 'Select Grade';
          gradeSelect.appendChild(emptyOption);

          // Add only the grades for the selected education level
          let optionsAdded = 0;

          // Add filtered options
          originalOptions.forEach(originalOpt => {
              // Skip the empty option as we've already added it
              if (originalOpt.value === '') return;

              // Check if this grade should be included for the selected education level
              const shouldInclude = gradesForLevel.some(grade =>
                  originalOpt.value.trim().toLowerCase() === grade.trim().toLowerCase()
              );

              if (shouldInclude) {
                  const newOption = document.createElement('option');
                  newOption.value = originalOpt.value;
                  newOption.textContent = originalOpt.textContent;
                  gradeSelect.appendChild(newOption);
                  optionsAdded++;
              }
          });

          console.log(`Added ${optionsAdded} filtered options for education level: ${educationLevel}`);

          // Clear the stream dropdown since grade has changed
          const streamSelect = document.getElementById('stream');
          if (streamSelect) {
              streamSelect.innerHTML = '<option value="">Select Stream</option>';
          }
      }

      // Function to restore original grade options
      function restoreOriginalGradeOptions(gradeSelect) {
          if (!window.originalGradeOptions || !window.originalGradeOptions[gradeSelect.id]) {
              console.error("Cannot restore original options - none stored for", gradeSelect.id);
              return;
          }

          const originalOptions = window.originalGradeOptions[gradeSelect.id];
          gradeSelect.innerHTML = '';

          originalOptions.forEach(optData => {
              const option = document.createElement('option');
              option.value = optData.value;
              option.textContent = optData.textContent;
              gradeSelect.appendChild(option);
          });

          console.log(`Restored ${originalOptions.length} original options for ${gradeSelect.id}`);
      }

      // Function to fetch streams dynamically based on selected grade
      function fetchStreams() {
          const grade = document.getElementById('grade').value;
          const streamSelect = document.getElementById('stream');
          const formGroup = streamSelect.closest('.form-group');

          console.log('=== FETCH STREAMS DEBUG ===');
          console.log('fetchStreams called with grade:', grade);

          // Clear existing options
          streamSelect.innerHTML = '<option value="">Select Stream</option>';

          if (grade) {
              // Extract grade level from "Grade X" format
              const gradeLevel = grade.replace('Grade ', '');
              console.log('Extracted grade level:', gradeLevel);

              // Make the API call to fetch streams
              fetch(`/classteacher/get_streams_by_level/${grade}`, {
                  credentials: 'same-origin'
              })
              .then(response => {
                  console.log('Fetch response status:', response.status);
                  if (!response.ok) {
                      throw new Error(`HTTP error! status: ${response.status}`);
                  }
                  return response.json();
              })
              .then(data => {
                  console.log('Received streams data:', data);

                  if (data.success && data.streams && data.streams.length > 0) {
                      data.streams.forEach(stream => {
                          const option = document.createElement('option');
                          option.value = stream.name;
                          option.textContent = stream.name;
                          streamSelect.appendChild(option);
                          console.log(`Added stream option: ${stream.name}`);
                      });

                      formGroup.classList.add('success');
                      formGroup.classList.remove('error');
                      console.log('✅ Successfully populated streams dropdown');
                  } else {
                      console.log('No streams found for grade:', grade);
                      const noStreamOption = document.createElement('option');
                      noStreamOption.value = '';
                      noStreamOption.textContent = 'No streams available';
                      noStreamOption.disabled = true;
                      streamSelect.appendChild(noStreamOption);
                  }
              })
              .catch(error => {
                  console.error('❌ Error fetching streams:', error);
                  formGroup.classList.add('error');
                  formGroup.classList.remove('success');

                  const errorOption = document.createElement('option');
                  errorOption.value = '';
                  errorOption.textContent = 'Error loading streams';
                  errorOption.disabled = true;
                  streamSelect.appendChild(errorOption);
              });
          } else {
              console.log('No grade selected, clearing form group success state');
              formGroup.classList.remove('success');
          }
      }

      // Function to fetch subjects for a specific education level
      function fetchSubjectsForEducationLevel(educationLevel) {
          fetch(`/classteacher/get_subjects_by_education_level/${educationLevel}`, {
              credentials: 'same-origin'
          })
          .then(response => response.json())
          .then(data => {
              console.log('Fetched subjects for education level:', educationLevel, data);
              // Store the subjects for later use
              window.availableSubjects = data.subjects;
          })
          .catch(error => {
              console.error('Error fetching subjects:', error);
          });
      }

      // 🔑 PERMISSION STATUS MANAGEMENT
      function refreshPermissionStatus() {
        const content = document.getElementById('permission-status-content');
        if (!content) return;

        content.innerHTML = `
          <div style="color: var(--text-color); opacity: 0.7;">
            <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 12px; color: #08ffa8;"></i>
            <p style="margin: 0;">Loading permission status...</p>
          </div>
        `;

        fetch('/classteacher/permission_status')
          .then(response => response.json())
          .then(data => {
            if (data.permissions && data.permissions.length > 0) {
              let html = '<div style="display: grid; gap: 12px;">';
              data.permissions.forEach(permission => {
                const statusColor = permission.status === 'active' ? '#08ffa8' : '#f6e71c';
                const statusIcon = permission.status === 'active' ? 'check-circle' : 'clock';
                html += `
                  <div style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 12px;
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 8px;
                    border-left: 4px solid ${statusColor};
                  ">
                    <div>
                      <strong style="color: var(--text-color);">${permission.grade} ${permission.stream}</strong>
                      <div style="font-size: 12px; opacity: 0.7; color: var(--text-color);">
                        ${permission.subjects.join(', ')}
                      </div>
                    </div>
                    <div style="color: ${statusColor};">
                      <i class="fas fa-${statusIcon}"></i>
                      ${permission.status}
                    </div>
                  </div>
                `;
              });
              html += '</div>';
              content.innerHTML = html;
            } else {
              content.innerHTML = `
                <div style="color: var(--text-color); opacity: 0.7;">
                  <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 12px; color: #00fffa;"></i>
                  <p style="margin: 0;">No class permissions found.</p>
                </div>
              `;
            }
          })
          .catch(error => {
            console.error('Error loading permissions:', error);
            content.innerHTML = `
              <div style="color: var(--text-color); opacity: 0.7;">
                <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 12px; color: #d21c00;"></i>
                <p style="margin: 0;">Error loading permissions.</p>
              </div>
            `;
          });
      }

      // 🎯 ENHANCED NAVIGATION
      function navigateToFeature(feature) {
        switchMainTab(feature);
        // Smooth scroll to the active tab
        const activeTab = document.getElementById(feature + '-tab');
        if (activeTab) {
          activeTab.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }

      // 📱 FILE UPLOAD ENHANCEMENTS
      function initializeFileUpload() {
        const fileInput = document.getElementById('marks_file');
        const uploadArea = document.querySelector('.file-upload-container');

        if (!fileInput || !uploadArea) return;

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
          e.preventDefault();
          uploadArea.style.borderColor = '#08ffa8';
          uploadArea.style.background = 'rgba(8, 255, 168, 0.1)';
        });

        uploadArea.addEventListener('dragleave', (e) => {
          e.preventDefault();
          uploadArea.style.borderColor = 'var(--glass-border)';
          uploadArea.style.background = 'rgba(255, 255, 255, 0.05)';
        });

        uploadArea.addEventListener('drop', (e) => {
          e.preventDefault();
          uploadArea.style.borderColor = 'var(--glass-border)';
          uploadArea.style.background = 'rgba(255, 255, 255, 0.05)';

          const files = e.dataTransfer.files;
          if (files.length > 0) {
            fileInput.files = files;
            showNotification('File selected: ' + files[0].name, 'success');
          }
        });

        // File selection feedback
        fileInput.addEventListener('change', (e) => {
          if (e.target.files.length > 0) {
            showNotification('File selected: ' + e.target.files[0].name, 'success');
          }
        });
      }

      // 📊 MARKS CALCULATION - Enhanced for full functionality
      function updatePercentage(input) {
        const mark = parseFloat(input.value) || 0;
        const maxMark = parseFloat(input.getAttribute('data-max-mark')) || parseFloat(input.max) || 100;
        const percentage = maxMark > 0 ? Math.round((mark / maxMark) * 100) : 0;

        const student = input.getAttribute('data-student');
        const subject = input.getAttribute('data-subject');

        // Update percentage display
        const percentageElement = document.getElementById(`percentage_${student}_${subject}`);
        const hiddenPercentageElement = document.getElementById(`hidden_percentage_${student}_${subject}`);
        const percentageBar = percentageElement ? percentageElement.closest('.percentage-display').querySelector('.percentage-fill') : null;

        if (percentageElement) {
          percentageElement.textContent = percentage + '%';

          // Color coding
          if (percentage >= 80) {
            percentageElement.style.color = '#08ffa8';
          } else if (percentage >= 60) {
            percentageElement.style.color = '#00fffa';
          } else if (percentage >= 40) {
            percentageElement.style.color = '#f6e71c';
          } else {
            percentageElement.style.color = '#d21c00';
          }
        }

        if (hiddenPercentageElement) {
          hiddenPercentageElement.value = percentage;
        }

        if (percentageBar) {
          percentageBar.style.width = percentage + '%';
        }
      }

      // Component marks calculation for composite subjects
      function updateComponentPercentage(input) {
        const mark = parseFloat(input.value) || 0;
        const maxMark = parseFloat(input.getAttribute('data-max-mark')) || 100;
        const percentage = maxMark > 0 ? Math.round((mark / maxMark) * 100) : 0;

        // Get component information
        const student = input.getAttribute('data-student');
        const subject = input.getAttribute('data-subject');
        const component = input.getAttribute('data-component');

        // Update individual component percentage display
        const componentPercentageElement = document.getElementById(`component_percentage_${student}_${subject}_${component}`);
        if (componentPercentageElement) {
          componentPercentageElement.textContent = percentage + '%';

          // Color coding for component percentage
          if (percentage >= 80) {
            componentPercentageElement.style.color = '#08ffa8';
          } else if (percentage >= 60) {
            componentPercentageElement.style.color = '#00fffa';
          } else if (percentage >= 40) {
            componentPercentageElement.style.color = '#f6e71c';
          } else {
            componentPercentageElement.style.color = '#d21c00';
          }
        }

        // Visual feedback for component input border
        if (percentage >= 80) {
          input.style.borderColor = '#08ffa8';
        } else if (percentage >= 60) {
          input.style.borderColor = '#00fffa';
        } else if (percentage >= 40) {
          input.style.borderColor = '#f6e71c';
        } else {
          input.style.borderColor = '#d21c00';
        }

        console.log(`Component ${component}: ${mark}/${maxMark} = ${percentage}%`);
      }

      // Calculate overall subject mark from components
      function calculateOverallSubjectMark(student, subjectId) {
        console.log(`🔄 Calculating overall mark for student: ${student}, subject: ${subjectId}`);

        const componentInputs = document.querySelectorAll(`input[data-student="${student}"][data-subject="${subjectId}"].component-mark`);
        console.log(`📊 Found ${componentInputs.length} component inputs`);

        let totalWeightedMark = 0;
        let totalWeight = 0;

        componentInputs.forEach((input, index) => {
          const mark = parseFloat(input.value) || 0;
          const maxMark = parseFloat(input.getAttribute('data-max-mark')) || 100;
          const weight = parseFloat(input.getAttribute('data-component-weight')) || 1;

          const percentage = maxMark > 0 ? (mark / maxMark) * 100 : 0;
          totalWeightedMark += percentage * weight;
          totalWeight += weight;

          console.log(`📈 Component ${index + 1}: ${mark}/${maxMark} = ${percentage.toFixed(1)}% (weight: ${weight})`);
        });

        const overallPercentage = totalWeight > 0 ? Math.round(totalWeightedMark / totalWeight) : 0;
        console.log(`🎯 Overall percentage: ${overallPercentage}% (weighted: ${totalWeightedMark.toFixed(1)}, total weight: ${totalWeight})`);

        // Update overall display
        const overallDisplay = document.getElementById(`overall_percentage_display_${student}_${subjectId}`);
        const overallMarkHidden = document.getElementById(`overall_mark_${student}_${subjectId}`);
        const overallPercentageHidden = document.getElementById(`hidden_percentage_${student}_${subjectId}`);
        const overallBar = overallDisplay ? overallDisplay.closest('.overall-percentage-display').querySelector('.percentage-fill') : null;

        console.log(`🎨 Updating display element: ${overallDisplay ? 'Found' : 'NOT FOUND'}`);

        if (overallDisplay) {
          overallDisplay.textContent = overallPercentage + '%';

          // Color coding
          if (overallPercentage >= 80) {
            overallDisplay.style.color = '#08ffa8';
          } else if (overallPercentage >= 60) {
            overallDisplay.style.color = '#00fffa';
          } else if (overallPercentage >= 40) {
            overallDisplay.style.color = '#f6e71c';
          } else {
            overallDisplay.style.color = '#d21c00';
          }

          console.log(`✅ Updated overall display to: ${overallPercentage}%`);
        } else {
          console.error(`❌ Could not find overall display element: overall_percentage_display_${student}_${subjectId}`);
        }

        if (overallMarkHidden) {
          overallMarkHidden.value = overallPercentage;
        }

        if (overallPercentageHidden) {
          overallPercentageHidden.value = overallPercentage;
        }

        if (overallBar) {
          overallBar.style.width = overallPercentage + '%';
        }
      }

      // Subject selection functions
      function selectAllSubjects() {
        document.querySelectorAll('.subject-checkbox').forEach(checkbox => {
          checkbox.checked = true;
          toggleSubjectVisibility(checkbox, checkbox.getAttribute('data-subject-id'));
        });
      }

      function deselectAllSubjects() {
        document.querySelectorAll('.subject-checkbox').forEach(checkbox => {
          checkbox.checked = false;
          toggleSubjectVisibility(checkbox, checkbox.getAttribute('data-subject-id'));
        });
      }

      function toggleSubjectVisibility(checkbox, subjectId) {
        const subjectColumns = document.querySelectorAll(`[data-subject-id="${subjectId}"]`);
        subjectColumns.forEach(column => {
          if (checkbox.checked) {
            column.style.display = '';
          } else {
            column.style.display = 'none';
          }
        });
      }

      // Validate max marks input
      function validateMaxMarks(input) {
        const value = parseInt(input.value);
        if (value < 1) {
          input.value = 1;
        } else if (value > 100) {
          input.value = 100;
        }
      }

      // Update all marks for a subject when max marks changes
      function updateAllMarksForSubject(subjectId, subjectIndex) {
        const maxMarksInput = document.getElementById(`total_marks_${subjectIndex}`);
        const newMaxMarks = maxMarksInput.value;

        // Update subject info display
        const subjectInfo = document.getElementById(`subject_info_${subjectIndex}`);
        if (subjectInfo) {
          subjectInfo.textContent = `Max Raw: ${newMaxMarks}`;
        }

        // Update all student mark inputs for this subject
        const studentInputs = document.querySelectorAll(`input[data-subject="${subjectId}"].student-mark:not(.component-mark)`);
        studentInputs.forEach(input => {
          input.max = newMaxMarks;
          input.setAttribute('data-max-mark', newMaxMarks);
          // Recalculate percentage
          updatePercentage(input);
        });
      }

      // Update component max marks
      function updateComponentMaxMarks(subjectId, componentId, input) {
        const newMaxMarks = parseInt(input.value) || 100;
        console.log(`🔧 Updating component max marks: Subject ${subjectId}, Component ${componentId}, New Max: ${newMaxMarks}`);

        // Update all student component inputs for this component
        const componentInputs = document.querySelectorAll(`input[data-subject="${subjectId}"][data-component="${componentId}"].component-mark`);
        console.log(`📝 Found ${componentInputs.length} student inputs to update`);

        componentInputs.forEach(componentInput => {
          componentInput.max = newMaxMarks;
          componentInput.setAttribute('data-max-mark', newMaxMarks);
          console.log(`✅ Updated input for student: ${componentInput.getAttribute('data-student')}`);

          // Recalculate component percentage
          updateComponentPercentage(componentInput);

          // Recalculate overall subject mark
          const student = componentInput.getAttribute('data-student');
          calculateOverallSubjectMark(student, subjectId);
        });

        // Update proportion display
        const proportionElement = document.getElementById(`proportion_${subjectId}_${componentId}`);
        if (proportionElement) {
          const weight = parseFloat(input.getAttribute('data-component-weight')) || 1;
          proportionElement.textContent = `Weight: ${weight}x, Max: ${newMaxMarks}`;
        }
      }

      // Add event listeners to mark inputs
      document.addEventListener('input', function(e) {
        if (e.target.classList.contains('student-mark')) {
          updatePercentage(e.target);
        }
      });

      // REMOVED DUPLICATE - downloadTemplate() function already exists above

      // 🔄 CASCADING DROPDOWNS
      function updateGradeOptions() {
        const educationLevel = document.getElementById('education_level').value;
        const gradeSelect = document.getElementById('grade');

        // Clear current options
        gradeSelect.innerHTML = '<option value="">Select Grade</option>';

        // Grade mappings
        const gradeMapping = {
          'pre_primary': ['PP1', 'PP2'],
          'lower_primary': ['Grade 1', 'Grade 2', 'Grade 3'],
          'upper_primary': ['Grade 4', 'Grade 5', 'Grade 6'],
          'junior_secondary': ['Grade 7', 'Grade 8', 'Grade 9']
        };

        if (educationLevel && gradeMapping[educationLevel]) {
          gradeMapping[educationLevel].forEach(grade => {
            const option = document.createElement('option');
            option.value = grade;
            option.textContent = grade;
            gradeSelect.appendChild(option);
          });
        }
      }

      function updateStreamOptions() {
        const grade = document.getElementById('grade').value;
        const streamSelect = document.getElementById('stream');

        // Clear current options
        streamSelect.innerHTML = '<option value="">Select Stream</option>';

        // Add common streams
        const streams = ['Stream A', 'Stream B', 'Stream C', 'Stream D'];
        streams.forEach(stream => {
          const option = document.createElement('option');
          option.value = stream;
          option.textContent = stream;
          streamSelect.appendChild(option);
        });
      }

      // Initialize default tab
      document.addEventListener('DOMContentLoaded', function() {
        // Show upload marks tab by default if no server-side active tab is set
        const serverActiveTab = "{{ active_tab or 'upload-marks' }}";
        const activeTab = serverActiveTab || localStorage.getItem('activeTab') || 'upload-marks';
        switchMainTab(activeTab);

        // Initialize enhanced features
        initializeFileUpload();
        refreshPermissionStatus();

        // Initialize grade change handlers for bulk upload
        const bulkGradeSelect = document.getElementById('bulk_grade');
        if (bulkGradeSelect) {
          bulkGradeSelect.addEventListener('change', function() {
            const streamSelect = document.getElementById('bulk_stream');
            if (this.value && streamSelect) {
              fetch(`/classteacher/get_streams_by_level/${this.value}`)
                .then(response => response.json())
                .then(data => {
                  streamSelect.innerHTML = '<option value="">Select Stream</option>';
                  if (data.streams) {
                    data.streams.forEach(stream => {
                      streamSelect.innerHTML += `<option value="${stream}">${stream}</option>`;
                    });
                  }
                })
                .catch(error => console.error('Error fetching streams:', error));
            }
          });
        }

        // Initialize percentage calculations for existing marks
        document.querySelectorAll('.student-mark').forEach(input => {
          if (input.value) {
            updatePercentage(input);
          }
        });
      });

      // 🚀 INITIALIZATION
      document.addEventListener('DOMContentLoaded', function() {
          console.log('🚀 Premium Dashboard Initializing...');

          // Test if elements exist
          const educationLevel = document.getElementById('education_level');
          const gradeSelect = document.getElementById('grade');
          const streamSelect = document.getElementById('stream');

          console.log('Elements found:', {
              educationLevel: !!educationLevel,
              gradeSelect: !!gradeSelect,
              streamSelect: !!streamSelect
          });

          // Store original grade options for all grade selects
          storeOriginalGradeOptions();

          // Add direct event listeners to education level dropdowns
          if (educationLevel) {
              console.log('Adding event listener to education level dropdown');
              educationLevel.addEventListener('change', function() {
                  console.log("🔄 Education level changed to:", this.value);
                  if (this.value) {
                      const gradeSelect = document.getElementById('grade');
                      filterGradesByEducationLevel(this.value, gradeSelect);
                  }
              });

              // Initialize main form if education level is already selected
              if (educationLevel.value) {
                  console.log("Initializing main form with education level:", educationLevel.value);
                  const gradeSelect = document.getElementById('grade');
                  filterGradesByEducationLevel(educationLevel.value, gradeSelect);
              }
          } else {
              console.log('❌ Education level dropdown not found!');
          }

          // Add event listener to grade dropdown
          if (gradeSelect) {
              console.log('Adding event listener to grade dropdown');
              gradeSelect.addEventListener('change', function() {
                  console.log("🔄 Grade changed to:", this.value);
                  if (this.value) {
                      fetchStreams();
                  }
              });

              // Initialize streams if grade is already selected
              if (gradeSelect.value) {
                  console.log("Initializing streams for grade:", gradeSelect.value);
                  fetchStreams();
              }
          } else {
              console.log('❌ Grade dropdown not found!');
          }

          console.log('✅ Premium Dashboard Initialized!');
      });

      // Test JavaScript execution
      console.log('🧪 JavaScript is working!');

      // Welcome message
      setTimeout(() => {
        showNotification("Welcome to your premium dashboard!", "success");
      }, 1000);

      // 🎯 UPLOAD MARKS FUNCTIONALITY

      // REMOVED DUPLICATE - fetchStreams() function already exists above

      // Function to update grades based on education level
      function updateGrades() {
        const educationLevel = document.getElementById('education_level').value;
        const gradeSelect = document.getElementById('grade');
        const streamSelect = document.getElementById('stream');

        // Clear current options
        gradeSelect.innerHTML = '<option value="">Select Grade</option>';
        streamSelect.innerHTML = '<option value="">Select Stream</option>';

        // Grade options by education level
        const gradesByEducationLevel = {
          'lower_primary': ['Grade 1', 'Grade 2', 'Grade 3'],
          'upper_primary': ['Grade 4', 'Grade 5', 'Grade 6'],
          'junior_secondary': ['Grade 7', 'Grade 8', 'Grade 9']
        };

        if (educationLevel && gradesByEducationLevel[educationLevel]) {
          gradesByEducationLevel[educationLevel].forEach(grade => {
            const option = document.createElement('option');
            option.value = grade;
            option.textContent = grade;
            gradeSelect.appendChild(option);
          });
        }
      }

      // Function to update composite subject totals
      function updateCompositeTotal(studentId, subjectId) {
        const componentInputs = document.querySelectorAll(`input[data-student="${studentId}"][data-subject="${subjectId}"]`);
        let total = 0;

        componentInputs.forEach(input => {
          const value = parseFloat(input.value) || 0;
          total += value;
        });

        const totalElement = document.getElementById(`total_${studentId}_${subjectId}`);
        if (totalElement) {
          totalElement.textContent = total.toFixed(1);
        }
      }

      // Function to validate individual marks
      function validateMark(input) {
        const value = parseFloat(input.value);
        const max = parseFloat(input.getAttribute('max')) || 100;

        if (value > max) {
          input.value = max;
          showNotification(`Mark cannot exceed ${max}`, 'warning');
        }

        if (value < 0) {
          input.value = 0;
          showNotification('Mark cannot be negative', 'warning');
        }

        // Update progress
        updateMarksProgress();
      }

      // Function to clear all marks
      function clearAllMarks() {
        if (confirm('Are you sure you want to clear all marks? This action cannot be undone.')) {
          const markInputs = document.querySelectorAll('.mark-input, .component-mark-input');
          markInputs.forEach(input => {
            input.value = '';
          });

          // Update composite totals
          const compositeTotals = document.querySelectorAll('[id^="total_"]');
          compositeTotals.forEach(total => {
            total.textContent = '0';
          });

          updateMarksProgress();
          showNotification('All marks cleared', 'info');
        }
      }

      // Function to update marks entry progress
      function updateMarksProgress() {
        const markInputs = document.querySelectorAll('.mark-input, .component-mark-input');
        const filledInputs = Array.from(markInputs).filter(input => input.value.trim() !== '');

        const marksEnteredElement = document.getElementById('marks-entered');
        const progressBar = document.getElementById('marks-progress');

        if (marksEnteredElement) {
          marksEnteredElement.textContent = filledInputs.length;
        }

        if (progressBar && markInputs.length > 0) {
          const percentage = (filledInputs.length / markInputs.length) * 100;
          progressBar.style.width = percentage + '%';
        }
      }

      // REMOVED DUPLICATES - selectAllSubjects() and deselectAllSubjects() functions already exist above

      // REMOVED DUPLICATE - toggleSubjectVisibility() function already exists above

      // Function to select class for upload (for multi-class teachers)
      function selectClassForUpload(grade, stream, educationLevel) {
        // Fill the form with selected class details
        document.getElementById('education_level').value = educationLevel;
        document.getElementById('grade').value = grade;

        // Update grades dropdown first
        updateGrades();

        // Wait a bit for grades to populate, then set grade and fetch streams
        setTimeout(() => {
          document.getElementById('grade').value = grade;
          fetchStreams();

          // Wait for streams to populate, then set stream
          setTimeout(() => {
            document.getElementById('stream').value = stream;
          }, 100);
        }, 100);

        showNotification(`Selected ${grade} Stream ${stream} for marks upload`, 'info');
      }

      // REMOVED DUPLICATE - DOMContentLoaded listener already exists above
      // Marks progress tracking is handled in the main DOMContentLoaded listener

      // Auto-scroll to upload form if students are being shown
      {% if show_students %}
      console.log("Students form is visible - auto-scrolling to upload form");
      setTimeout(() => {
          const pupilsList = document.getElementById('pupils-list');
          if (pupilsList) {
              pupilsList.scrollIntoView({
                  behavior: 'smooth',
                  block: 'start',
                  inline: 'nearest'
              });
              console.log("Scrolled to pupils-list");
          }
      }, 500); // Small delay to ensure page is fully rendered
      {% endif %}

    </script>
  </body>
</html>
