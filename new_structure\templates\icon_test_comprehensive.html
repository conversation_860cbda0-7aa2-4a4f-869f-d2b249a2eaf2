<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Icon Test - Hillview School Management System</title>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Icon Loading System -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/font_awesome_fixes.css') }}">
    <script src="{{ url_for('static', filename='js/icon_loader.js') }}"></script>
    <script src="{{ url_for('static', filename='js/font_awesome_helper.js') }}"></script>
    
    <style>
        body {
            font-family: 'Inter', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #2d3748;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-section h2 {
            color: #667eea;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .icon-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        
        .icon-item i {
            font-size: 20px;
            width: 30px;
            text-align: center;
            color: #667eea;
        }
        
        .status-indicator {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-left: auto;
        }
        
        .status-good { background: #c6f6d5; color: #22543d; }
        .status-bad { background: #fed7d7; color: #742a2a; }
        .status-fallback { background: #fef5e7; color: #744210; }
        
        .system-status {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
        }
        
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px 5px;
        }
        
        .refresh-btn:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-school"></i> Hillview Icon System Test</h1>
            <p>Comprehensive test of all icons used in the school management system</p>
            <div id="system-status" class="system-status">
                <h3><i class="fas fa-cog fa-spin"></i> Checking Icon System...</h3>
                <p>Please wait while we verify all icons are loading correctly.</p>
            </div>
            <button class="refresh-btn" onclick="window.HillviewIcons.reload()">
                <i class="fas fa-sync-alt"></i> Reload Icons
            </button>
            <button class="refresh-btn" onclick="window.HillviewIcons.applyFallbacks()">
                <i class="fas fa-shield-alt"></i> Apply Fallbacks
            </button>
            <button class="refresh-btn" onclick="window.HillviewIcons.fixSquares()">
                <i class="fas fa-wrench"></i> Fix Squares
            </button>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-bars"></i> Navigation Icons</h2>
            <div class="icon-grid">
                <div class="icon-item">
                    <i class="fas fa-bars"></i>
                    <span>Menu (fa-bars)</span>
                    <span class="status-indicator" id="status-fa-bars">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-times"></i>
                    <span>Close (fa-times)</span>
                    <span class="status-indicator" id="status-fa-times">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-home"></i>
                    <span>Home (fa-home)</span>
                    <span class="status-indicator" id="status-fa-home">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-chevron-left"></i>
                    <span>Previous (fa-chevron-left)</span>
                    <span class="status-indicator" id="status-fa-chevron-left">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-chevron-right"></i>
                    <span>Next (fa-chevron-right)</span>
                    <span class="status-indicator" id="status-fa-chevron-right">Testing...</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-chalkboard-teacher"></i> Teacher Dashboard Icons</h2>
            <div class="icon-grid">
                <div class="icon-item">
                    <i class="fas fa-chalkboard-teacher"></i>
                    <span>Teacher (fa-chalkboard-teacher)</span>
                    <span class="status-indicator" id="status-fa-chalkboard-teacher">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-user-tie"></i>
                    <span>Staff (fa-user-tie)</span>
                    <span class="status-indicator" id="status-fa-user-tie">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-users"></i>
                    <span>Students (fa-users)</span>
                    <span class="status-indicator" id="status-fa-users">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-book"></i>
                    <span>Subjects (fa-book)</span>
                    <span class="status-indicator" id="status-fa-book">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-layer-group"></i>
                    <span>Grades (fa-layer-group)</span>
                    <span class="status-indicator" id="status-fa-layer-group">Testing...</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-chart-bar"></i> Analytics & Reports Icons</h2>
            <div class="icon-grid">
                <div class="icon-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>Bar Chart (fa-chart-bar)</span>
                    <span class="status-indicator" id="status-fa-chart-bar">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-chart-line"></i>
                    <span>Line Chart (fa-chart-line)</span>
                    <span class="status-indicator" id="status-fa-chart-line">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-file-pdf"></i>
                    <span>PDF Report (fa-file-pdf)</span>
                    <span class="status-indicator" id="status-fa-file-pdf">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-print"></i>
                    <span>Print (fa-print)</span>
                    <span class="status-indicator" id="status-fa-print">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-download"></i>
                    <span>Download (fa-download)</span>
                    <span class="status-indicator" id="status-fa-download">Testing...</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-cogs"></i> Action Icons</h2>
            <div class="icon-grid">
                <div class="icon-item">
                    <i class="fas fa-upload"></i>
                    <span>Upload (fa-upload)</span>
                    <span class="status-indicator" id="status-fa-upload">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-edit"></i>
                    <span>Edit (fa-edit)</span>
                    <span class="status-indicator" id="status-fa-edit">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-trash"></i>
                    <span>Delete (fa-trash)</span>
                    <span class="status-indicator" id="status-fa-trash">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-eye"></i>
                    <span>View (fa-eye)</span>
                    <span class="status-indicator" id="status-fa-eye">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings (fa-cog)</span>
                    <span class="status-indicator" id="status-fa-cog">Testing...</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-info-circle"></i> Status Icons</h2>
            <div class="icon-grid">
                <div class="icon-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Success (fa-check-circle)</span>
                    <span class="status-indicator" id="status-fa-check-circle">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Warning (fa-exclamation-triangle)</span>
                    <span class="status-indicator" id="status-fa-exclamation-triangle">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-info-circle"></i>
                    <span>Info (fa-info-circle)</span>
                    <span class="status-indicator" id="status-fa-info-circle">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>Loading (fa-spinner)</span>
                    <span class="status-indicator" id="status-fa-spinner">Testing...</span>
                </div>
                <div class="icon-item">
                    <i class="fas fa-lock"></i>
                    <span>Locked (fa-lock)</span>
                    <span class="status-indicator" id="status-fa-lock">Testing...</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test each icon and update status
        function testIcon(iconClass) {
            const element = document.querySelector(`.${iconClass}`);
            const statusElement = document.getElementById(`status-${iconClass}`);
            
            if (!element || !statusElement) return;
            
            const computedStyle = window.getComputedStyle(element, ':before');
            const fontFamily = computedStyle.getPropertyValue('font-family');
            const content = computedStyle.getPropertyValue('content');
            
            let status = 'bad';
            let text = 'Failed';
            
            if (fontFamily && fontFamily.includes('Font Awesome')) {
                status = 'good';
                text = 'Font Awesome';
            } else if (element.textContent && element.textContent.trim()) {
                status = 'fallback';
                text = 'Unicode Fallback';
            } else if (content && content !== 'none' && content !== '""') {
                status = 'good';
                text = 'CSS Content';
            }
            
            statusElement.className = `status-indicator status-${status}`;
            statusElement.textContent = text;
        }
        
        // Test all icons
        function testAllIcons() {
            const iconClasses = [
                'fa-bars', 'fa-times', 'fa-home', 'fa-chevron-left', 'fa-chevron-right',
                'fa-chalkboard-teacher', 'fa-user-tie', 'fa-users', 'fa-book', 'fa-layer-group',
                'fa-chart-bar', 'fa-chart-line', 'fa-file-pdf', 'fa-print', 'fa-download',
                'fa-upload', 'fa-edit', 'fa-trash', 'fa-eye', 'fa-cog',
                'fa-check-circle', 'fa-exclamation-triangle', 'fa-info-circle', 'fa-spinner', 'fa-lock'
            ];
            
            iconClasses.forEach(testIcon);
            
            // Update system status
            const systemStatus = document.getElementById('system-status');
            const isLoaded = window.HillviewIcons && window.HillviewIcons.isLoaded();
            
            if (isLoaded) {
                systemStatus.innerHTML = `
                    <h3><i class="fas fa-check-circle"></i> Icon System Status: ACTIVE</h3>
                    <p>Font Awesome is loaded and working correctly. All icons should display properly.</p>
                `;
                systemStatus.style.background = 'linear-gradient(135deg, #48bb78 0%, #38a169 100%)';
            } else {
                systemStatus.innerHTML = `
                    <h3><i class="fas fa-exclamation-triangle"></i> Icon System Status: FALLBACK MODE</h3>
                    <p>Font Awesome failed to load. Using Unicode fallback icons where available.</p>
                `;
                systemStatus.style.background = 'linear-gradient(135deg, #ed8936 0%, #dd6b20 100%)';
            }
        }
        
        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(testAllIcons, 1000);
            
            // Re-test every 5 seconds
            setInterval(testAllIcons, 5000);
        });
    </script>
</body>
</html>
