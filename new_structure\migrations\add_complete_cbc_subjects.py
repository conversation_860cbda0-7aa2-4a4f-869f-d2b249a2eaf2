#!/usr/bin/env python3
"""
Migration script to add complete CBC subjects for all educational levels.
This implements the rationalized CBC curriculum structure (2024) for:
- Lower Primary (7 subjects)
- Upper Primary (8 subjects) 
- Junior Secondary (9 subjects)
"""

import sys
import os
from datetime import datetime

# Add the parent directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
grandparent_dir = os.path.dirname(parent_dir)
sys.path.insert(0, grandparent_dir)

def add_complete_cbc_subjects():
    """Add complete CBC subjects for all educational levels."""
    
    try:
        from new_structure import create_app
        from new_structure.extensions import db
        from new_structure.models.academic import Subject
        
        print("📚 Adding Complete CBC Subjects (Rationalized 2024)")
        print("=" * 60)
        
        # Create Flask app context
        app = create_app('development')
        
        with app.app_context():
            # Check current database connection
            print(f"📍 Database URI: {app.config['SQLALCHEMY_DATABASE_URI']}")
            
            # Define complete CBC subjects structure (Rationalized 2024)
            cbc_subjects = {
                'lower_primary': [
                    # 7 Learning Areas for Lower Primary (Grades 1-3)
                    {'name': 'Indigenous Language', 'is_composite': False},
                    {'name': 'Kiswahili', 'is_composite': True},  # Language components
                    {'name': 'English', 'is_composite': True},   # Language components
                    {'name': 'Mathematics', 'is_composite': False},
                    {'name': 'Religious Education', 'is_composite': False},
                    {'name': 'Environmental Activities', 'is_composite': False},  # Includes Hygiene & Nutrition
                    {'name': 'Creative Activities', 'is_composite': False}
                ],
                
                'upper_primary': [
                    # 8 Learning Areas for Upper Primary (Grades 4-6)
                    {'name': 'English', 'is_composite': True},   # Language components
                    {'name': 'Mathematics', 'is_composite': False},
                    {'name': 'Kiswahili', 'is_composite': True}, # Language components
                    {'name': 'Religious Education', 'is_composite': False},
                    {'name': 'Agriculture and Nutrition', 'is_composite': True},  # Merged Agri + Home Science
                    {'name': 'Social Studies', 'is_composite': False},
                    {'name': 'Creative Arts', 'is_composite': True},  # Merged Creative Arts + Music + PHE
                    {'name': 'Science and Technology', 'is_composite': True}
                ],
                
                'junior_secondary': [
                    # 9 Compulsory Learning Areas for Junior Secondary (Grades 7-9)
                    {'name': 'English', 'is_composite': True},   # Language components
                    {'name': 'Kiswahili', 'is_composite': True}, # Language components
                    {'name': 'Mathematics', 'is_composite': False},
                    {'name': 'Religious Education', 'is_composite': False},
                    {'name': 'Social Studies', 'is_composite': True},  # Merged with Life Skills
                    {'name': 'Integrated Science', 'is_composite': True},  # Merged with Health Education
                    {'name': 'Pre-Technical Studies', 'is_composite': True},  # Merged Computer + Business
                    {'name': 'Agriculture and Nutrition', 'is_composite': True},  # Merged Agri + Home Science
                    {'name': 'Creative Arts and Sports', 'is_composite': True}  # Merged Visual + Performing + Sports + PE
                ]
            }
            
            total_added = 0
            
            for education_level, subjects in cbc_subjects.items():
                print(f"\n🎯 Processing {education_level.replace('_', ' ').title()} subjects...")
                
                # Check existing subjects for this level
                existing_subjects = Subject.query.filter_by(education_level=education_level).all()
                existing_names = [s.name for s in existing_subjects]
                
                print(f"📋 Current {education_level} subjects: {len(existing_subjects)}")
                
                level_added = 0
                for subject_data in subjects:
                    subject_name = subject_data['name']
                    is_composite = subject_data['is_composite']
                    
                    if subject_name not in existing_names:
                        new_subject = Subject(
                            name=subject_name,
                            education_level=education_level,
                            is_composite=is_composite
                        )
                        db.session.add(new_subject)
                        level_added += 1
                        total_added += 1
                        composite_text = " (Composite)" if is_composite else ""
                        print(f"✅ Added: {subject_name}{composite_text}")
                    else:
                        print(f"⚠️  Exists: {subject_name}")
                
                print(f"📊 Added {level_added} new subjects for {education_level}")
            
            # Commit all changes
            if total_added > 0:
                db.session.commit()
                print(f"\n🎉 Successfully added {total_added} CBC subjects!")
            else:
                print(f"\n✅ All CBC subjects already exist in database")
            
            return True
            
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_cbc_subjects():
    """Verify that all CBC subjects were added correctly."""
    
    try:
        from new_structure import create_app
        from new_structure.models.academic import Subject
        
        app = create_app('development')
        
        with app.app_context():
            print("\n🔍 Verification - Complete CBC Subject Structure:")
            print("=" * 60)
            
            levels = ['pre_primary', 'lower_primary', 'upper_primary', 'junior_secondary']
            expected_counts = {
                'pre_primary': 13,      # Already added
                'lower_primary': 7,     # CBC rationalized
                'upper_primary': 8,     # CBC rationalized  
                'junior_secondary': 9   # CBC rationalized
            }
            
            total_subjects = 0
            all_correct = True
            
            for level in levels:
                subjects = Subject.query.filter_by(education_level=level).order_by(Subject.name).all()
                count = len(subjects)
                expected = expected_counts.get(level, 0)
                
                status = "✅" if count >= expected else "⚠️"
                print(f"\n{status} {level.replace('_', ' ').title()}: {count} subjects")
                
                for subject in subjects:
                    composite_text = " (Composite)" if subject.is_composite else ""
                    print(f"   - {subject.name}{composite_text}")
                
                total_subjects += count
                if count < expected:
                    all_correct = False
            
            print(f"\n📊 Total Subjects: {total_subjects}")
            print(f"🎯 CBC Structure: {'Complete' if all_correct else 'Incomplete'}")
            
            return all_correct
            
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Complete CBC Subjects Migration (Rationalized 2024)")
    print("=" * 60)
    
    # Run the migration
    success = add_complete_cbc_subjects()
    
    if success:
        print("\n🔍 Running verification...")
        verification_success = verify_cbc_subjects()
        
        if verification_success:
            print("\n" + "=" * 60)
            print("✅ COMPLETE CBC SUBJECTS MIGRATION COMPLETED!")
            print("📚 Rationalized CBC Structure (2024) Implemented:")
            print("\n🌟 Pre-Primary (PP1, PP2): 13 subjects")
            print("   - Early childhood development activities")
            print("\n🎓 Lower Primary (Grades 1-3): 7 subjects")
            print("   - Indigenous Language, Kiswahili, English")
            print("   - Mathematics, Religious Education")
            print("   - Environmental Activities, Creative Activities")
            print("\n📚 Upper Primary (Grades 4-6): 8 subjects")
            print("   - English, Mathematics, Kiswahili, Religious Education")
            print("   - Agriculture & Nutrition, Social Studies")
            print("   - Creative Arts, Science & Technology")
            print("\n🏫 Junior Secondary (Grades 7-9): 9 subjects")
            print("   - English, Kiswahili, Mathematics, Religious Education")
            print("   - Social Studies, Integrated Science")
            print("   - Pre-Technical Studies, Agriculture & Nutrition")
            print("   - Creative Arts & Sports")
            print("\n🎯 Features:")
            print("   - Composite subjects marked for component tracking")
            print("   - Aligned with KICD rationalized curriculum")
            print("   - Ready for marks upload and reporting")
            print("=" * 60)
        else:
            print("\n⚠️ VERIFICATION ISSUES FOUND!")
    else:
        print("\n❌ MIGRATION FAILED!")
        sys.exit(1)
