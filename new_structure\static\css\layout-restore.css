/* LAYOUT RESTORATION CSS - Simple Original Layout with New Colors */
/* This file restores the original classteacher layout while keeping the new color scheme */

/* Simple, clean navbar */
.navbar {
  background: var(--custom-primary) !important;
  background-image: none !important;
  padding: 1rem 2rem !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  position: relative !important;
}

.navbar-brand {
  color: white !important;
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  text-decoration: none !important;
}

.navbar-nav {
  display: flex !important;
  list-style: none !important;
  margin: 0 !important;
  padding: 0 !important;
  gap: 1rem !important;
  align-items: center !important;
}

.navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  text-decoration: none !important;
  padding: 0.5rem 1rem !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

.navbar-nav .nav-link:hover {
  color: white !important;
  background: rgba(255, 255, 255, 0.15) !important;
}

.navbar-nav .logout-btn {
  background: rgba(220, 53, 69, 0.2) !important;
  border: 1px solid rgba(220, 53, 69, 0.3) !important;
  color: white !important;
}

.navbar-nav .logout-btn:hover {
  background: rgba(220, 53, 69, 0.4) !important;
}

/* Override complex grid systems with simple layouts */
.modern-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  gap: 1.5rem !important;
  margin: 0 !important;
  padding: 0 !important;
}

.grid-cols-3 {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
}

/* Simplify the management section */
#management-section {
  background-color: white !important;
  border-radius: 12px !important;
  padding: 2rem !important;
  margin-bottom: 2rem !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e8e8e8 !important;
}

/* Simple card header */
.card-header {
  margin-bottom: 1.5rem !important;
  padding-bottom: 1rem !important;
  border-bottom: 2px solid #f0f0f0 !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
}

.card-title {
  color: var(--custom-primary) !important;
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

.card-icon {
  color: var(--custom-primary) !important;
  font-size: 1.2rem !important;
}

/* Simple management filters */
.management-filters {
  background-color: #f8f9fa !important;
  padding: 1.5rem !important;
  border-radius: 8px !important;
  margin-bottom: 2rem !important;
  border: 1px solid #e8e8e8 !important;
}

/* Restore simple main container */
.main-container {
  max-width: 1200px !important;
  margin: 0 auto !important;
  padding: 2rem !important;
}

/* Simple content wrapper */
.content-wrapper {
  background-color: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Override complex modern containers */
.modern-container {
  max-width: 1200px !important;
  margin: 0 auto !important;
  padding: 2rem !important;
  background: none !important;
}

/* Simplify modern cards */
.modern-card {
  background-color: white !important;
  border-radius: 12px !important;
  padding: 2rem !important;
  margin-bottom: 2rem !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid #e8e8e8 !important;
}

/* Remove complex animations and transitions */
.slide-up {
  animation: none !important;
  transform: none !important;
}

/* Simplify quick action cards layout */
.quick-action-card {
  display: block !important;
  padding: 1.5rem !important;
  text-decoration: none !important;
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
  height: auto !important;
  min-height: 160px !important;
}

.quick-action-icon {
  margin-bottom: 1rem !important;
  font-size: 2rem !important;
}

.quick-action-title {
  margin-bottom: 0.5rem !important;
  font-size: 1.1rem !important;
}

.quick-action-desc {
  margin-bottom: 1rem !important;
  font-size: 0.9rem !important;
  line-height: 1.4 !important;
}

/* Simple responsive behavior */
@media (max-width: 768px) {
  .modern-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  .main-container,
  .modern-container {
    padding: 1rem !important;
  }

  .modern-card,
  #management-section {
    padding: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }

  .card-header {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 1rem !important;
  }
}

/* Remove complex gradients and overlays */
.modern-header {
  background: white !important;
  backdrop-filter: none !important;
  border: 1px solid #e8e8e8 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Simplify the body background */
body {
  background: var(--custom-bg) !important;
  background-image: none !important;
}

/* Override complex button styles with simple ones */
.modern-btn {
  padding: 0.5rem 1rem !important;
  border-radius: 6px !important;
  font-weight: 600 !important;
  border: 1px solid transparent !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.btn-outline {
  background: transparent !important;
  color: var(--custom-primary) !important;
  border-color: var(--custom-primary) !important;
}

.btn-outline:hover {
  background: var(--custom-primary) !important;
  color: white !important;
}

/* Simple form inputs */
.form-input {
  width: 100% !important;
  padding: 0.75rem !important;
  border: 1px solid #ddd !important;
  border-radius: 6px !important;
  font-size: 0.95rem !important;
  background: white !important;
  color: var(--custom-secondary) !important;
}

.form-input:focus {
  outline: none !important;
  border-color: var(--custom-primary) !important;
  box-shadow: 0 0 0 2px rgba(38, 58, 209, 0.1) !important;
}

/* Remove any complex positioning */
.dashboard-grid {
  display: block !important;
}

.dashboard-full-width {
  grid-column: unset !important;
}

/* Ensure consistent spacing */
.modern-card + .modern-card {
  margin-top: 2rem !important;
}
