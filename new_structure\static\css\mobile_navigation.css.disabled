/* =============================================================================
   HILLVIEW SCHOOL MANAGEMENT SYSTEM - MOBILE-FIRST NAVIGATION
   Clean, Modern, Responsive Design for Class Teacher Dashboard
   ============================================================================= */

/* Core Mobile-First Navigation Styles */
.mobile-nav {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-nav.active {
  opacity: 1;
  visibility: visible;
}

.mobile-nav-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  min-width: 300px;
  max-width: 400px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.mobile-nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.mobile-nav-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}

.mobile-nav-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.mobile-nav-close:hover {
  background: #f0f0f0;
  color: #333;
}

.mobile-nav-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-nav-links li {
  margin-bottom: 0.5rem;
}

.mobile-nav-links a {
  display: flex;
  align-items: center;
  padding: 1rem;
  color: #333;
  text-decoration: none;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  font-weight: 500;
}

.mobile-nav-links a:hover {
  background: #f8f9fa;
  color: #667eea;
  text-decoration: none;
}

.mobile-nav-links a i {
  margin-right: 0.75rem;
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
}

.mobile-nav-toggle {
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  padding: 0.5rem;
  cursor: pointer;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}

.mobile-nav-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

.mobile-nav-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

/* Mobile-First Responsive Design */
@media (max-width: 768px) {
  .mobile-nav-toggle {
    display: block;
  }

  .desktop-nav {
    display: none;
  }

  .mobile-nav {
    display: block;
  }

  /* Navigation becomes mobile-first */
  .navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--bg-gradient-primary);
  }

  .navbar-brand {
    font-size: 1.2rem;
    font-weight: 700;
    color: white;
  }

  /* Content adjustments for mobile */
  .modern-container {
    padding: 1rem;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  /* Form improvements */
  .modern-form {
    padding: 1.5rem;
  }

  .modern-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-control,
  .form-select {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.75rem;
  }

  .modern-btn {
    width: 100%;
    min-height: 44px;
    padding: 0.75rem;
    font-size: 16px;
    margin: 0.5rem 0;
  }

  /* Table improvements */
  .table-wrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 0.5rem;
    margin: 1rem 0;
  }

  .table-wrapper table {
    min-width: 600px;
    font-size: 14px;
  }

  .student-mark,
  .component-mark {
    min-width: 60px;
    height: 40px;
    font-size: 14px;
  }

  /* Tab navigation */
  .tab-nav {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .tab-button {
    flex: 1;
    min-width: 120px;
    padding: 0.75rem;
    min-height: 44px;
    font-size: 14px;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
  }

  /* Quick actions */
  .modern-quick-actions {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .quick-action-card {
    min-height: 80px;
    padding: 1rem;
    touch-action: manipulation;
  }

  /* Management cards */
  .management-options-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .management-option-card {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .navbar-brand {
    font-size: 1rem;
  }

  .modern-container {
    padding: 0.75rem;
  }

  .mobile-nav-content {
    min-width: 280px;
    padding: 1.5rem;
  }

  .modern-btn {
    min-height: 48px;
    font-size: 0.9rem;
  }

  .form-control,
  .form-select {
    min-height: 48px;
  }

  .tab-button {
    min-height: 48px;
    font-size: 0.85rem;
  }
}

/* Desktop styles */
@media (min-width: 769px) {
  .mobile-nav-toggle {
    display: none;
  }

  .mobile-nav {
    display: none;
  }

  .desktop-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .nav-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    font-weight: 500;
  }

  .nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    text-decoration: none;
  }

  .logout-btn {
    background: rgba(220, 53, 69, 0.2);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #ff6b6b;
  }

  .logout-btn:hover {
    background: rgba(220, 53, 69, 0.3);
    color: white;
  }
}
