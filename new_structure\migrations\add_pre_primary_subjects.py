#!/usr/bin/env python3
"""
Migration script to add Pre-Primary subjects to the database.
This adds appropriate subjects for PP1 and PP2 levels based on Kenya CBC curriculum.
"""

import sys
import os
from datetime import datetime

# Add the parent directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
grandparent_dir = os.path.dirname(parent_dir)
sys.path.insert(0, grandparent_dir)

def add_pre_primary_subjects():
    """Add Pre-Primary subjects to the database."""
    
    try:
        from new_structure import create_app
        from new_structure.extensions import db
        from new_structure.models.academic import Subject
        
        print("🎨 Adding Pre-Primary Subjects")
        print("=" * 50)
        
        # Create Flask app context
        app = create_app('development')
        
        with app.app_context():
            # Check current database connection
            print(f"📍 Database URI: {app.config['SQLALCHEMY_DATABASE_URI']}")
            
            # Check existing subjects
            existing_subjects = Subject.query.filter_by(education_level='pre_primary').all()
            existing_subject_names = [subject.name for subject in existing_subjects]
            
            print(f"📋 Current pre-primary subjects in database: {len(existing_subjects)}")
            for subject in existing_subjects:
                print(f"   - {subject.name}")
            
            # Define Pre-Primary subjects based on Kenya CBC curriculum
            pre_primary_subjects = [
                # Core learning areas for Pre-Primary
                'Language Activities',
                'Mathematical Activities', 
                'Environmental Activities',
                'Psychomotor and Creative Activities',
                'Religious Education Activities',
                
                # Additional subjects commonly used
                'English',
                'Kiswahili',
                'Mathematics',
                'Science Activities',
                'Creative Arts',
                'Physical Education',
                'Music and Movement',
                'Drawing and Coloring'
            ]
            
            added_subjects = []
            for subject_name in pre_primary_subjects:
                if subject_name not in existing_subject_names:
                    new_subject = Subject(
                        name=subject_name, 
                        education_level='pre_primary',
                        is_composite=False  # Pre-primary subjects are typically not composite
                    )
                    db.session.add(new_subject)
                    added_subjects.append(subject_name)
                    print(f"✅ Added subject: {subject_name}")
                else:
                    print(f"⚠️  Subject {subject_name} already exists")
            
            # Commit the changes
            if added_subjects:
                db.session.commit()
                print(f"\n🎉 Successfully added {len(added_subjects)} pre-primary subjects!")
            else:
                print(f"\n✅ All pre-primary subjects already exist in database")
            
            # Verify the addition
            print(f"\n🔍 Verification - Current pre-primary subjects after migration:")
            all_pre_primary_subjects = Subject.query.filter_by(education_level='pre_primary').order_by(Subject.name).all()
            for subject in all_pre_primary_subjects:
                print(f"   - {subject.name}")
            
            print(f"\n📊 Subject Distribution by Education Level:")
            levels = ['pre_primary', 'lower_primary', 'upper_primary', 'junior_secondary']
            for level in levels:
                count = Subject.query.filter_by(education_level=level).count()
                print(f"   {level}: {count} subjects")
            
            return True
            
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pre_primary_subjects():
    """Test that pre-primary subjects were added correctly."""
    
    try:
        from new_structure import create_app
        from new_structure.models.academic import Subject
        
        app = create_app('development')
        
        with app.app_context():
            print("\n🧪 Testing Pre-Primary Subjects...")
            
            # Test database subjects
            pre_primary_subjects = Subject.query.filter_by(education_level='pre_primary').all()
            
            if len(pre_primary_subjects) > 0:
                print(f"✅ Found {len(pre_primary_subjects)} pre-primary subjects")
                
                # Check for key subjects
                subject_names = [s.name for s in pre_primary_subjects]
                key_subjects = ['Language Activities', 'Mathematical Activities', 'Environmental Activities']
                
                missing_key_subjects = [s for s in key_subjects if s not in subject_names]
                if not missing_key_subjects:
                    print(f"✅ All key pre-primary subjects found")
                else:
                    print(f"⚠️  Missing key subjects: {missing_key_subjects}")
                
            else:
                print(f"❌ No pre-primary subjects found in database")
                return False
            
            print(f"✅ All tests passed!")
            return True
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Pre-Primary Subjects Migration")
    print("=" * 40)
    
    # Run the migration
    success = add_pre_primary_subjects()
    
    if success:
        print("\n🔍 Running tests...")
        test_success = test_pre_primary_subjects()
        
        if test_success:
            print("\n" + "=" * 40)
            print("✅ PRE-PRIMARY SUBJECTS MIGRATION COMPLETED!")
            print("🎨 Added subjects for Pre-Primary education:")
            print("   - Language Activities")
            print("   - Mathematical Activities") 
            print("   - Environmental Activities")
            print("   - Psychomotor and Creative Activities")
            print("   - Religious Education Activities")
            print("   - And more...")
            print("📚 Educational structure now complete:")
            print("   - Pre-Primary: PP1, PP2 with appropriate subjects")
            print("   - Lower Primary: Grade 1, 2, 3")
            print("   - Upper Primary: Grade 4, 5, 6")
            print("   - Junior Secondary: Grade 7, 8, 9")
            print("=" * 40)
        else:
            print("\n❌ TESTS FAILED!")
    else:
        print("\n❌ MIGRATION FAILED!")
        sys.exit(1)
