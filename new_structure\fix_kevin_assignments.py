#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check and fix <PERSON>'s teacher assignments.
"""
import sys
import os

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models.user import Teacher
from models.assignment import TeacherSubjectAssignment
from models.academic import Subject, Grade, Stream
from extensions import db

def fix_kevin_assignments():
    """Check and fix <PERSON>'s assignments."""
    app = create_app()
    
    with app.app_context():
        print("=== FIXING KEVIN'S ASSIGNMENTS ===")
        
        # Find Kevin
        kevin = Teacher.query.filter_by(username='kevin').first()
        if not kevin:
            print("❌ Kevin not found in database")
            return
        
        print(f"✅ Found Kevin: ID={kevin.id}, Role={kevin.role}")
        print(f"✅ <PERSON>'s name: {kevin.name}")
        
        # Check existing assignments
        existing_assignments = TeacherSubjectAssignment.query.filter_by(teacher_id=kevin.id).all()
        print(f"\n📚 Existing assignments: {len(existing_assignments)}")
        
        if existing_assignments:
            print("Current assignments:")
            for assignment in existing_assignments:
                subject_name = assignment.subject.name if assignment.subject else "Unknown"
                grade_name = assignment.grade.name if assignment.grade else "Unknown"
                stream_name = assignment.stream.name if assignment.stream else "All"
                print(f"  - {subject_name} | Grade {grade_name} | Stream {stream_name}")
        else:
            print("❌ No assignments found. Adding sample assignments...")
            
            # Get some subjects and grades to assign
            math_subject = Subject.query.filter_by(name='MATHEMATICS').first()
            english_subject = Subject.query.filter_by(name='ENGLISH').first()
            grade_7 = Grade.query.filter_by(name='Grade 7').first()
            grade_8 = Grade.query.filter_by(name='Grade 8').first()
            
            if not math_subject:
                math_subject = Subject.query.first()
            if not english_subject:
                english_subject = Subject.query.offset(1).first()
            if not grade_7:
                grade_7 = Grade.query.first()
            if not grade_8:
                grade_8 = Grade.query.offset(1).first()
            
            # Get streams for these grades
            stream_a_7 = Stream.query.filter_by(grade_id=grade_7.id, name='A').first() if grade_7 else None
            stream_a_8 = Stream.query.filter_by(grade_id=grade_8.id, name='A').first() if grade_8 else None
            
            assignments_to_add = []
            
            # Add Mathematics assignments
            if math_subject and grade_7 and stream_a_7:
                assignments_to_add.append({
                    'teacher_id': kevin.id,
                    'subject_id': math_subject.id,
                    'grade_id': grade_7.id,
                    'stream_id': stream_a_7.id,
                    'is_class_teacher': False
                })
            
            if math_subject and grade_8 and stream_a_8:
                assignments_to_add.append({
                    'teacher_id': kevin.id,
                    'subject_id': math_subject.id,
                    'grade_id': grade_8.id,
                    'stream_id': stream_a_8.id,
                    'is_class_teacher': False
                })
            
            # Add English assignments
            if english_subject and grade_7 and stream_a_7:
                assignments_to_add.append({
                    'teacher_id': kevin.id,
                    'subject_id': english_subject.id,
                    'grade_id': grade_7.id,
                    'stream_id': stream_a_7.id,
                    'is_class_teacher': False
                })
            
            # Create the assignments
            for assignment_data in assignments_to_add:
                try:
                    assignment = TeacherSubjectAssignment(**assignment_data)
                    db.session.add(assignment)
                    print(f"✅ Added assignment: {assignment}")
                except Exception as e:
                    print(f"❌ Error adding assignment: {e}")
            
            try:
                db.session.commit()
                print(f"✅ Successfully added {len(assignments_to_add)} assignments for Kevin")
            except Exception as e:
                db.session.rollback()
                print(f"❌ Error committing assignments: {e}")
        
        # Verify assignments after fix
        final_assignments = TeacherSubjectAssignment.query.filter_by(teacher_id=kevin.id).all()
        print(f"\n📊 Final assignment count: {len(final_assignments)}")
        
        for assignment in final_assignments:
            subject_name = assignment.subject.name if assignment.subject else "Unknown"
            grade_name = assignment.grade.name if assignment.grade else "Unknown"
            stream_name = assignment.stream.name if assignment.stream else "All"
            print(f"  ✅ {subject_name} | Grade {grade_name} | Stream {stream_name}")

if __name__ == "__main__":
    fix_kevin_assignments()
