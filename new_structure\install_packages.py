#!/usr/bin/env python3
"""
Hillview School Management System - Package Installer
This script installs all required packages for the application.
"""
import subprocess
import sys
import os

def run_pip_install(packages):
    """Install packages using pip."""
    venv_pip = os.path.join('venv', 'Scripts', 'pip.exe')
    
    if os.path.exists(venv_pip):
        pip_cmd = venv_pip
        print("✅ Using virtual environment pip")
    else:
        pip_cmd = 'pip'
        print("⚠️ Using system pip (virtual environment not found)")
    
    for package in packages:
        print(f"📦 Installing {package}...")
        try:
            result = subprocess.run([pip_cmd, 'install', package], 
                                  capture_output=True, text=True, check=True)
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            print(f"Error output: {e.stderr}")

def main():
    print("🚀 Hillview School Management System - Package Installer")
    print("=" * 60)
    
    # Core Flask packages
    core_packages = [
        'Flask==2.3.3',
        'Flask-SQLAlchemy==3.0.5',
        'Flask-WTF==1.1.1',
        'WTForms==3.0.1',
        'Werkzeug==2.3.7'
    ]
    
    # Database packages
    database_packages = [
        'PyMySQL>=1.0.2',
        'cryptography>=3.4.8',
        'mysql-connector-python>=8.0.33'
    ]
    
    # Report generation packages
    report_packages = [
        'reportlab==4.0.4',
        'Pillow==10.0.0',
        'openpyxl==3.1.2',
        'pdfkit',
        'pandas',
        'python-docx'
    ]
    
    # Additional packages
    additional_packages = [
        'python-decouple==3.8',
        'PyYAML==6.0.1',
        'Flask-Limiter==3.5.0',
        'structlog==23.2.0',
        'Flask-Session==0.5.0',
        'requests==2.31.0',
        'bleach==6.0.0'
    ]
    
    all_packages = core_packages + database_packages + report_packages + additional_packages
    
    print("📋 Installing packages:")
    for package in all_packages:
        print(f"   - {package}")
    print()
    
    run_pip_install(all_packages)
    
    print("\n" + "=" * 60)
    print("✅ Package installation complete!")
    print("🚀 You can now run: python run.py")

if __name__ == '__main__':
    main()
