<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, maximum-scale=5.0">
    <meta name="description" content="Mobile Subject Teacher Demo - Hillview School Management System">
    <title>Mobile Subject Teacher Demo - Hillview School</title>

    <!-- Mobile Responsive Dashboard Styles -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile_responsive_dashboard.css') }}">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* Demo-specific styles */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .demo-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem;
            text-align: center;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .demo-header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .demo-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }
        
        .mobile-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- Mobile Indicator -->
    <div class="mobile-indicator">
        <i class="fas fa-mobile-alt"></i>
        <span id="screen-size"></span>
    </div>

    <!-- Demo Header -->
    <div class="demo-header">
        <h1><i class="fas fa-chalkboard-teacher"></i> Subject Teacher Mobile Demo</h1>
        <p>Responsive design optimized for smartphones and tablets</p>
    </div>

    <!-- Dashboard Container -->
    <div class="dashboard-container">
        
        <!-- Navigation Links -->
        <div class="nav-links">
            <a href="#" class="btn btn-secondary"><i class="fas fa-home"></i> Dashboard</a>
            <a href="#" class="btn btn-secondary"><i class="fas fa-book"></i> Subjects</a>
            <a href="#" class="btn btn-secondary"><i class="fas fa-chart-bar"></i> Reports</a>
            <a href="#" class="btn btn-secondary"><i class="fas fa-sign-out-alt"></i> Logout</a>
        </div>

        <!-- Marks Upload Form -->
        <div class="form-section">
            <h3><i class="fas fa-upload"></i> Upload Subject Marks</h3>
            
            <form>
                <div class="form-group">
                    <label for="education_level">Education Level</label>
                    <select id="education_level" class="form-control" required>
                        <option value="">Select Education Level</option>
                        <option value="pre_primary">🌟 Pre-Primary (PP1, PP2)</option>
                        <option value="lower_primary">🎓 Lower Primary (Grades 1-3)</option>
                        <option value="upper_primary">📚 Upper Primary (Grades 4-6)</option>
                        <option value="junior_secondary">🏫 Junior Secondary (Grades 7-9)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="subject">Subject</label>
                    <select id="subject" class="form-control" required>
                        <option value="">Select Subject</option>
                        <option value="mathematics">Mathematics</option>
                        <option value="english">English (Composite)</option>
                        <option value="kiswahili">Kiswahili (Composite)</option>
                        <option value="science">Integrated Science</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="grade">Grade</label>
                    <select id="grade" class="form-control" required>
                        <option value="">Select Grade</option>
                        <option value="grade_7">Grade 7</option>
                        <option value="grade_8">Grade 8</option>
                        <option value="grade_9">Grade 9</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="stream">Stream</label>
                    <select id="stream" class="form-control" required>
                        <option value="">Select Stream</option>
                        <option value="stream_a">Stream A</option>
                        <option value="stream_b">Stream B</option>
                        <option value="stream_c">Stream C</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="term">Term</label>
                    <select id="term" class="form-control" required>
                        <option value="">Select Term</option>
                        <option value="term_1">Term 1</option>
                        <option value="term_2">Term 2</option>
                        <option value="term_3">Term 3</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="assessment">Assessment Type</label>
                    <select id="assessment" class="form-control" required>
                        <option value="">Select Assessment</option>
                        <option value="cat">Continuous Assessment Test (CAT)</option>
                        <option value="exam">End of Term Exam</option>
                    </select>
                </div>

                <div class="button-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Upload Marks
                    </button>
                    <button type="button" class="btn btn-secondary">
                        <i class="fas fa-download"></i> Download Template
                    </button>
                </div>
            </form>
        </div>

        <!-- Student Marks Table -->
        <div class="form-section">
            <h3><i class="fas fa-table"></i> Student Marks</h3>
            
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Student Name</th>
                            <th>Admission No.</th>
                            <th>Marks</th>
                            <th>Grade</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>John Doe</td>
                            <td>ADM001</td>
                            <td><input type="number" class="form-control" value="85" min="0" max="100"></td>
                            <td>A</td>
                            <td><button class="btn btn-success btn-sm"><i class="fas fa-save"></i></button></td>
                        </tr>
                        <tr>
                            <td>Jane Smith</td>
                            <td>ADM002</td>
                            <td><input type="number" class="form-control" value="78" min="0" max="100"></td>
                            <td>B+</td>
                            <td><button class="btn btn-success btn-sm"><i class="fas fa-save"></i></button></td>
                        </tr>
                        <tr>
                            <td>Michael Johnson</td>
                            <td>ADM003</td>
                            <td><input type="number" class="form-control" value="92" min="0" max="100"></td>
                            <td>A</td>
                            <td><button class="btn btn-success btn-sm"><i class="fas fa-save"></i></button></td>
                        </tr>
                        <tr>
                            <td>Sarah Wilson</td>
                            <td>ADM004</td>
                            <td><input type="number" class="form-control" value="67" min="0" max="100"></td>
                            <td>B</td>
                            <td><button class="btn btn-success btn-sm"><i class="fas fa-save"></i></button></td>
                        </tr>
                        <tr>
                            <td>David Brown</td>
                            <td>ADM005</td>
                            <td><input type="number" class="form-control" value="73" min="0" max="100"></td>
                            <td>B+</td>
                            <td><button class="btn btn-success btn-sm"><i class="fas fa-save"></i></button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="button-group">
                <button type="button" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save All Marks
                </button>
                <button type="button" class="btn btn-success">
                    <i class="fas fa-check"></i> Submit for Review
                </button>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="form-section">
            <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
            
            <div class="button-group">
                <button class="btn btn-info">
                    <i class="fas fa-chart-line"></i> View Analytics
                </button>
                <button class="btn btn-warning">
                    <i class="fas fa-file-pdf"></i> Generate Report
                </button>
                <button class="btn btn-secondary">
                    <i class="fas fa-history"></i> View History
                </button>
            </div>
        </div>

        <!-- Mobile Features Demo -->
        <div class="form-section">
            <h3><i class="fas fa-mobile-alt"></i> Mobile Features</h3>
            
            <div class="alert alert-info">
                <strong>Mobile Optimizations:</strong>
                <ul style="margin: 0.5rem 0 0 1rem; padding: 0;">
                    <li>Touch-friendly buttons (44px minimum)</li>
                    <li>Responsive tables with scroll indicators</li>
                    <li>Optimized form layouts</li>
                    <li>Landscape orientation support</li>
                    <li>Dark mode compatibility</li>
                    <li>Smooth animations and transitions</li>
                </ul>
            </div>
            
            <button class="btn btn-primary" onclick="MobileDashboard.showAlert('Mobile alert demo!', 'success')">
                <i class="fas fa-bell"></i> Test Mobile Alert
            </button>
        </div>

    </div>

    <!-- Mobile Responsive Dashboard JavaScript -->
    <script src="{{ url_for('static', filename='js/mobile_responsive_dashboard.js') }}"></script>
    
    <script>
        // Update screen size indicator
        function updateScreenSize() {
            const indicator = document.getElementById('screen-size');
            const width = window.innerWidth;
            const height = window.innerHeight;
            
            let deviceType = 'Desktop';
            if (width <= 480) deviceType = 'Mobile (Small)';
            else if (width <= 768) deviceType = 'Mobile';
            else if (width <= 1024) deviceType = 'Tablet';
            
            indicator.textContent = `${deviceType} ${width}×${height}`;
        }
        
        // Update on load and resize
        updateScreenSize();
        window.addEventListener('resize', updateScreenSize);
        
        // Demo form interactions
        document.getElementById('education_level').addEventListener('change', function() {
            if (this.value) {
                MobileDashboard.showAlert(`Selected: ${this.options[this.selectedIndex].text}`, 'info');
            }
        });
    </script>
</body>
</html>
