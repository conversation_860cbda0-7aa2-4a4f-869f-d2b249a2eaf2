/* Enhanced Professional Styling for All Manage Pages */

/* Enhanced text visibility and contrast */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Container and Layout */
.manage-container {
  max-width: 95% !important;
  width: 95% !important;
  margin: 80px auto 60px !important;
  padding: var(--spacing-xl) !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Enhanced Page Header */
.page-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, #2c5530 100%);
  color: white !important;
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(31, 125, 83, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: var(--spacing-xl);
  position: relative;
  overflow: hidden;
}

/* Ensure all header text is visible */
.page-header h1,
.page-header h2,
.page-header h3,
.page-header p,
.page-header span,
.page-header a {
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Navigation links in header */
.nav-links a {
  color: white !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  text-decoration: none !important;
}

.nav-links a:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

.page-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.page-header h1 {
  color: white;
  margin-bottom: var(--spacing-sm);
  font-size: 2.8rem;
  font-weight: 700;
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  gap: 15px;
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.2rem;
  margin-bottom: var(--spacing-md);
  position: relative;
  z-index: 1;
  font-weight: 400;
}

/* Enhanced Navigation Links */
.nav-links {
  position: relative;
  z-index: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 1.5rem;
}

.nav-links a {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.95rem;
}

.nav-links a:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  text-decoration: none;
}

/* Enhanced Form Cards */
.form-card {
  background: white;
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(31, 125, 83, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 2rem;
}

.form-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), #28a745);
}

.form-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.form-card h2 {
  color: var(--primary-color);
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Enhanced Form Controls */
.form-group {
  margin-bottom: 1.8rem;
  position: relative;
}

.form-group label {
  display: block;
  margin-bottom: 0.8rem;
  color: var(--text-dark);
  font-weight: 600;
  font-size: 1.05rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-control {
  width: 100%;
  padding: 1.2rem 1.5rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 1rem;
  color: var(--text-dark);
  background: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(31, 125, 83, 0.1);
  transform: translateY(-2px);
}

.form-control:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Enhanced Buttons */
.btn,
.manage-btn {
  background: linear-gradient(135deg, var(--primary-color) 0%, #2c5530 100%);
  color: white;
  border: none;
  padding: 1.2rem 2.5rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1.05rem;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(31, 125, 83, 0.3);
}

.btn::before,
.manage-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn:hover,
.manage-btn:hover {
  background: linear-gradient(135deg, #2c5530 0%, var(--primary-color) 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(31, 125, 83, 0.4);
  text-decoration: none;
}

.btn:hover::before,
.manage-btn:hover::before {
  left: 100%;
}

/* Enhanced Tables */
.table-responsive {
  overflow-x: auto;
  margin: var(--spacing-lg) 0;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  background: white;
  border: 1px solid #dee2e6;
}

table {
  width: 100%;
  min-width: 800px;
  border-collapse: collapse;
  border-radius: 16px;
  overflow: hidden;
}

th,
td {
  padding: 1.5rem 1.2rem;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

th {
  background: linear-gradient(135deg, var(--primary-color) 0%, #2c5530 100%);
  color: white;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

th::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: #28a745;
}

tr:nth-child(even) {
  background: rgba(31, 125, 83, 0.03);
}

tr:hover {
  background: rgba(31, 125, 83, 0.08);
  transform: scale(1.01);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Action Buttons */
.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.edit-btn,
.delete-btn {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  display: inline-block;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.edit-btn {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  color: var(--text-dark);
}

.edit-btn:hover {
  background: linear-gradient(135deg, #e0a800 0%, #ffc107 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
  text-decoration: none;
}

.delete-btn {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
}

.delete-btn:hover {
  background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

/* Enhanced Messages */
.message {
  padding: 1.5rem 2rem;
  margin-bottom: 2rem;
  border-radius: 12px;
  font-weight: 500;
  border-left: 4px solid;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 1.05rem;
}

.message-success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
  border-left-color: #28a745;
}

.message-success::before {
  content: "✅";
  font-size: 1.5rem;
}

.message-error {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  border-left-color: #dc3545;
}

.message-error::before {
  content: "❌";
  font-size: 1.5rem;
}

/* Enhanced Filter Section */
#student-filter,
.filter-section {
  margin-bottom: 2rem;
  padding: 1.8rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #dee2e6;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

#student-filter label,
.filter-section label {
  font-weight: 600;
  color: var(--text-dark);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.05rem;
}

#student-filter input,
.filter-section input {
  padding: 1rem 1.5rem;
  border: 2px solid #dee2e6;
  border-radius: 10px;
  width: 350px;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

#student-filter input:focus,
.filter-section input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(31, 125, 83, 0.1);
  transform: translateY(-2px);
}

/* Enhanced Filter Buttons */
.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.filter-btn {
  padding: 0.8rem 1.5rem;
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 25px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-dark);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(31, 125, 83, 0.3);
}

.filter-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(31, 125, 83, 0.3);
}

/* Enhanced Grid Layouts */
.forms-grid {
  display: grid;
  grid-template-columns: 1.3fr 0.7fr;
  gap: 2.5rem;
  margin-bottom: 3rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .forms-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .manage-container {
    max-width: 98% !important;
    width: 98% !important;
    padding: var(--spacing-lg) !important;
  }
}

@media (max-width: 768px) {
  .manage-container {
    max-width: 98% !important;
    width: 98% !important;
    padding: var(--spacing-md) !important;
  }

  .page-header {
    padding: 2rem;
  }

  .page-header h1 {
    font-size: 2.2rem;
  }

  .nav-links {
    flex-direction: column;
    gap: 10px;
  }

  .form-card {
    padding: 1.5rem;
  }

  #student-filter,
  .filter-section {
    flex-direction: column;
    align-items: stretch;
  }

  #student-filter input,
  .filter-section input {
    width: 100%;
  }

  .filter-buttons {
    justify-content: center;
  }

  .action-buttons {
    flex-direction: column;
  }

  th,
  td {
    padding: 1rem 0.8rem;
    font-size: 0.9rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .manage-container {
    padding: 1rem !important;
  }

  .page-header {
    padding: 1.5rem;
  }

  .page-header h1 {
    font-size: 1.8rem;
  }

  .form-card {
    padding: 1rem;
  }

  .manage-btn {
    width: 100%;
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--primary-color);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-card {
  animation: fadeInUp 0.6s ease-out;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #2c5530;
}

/* Enhanced Text Visibility for All Elements */
.manage-container h1,
.manage-container h2,
.manage-container h3,
.manage-container h4,
.manage-container h5,
.manage-container h6 {
  color: var(--primary-color) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.manage-container p,
.manage-container span,
.manage-container div,
.manage-container label,
.manage-container td,
.manage-container th {
  color: var(--text-primary) !important;
}

.manage-container .page-subtitle {
  color: var(--text-secondary) !important;
}

/* Form elements text visibility */
.form-card h3,
.form-card h4 {
  color: var(--primary-color) !important;
}

.form-card label {
  color: var(--text-primary) !important;
  font-weight: 500;
}

/* Table text visibility */
table th {
  color: white !important;
}

table td {
  color: var(--text-primary) !important;
}

/* Success and Error Messages */
.success-message {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724 !important;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  border: 1px solid #c3e6cb;
  margin: 1rem 0;
  box-shadow: 0 4px 12px rgba(21, 87, 36, 0.1);
  font-weight: 500;
}

.error-message {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24 !important;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  border: 1px solid #f5c6cb;
  margin: 1rem 0;
  box-shadow: 0 4px 12px rgba(114, 28, 36, 0.1);
  font-weight: 500;
}

/* Enhanced Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus States */
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}
