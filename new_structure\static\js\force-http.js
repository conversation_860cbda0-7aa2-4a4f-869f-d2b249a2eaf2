/**
 * Force HTTP Redirect <PERSON>ript
 * Automatically redirects HTTPS attempts to HTTP in development
 */

(function() {
    'use strict';
    
    // Check if we're on HTTPS and should be on HTTP
    function forceHTTP() {
        if (window.location.protocol === 'https:') {
            console.log('🔄 HTTPS detected in development - redirecting to HTTP');
            
            // Build HTTP URL
            const httpUrl = window.location.href.replace('https://', 'http://');
            
            // Show user-friendly message
            const message = document.createElement('div');
            message.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                background: #ff6b6b;
                color: white;
                padding: 15px;
                text-align: center;
                z-index: 10000;
                font-family: Arial, sans-serif;
                font-size: 14px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            `;
            message.innerHTML = `
                🔄 Redirecting from HTTPS to HTTP... 
                <span style="font-family: monospace; background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 3px;">
                    ${httpUrl}
                </span>
            `;
            
            document.body.insertBefore(message, document.body.firstChild);
            
            // Redirect after short delay
            setTimeout(() => {
                window.location.replace(httpUrl);
            }, 1500);
            
            return true;
        }
        return false;
    }
    
    // Check for mixed content issues
    function checkMixedContent() {
        if (window.location.protocol === 'https:') {
            // Warn about potential mixed content issues
            console.warn('⚠️ HTTPS detected - this may cause mixed content issues with HTTP resources');
        }
    }
    
    // Update all HTTPS links to HTTP
    function updateLinksToHTTP() {
        const links = document.querySelectorAll('a[href^="https://"]');
        links.forEach(link => {
            const href = link.getAttribute('href');
            if (href.includes('localhost') || href.includes('127.0.0.1') || href.includes('192.168.')) {
                link.setAttribute('href', href.replace('https://', 'http://'));
                console.log('🔗 Updated link to HTTP:', href);
            }
        });
    }
    
    // Update forms to use HTTP
    function updateFormsToHTTP() {
        const forms = document.querySelectorAll('form[action^="https://"]');
        forms.forEach(form => {
            const action = form.getAttribute('action');
            if (action && (action.includes('localhost') || action.includes('127.0.0.1') || action.includes('192.168.'))) {
                form.setAttribute('action', action.replace('https://', 'http://'));
                console.log('📝 Updated form action to HTTP:', action);
            }
        });
    }
    
    // Main execution
    function init() {
        // First, check if we need to redirect
        if (forceHTTP()) {
            return; // Exit if redirecting
        }
        
        // Check for mixed content issues
        checkMixedContent();
        
        // Update existing links and forms
        updateLinksToHTTP();
        updateFormsToHTTP();
        
        // Set up observer for dynamically added content
        if (window.MutationObserver) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        updateLinksToHTTP();
                        updateFormsToHTTP();
                    }
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }
    
    // Run when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // Also run immediately in case we're already loaded
    init();
    
    // Expose function globally for manual use
    window.forceHTTP = forceHTTP;
    
})();

// Additional utility functions
window.hillviewUtils = window.hillviewUtils || {};
window.hillviewUtils.forceHTTP = function() {
    if (window.location.protocol === 'https:') {
        const httpUrl = window.location.href.replace('https://', 'http://');
        window.location.replace(httpUrl);
        return true;
    }
    return false;
};

// Console helper
console.log('🔧 Hillview HTTP Force Script Loaded');
if (window.location.protocol === 'https:') {
    console.warn('⚠️ HTTPS detected - use hillviewUtils.forceHTTP() to redirect to HTTP');
}
