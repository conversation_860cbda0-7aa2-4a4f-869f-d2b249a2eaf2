/* ===================================================================
   HILLVIEW SCHOOL MANAGEMENT SYSTEM - LOGIN BUTTON FIX
   Ensures login button is always visible and functional
   ================================================================== */

/* ===== LOGIN BUTTON VISIBILITY FIX ===== */
.login-button,
#loginBtn {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 10 !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 12px !important;
  padding: 16px 24px !important;
  font-family: "Inter", system-ui, -apple-system, sans-serif !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  width: 100% !important;
  transition: all 0.3s ease !important;
  overflow: hidden !important;
  margin-top: 16px !important;
  text-align: center !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  min-height: 54px !important;
}

.login-button:hover,
#loginBtn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -4px rgba(0, 0, 0, 0.1) !important;
}

.login-button:active,
#loginBtn:active {
  transform: translateY(0) !important;
}

.login-button:disabled,
#loginBtn:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

/* ===== LOGIN BUTTON ICON FIX ===== */
.login-button i,
#loginBtn i {
  margin-right: 8px !important;
  color: white !important;
  opacity: 1 !important;
  display: inline-block !important;
  font-size: 1rem !important;
  font-family: "Font Awesome 6 Free" !important;
  font-weight: 900 !important;
}

/* ===== LOGIN FORM ENHANCEMENTS ===== */
.modern-form {
  display: grid !important;
  gap: 24px !important;
  margin: 0 !important;
  padding: 0 !important;
}

.form-group {
  position: relative !important;
  display: block !important;
  margin-bottom: 16px !important;
}

.form-input {
  width: 100% !important;
  padding: 16px 20px !important;
  border: 2px solid #e2e8f0 !important;
  border-radius: 12px !important;
  font-size: 1rem !important;
  font-family: "Inter", system-ui, -apple-system, sans-serif !important;
  transition: all 0.2s ease !important;
  background: white !important;
  color: #1f2937 !important;
  box-sizing: border-box !important;
}

.form-input:focus {
  outline: none !important;
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  transform: translateY(-1px) !important;
}

.form-input.with-icon {
  padding-left: 48px !important;
}

.input-icon {
  position: absolute !important;
  left: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  color: #9ca3af !important;
  font-size: 1.1rem !important;
  transition: color 0.2s ease !important;
  z-index: 5 !important;
}

.form-input:focus + .input-icon {
  color: #667eea !important;
}

/* ===== LOADING STATE FIX ===== */
.login-button.loading,
#loginBtn.loading {
  opacity: 0.8 !important;
  cursor: not-allowed !important;
}

.login-button.loading i,
#loginBtn.loading i {
  animation: spin 1s linear infinite !important;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* ===== ERROR MESSAGE STYLING ===== */
.error-message {
  font-size: 0.875rem !important;
  color: #ef4444 !important;
  margin-top: 8px !important;
  margin-bottom: 16px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 12px 16px !important;
  background: rgba(239, 68, 68, 0.1) !important;
  border: 1px solid rgba(239, 68, 68, 0.2) !important;
  border-radius: 8px !important;
}

.error-message i {
  color: #ef4444 !important;
  font-size: 1rem !important;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {
  .login-button,
  #loginBtn {
    padding: 18px 24px !important;
    font-size: 1.1rem !important;
    min-height: 58px !important;
  }

  .form-input {
    padding: 18px 20px !important;
    font-size: 1.1rem !important;
  }

  .form-input.with-icon {
    padding-left: 52px !important;
  }

  .input-icon {
    left: 18px !important;
    font-size: 1.2rem !important;
  }
}

/* ===== FALLBACK STYLES ===== */
button[type="submit"] {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* ===== ICON FALLBACK ===== */
.fa-sign-in-alt::before {
  content: "\f2f6" !important;
  font-family: "Font Awesome 6 Free" !important;
  font-weight: 900 !important;
}

/* Emergency fallback if Font Awesome fails */
.login-button i:empty::before,
#loginBtn i:empty::before {
  content: "→" !important;
  font-family: Arial, sans-serif !important;
  font-weight: bold !important;
}

/* ===== CONTAINER FIXES ===== */
.login-container {
  position: relative !important;
  z-index: 2 !important;
  width: 100% !important;
  max-width: 450px !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: 24px !important;
  padding: 32px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  margin: 32px auto !important;
  min-height: auto !important;
  height: auto !important;
}

/* ===== DARK MODE SUPPORT ===== */
[data-theme="dark"] .login-button,
[data-theme="dark"] #loginBtn {
  background: linear-gradient(135deg, #4338ca 0%, #7c3aed 100%) !important;
  color: white !important;
}

[data-theme="dark"] .login-button i,
[data-theme="dark"] #loginBtn i {
  color: white !important;
}

[data-theme="dark"] .form-input {
  background: #1f2937 !important;
  color: #f9fafb !important;
  border-color: #374151 !important;
}

[data-theme="dark"] .form-input:focus {
  border-color: #6366f1 !important;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1) !important;
}

[data-theme="dark"] .input-icon {
  color: #6b7280 !important;
}

[data-theme="dark"] .form-input:focus + .input-icon {
  color: #6366f1 !important;
}
