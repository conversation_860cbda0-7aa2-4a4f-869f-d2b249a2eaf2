#!/usr/bin/env python3
"""
Migration script to remove duplicate subjects with different cases.
This ensures only one version of each subject exists per education level,
preventing confusion between "English" and "ENGLISH", etc.
"""

import sys
import os
from datetime import datetime
from collections import defaultdict

# Add the parent directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
grandparent_dir = os.path.dirname(parent_dir)
sys.path.insert(0, grandparent_dir)

def remove_duplicate_subjects():
    """Remove duplicate subjects with different cases."""
    
    try:
        from new_structure import create_app
        from new_structure.extensions import db
        from new_structure.models.academic import Subject, Mark
        
        print("🧹 Removing Duplicate Subjects (Case-Insensitive)")
        print("=" * 60)
        
        # Create Flask app context
        app = create_app('development')
        
        with app.app_context():
            # Check current database connection
            print(f"📍 Database URI: {app.config['SQLALCHEMY_DATABASE_URI']}")
            
            # Get all subjects grouped by education level
            all_subjects = Subject.query.all()
            print(f"📋 Total subjects before cleanup: {len(all_subjects)}")
            
            # Group subjects by education level and normalized name
            subjects_by_level = defaultdict(lambda: defaultdict(list))
            
            for subject in all_subjects:
                normalized_name = subject.name.lower().strip()
                subjects_by_level[subject.education_level][normalized_name].append(subject)
            
            total_removed = 0
            total_kept = 0
            
            for education_level, subjects_dict in subjects_by_level.items():
                print(f"\n🎯 Processing {education_level.replace('_', ' ').title()}...")
                
                level_removed = 0
                level_kept = 0
                
                for normalized_name, subject_list in subjects_dict.items():
                    if len(subject_list) > 1:
                        print(f"\n📝 Found {len(subject_list)} duplicates for '{normalized_name}':")
                        
                        # Show all duplicates
                        for i, subject in enumerate(subject_list):
                            composite_text = " (Composite)" if subject.is_composite else ""
                            print(f"   {i+1}. '{subject.name}'{composite_text} (ID: {subject.id})")
                        
                        # Choose the best version to keep
                        subject_to_keep = choose_best_subject(subject_list)
                        subjects_to_remove = [s for s in subject_list if s.id != subject_to_keep.id]
                        
                        composite_text = " (Composite)" if subject_to_keep.is_composite else ""
                        print(f"   ✅ Keeping: '{subject_to_keep.name}'{composite_text}")
                        
                        # Remove duplicates
                        for subject in subjects_to_remove:
                            # Check if subject has marks
                            marks_count = Mark.query.filter_by(subject_id=subject.id).count()
                            
                            if marks_count > 0:
                                print(f"   ⚠️  Cannot remove '{subject.name}' - has {marks_count} marks")
                                # Instead, we could merge the marks to the kept subject
                                # For now, we'll skip deletion
                                continue
                            
                            print(f"   🗑️  Removing: '{subject.name}'")
                            db.session.delete(subject)
                            level_removed += 1
                            total_removed += 1
                        
                        level_kept += 1
                        total_kept += 1
                    else:
                        # Only one subject with this name
                        level_kept += 1
                        total_kept += 1
                
                print(f"📊 {education_level}: Kept {level_kept}, Removed {level_removed}")
            
            # Commit changes
            if total_removed > 0:
                db.session.commit()
                print(f"\n🎉 Successfully removed {total_removed} duplicate subjects!")
                print(f"✅ Kept {total_kept} unique subjects")
            else:
                print(f"\n✅ No duplicate subjects found to remove")
            
            return True
            
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def choose_best_subject(subject_list):
    """Choose the best subject to keep from duplicates."""
    
    # Priority rules:
    # 1. Prefer proper case (Title Case) over ALL CAPS
    # 2. Prefer composite subjects over non-composite
    # 3. Prefer subjects with marks
    # 4. Prefer the one created first (lower ID)
    
    from new_structure.models.academic import Mark
    
    scored_subjects = []
    
    for subject in subject_list:
        score = 0
        
        # Rule 1: Prefer proper case
        if subject.name.istitle():  # "English"
            score += 10
        elif subject.name.islower():  # "english"
            score += 5
        # ALL CAPS gets 0 points
        
        # Rule 2: Prefer composite subjects
        if subject.is_composite:
            score += 3
        
        # Rule 3: Prefer subjects with marks
        marks_count = Mark.query.filter_by(subject_id=subject.id).count()
        if marks_count > 0:
            score += 20  # High priority for subjects with data
        
        # Rule 4: Prefer older subjects (lower ID = created first)
        score += (1000 - subject.id) * 0.001  # Small bonus for older subjects
        
        scored_subjects.append((score, subject))
    
    # Sort by score (highest first) and return the best subject
    scored_subjects.sort(key=lambda x: x[0], reverse=True)
    return scored_subjects[0][1]

def verify_cleanup():
    """Verify that duplicate cleanup was successful."""
    
    try:
        from new_structure import create_app
        from new_structure.models.academic import Subject
        from collections import defaultdict
        
        app = create_app('development')
        
        with app.app_context():
            print("\n🔍 Verification - Subject Cleanup Results:")
            print("=" * 60)
            
            # Get all subjects after cleanup
            all_subjects = Subject.query.all()
            print(f"📊 Total subjects after cleanup: {len(all_subjects)}")
            
            # Check for remaining duplicates
            subjects_by_level = defaultdict(lambda: defaultdict(list))
            
            for subject in all_subjects:
                normalized_name = subject.name.lower().strip()
                subjects_by_level[subject.education_level][normalized_name].append(subject)
            
            duplicates_found = False
            
            for education_level, subjects_dict in subjects_by_level.items():
                print(f"\n✅ {education_level.replace('_', ' ').title()}:")
                
                level_subjects = []
                for normalized_name, subject_list in subjects_dict.items():
                    if len(subject_list) > 1:
                        print(f"   ⚠️  Still has duplicates: {[s.name for s in subject_list]}")
                        duplicates_found = True
                    else:
                        subject = subject_list[0]
                        composite_text = " (Composite)" if subject.is_composite else ""
                        level_subjects.append(f"{subject.name}{composite_text}")
                
                # Show clean subjects
                for subject_name in sorted(level_subjects):
                    print(f"   - {subject_name}")
            
            if not duplicates_found:
                print(f"\n🎉 No duplicate subjects found! Cleanup successful!")
                return True
            else:
                print(f"\n⚠️  Some duplicates still exist")
                return False
            
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Duplicate Subjects Cleanup")
    print("=" * 40)
    
    # Run the cleanup
    success = remove_duplicate_subjects()
    
    if success:
        print("\n🔍 Running verification...")
        verification_success = verify_cleanup()
        
        if verification_success:
            print("\n" + "=" * 60)
            print("✅ DUPLICATE SUBJECTS CLEANUP COMPLETED!")
            print("🧹 Benefits:")
            print("   - No more case confusion (English vs ENGLISH)")
            print("   - Clean subject lists in all dropdowns")
            print("   - Consistent subject naming across the system")
            print("   - Preserved subjects with existing marks")
            print("   - Kept the best version of each subject")
            print("\n🎯 Subject Selection Priority:")
            print("   1. Proper Case (English) > lowercase (english) > UPPERCASE")
            print("   2. Composite subjects preferred over non-composite")
            print("   3. Subjects with marks preserved")
            print("   4. Older subjects preferred")
            print("=" * 60)
        else:
            print("\n⚠️ VERIFICATION ISSUES FOUND!")
    else:
        print("\n❌ CLEANUP FAILED!")
        sys.exit(1)
