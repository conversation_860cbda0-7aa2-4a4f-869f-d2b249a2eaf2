<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Footer Position Test</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: Arial, sans-serif;
        background-color: #f5f5f5;
        min-height: 100vh;
      }

      .test-container {
        max-width: 400px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        min-height: 100vh;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      }

      .content-section {
        margin-bottom: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #007bff;
      }

      .content-section h2 {
        color: #007bff;
        margin-top: 0;
      }

      .test-content {
        height: 200px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin: 10px 0;
      }

      .footer-test {
        background: #dc3545;
        color: white;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
        text-align: center;
        font-weight: bold;
      }

      /* Original footer styles (problematic) */
      .original-footer {
        position: fixed;
        bottom: 0;
        width: 100%;
        background: #343a40;
        color: white;
        padding: 10px;
        text-align: center;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
      }

      /* Fixed footer styles */
      .fixed-footer {
        position: relative;
        bottom: auto;
        width: 100%;
        margin-top: 2rem;
        padding: 1rem;
        background: #f8f9fa;
        color: #6c757d;
        border-top: 1px solid #dee2e6;
        text-align: center;
        font-size: 0.875rem;
      }

      .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .status-good {
        background-color: #28a745;
      }
      .status-bad {
        background-color: #dc3545;
      }

      .test-button {
        background-color: #007bff;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
        font-size: 14px;
      }

      .test-button:hover {
        background-color: #0056b3;
      }
    </style>
  </head>
  <body>
    <div class="test-container">
      <div class="content-section">
        <h1>📱 Footer Position Test</h1>
        <p>
          This test page shows the difference between problematic and fixed
          footer positioning on mobile devices.
        </p>
      </div>

      <div class="content-section">
        <h2>Test Content Sections</h2>
        <div class="test-content">Content Section 1</div>
        <div class="test-content">Content Section 2</div>
        <div class="test-content">Content Section 3</div>
      </div>

      <div class="content-section">
        <h2>Footer Status</h2>
        <div class="footer-test">
          <span class="status-indicator status-good"></span>
          Footer is now positioned correctly as relative instead of fixed
        </div>
        <div style="margin-top: 15px">
          <button class="test-button" onclick="toggleFooterStyle()">
            Toggle Footer Style
          </button>
          <button class="test-button" onclick="scrollToBottom()">
            Scroll to Bottom
          </button>
        </div>
      </div>

      <div class="content-section">
        <h2>More Content</h2>
        <p>
          This section ensures there's enough content to test scrolling
          behavior.
        </p>
        <div class="test-content">More Content</div>
        <div class="test-content">Even More Content</div>
      </div>

      <div class="content-section">
        <h2>Final Section</h2>
        <p>This is the last content section before the footer.</p>
        <div class="test-content">Final Content</div>
      </div>
    </div>

    <!-- This is the test footer -->
    <footer class="fixed-footer" id="testFooter">
      <p>© 2025 Hillview School - All Rights Reserved</p>
    </footer>

    <script>
      let isFixedFooter = false;

      function toggleFooterStyle() {
        const footer = document.getElementById("testFooter");
        if (isFixedFooter) {
          footer.className = "fixed-footer";
          isFixedFooter = false;
          console.log("Footer set to relative positioning");
        } else {
          footer.className = "original-footer";
          isFixedFooter = true;
          console.log("Footer set to fixed positioning (problematic)");
        }
      }

      function scrollToBottom() {
        window.scrollTo({
          top: document.body.scrollHeight,
          behavior: "smooth",
        });
      }

      // Test scroll behavior
      window.addEventListener("scroll", function () {
        const scrollPosition = window.scrollY;
        const windowHeight = window.innerHeight;
        const documentHeight = document.body.scrollHeight;

        console.log(
          `Scroll: ${scrollPosition}px, Window: ${windowHeight}px, Document: ${documentHeight}px`
        );
      });

      // Check if footer is overlapping content
      function checkFooterOverlap() {
        const footer = document.getElementById("testFooter");
        const footerRect = footer.getBoundingClientRect();
        const contentSections = document.querySelectorAll(".content-section");

        let overlapping = false;
        contentSections.forEach((section) => {
          const sectionRect = section.getBoundingClientRect();
          if (
            footerRect.top < sectionRect.bottom &&
            footerRect.bottom > sectionRect.top
          ) {
            overlapping = true;
          }
        });

        if (overlapping) {
          console.warn("❌ Footer is overlapping with content!");
        } else {
          console.log("✅ Footer is not overlapping with content");
        }
      }

      // Check on page load and resize
      window.addEventListener("load", checkFooterOverlap);
      window.addEventListener("resize", checkFooterOverlap);

      console.log(
        "Footer position test loaded. Use toggleFooterStyle() to test different footer positions."
      );
    </script>
  </body>
</html>
