<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Mobile Navigation Debug - Hillview</title>

    <!-- Font Awesome -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
      integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <!-- Mobile Navigation CSS -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/mobile_navigation.css') }}"
    />

    <!-- Font Awesome Fixes -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/font_awesome_fixes.css') }}"
    />

    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Oxygen, Ubuntu, Cantarell, sans-serif;
        margin: 0;
        padding: 0;
        line-height: 1.6;
        background: #f5f5f5;
      }

      .debug-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .debug-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .debug-section h2 {
        color: #333;
        margin-top: 0;
        padding-bottom: 10px;
        border-bottom: 2px solid #007bff;
      }

      .icon-test {
        display: inline-block;
        margin: 10px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
        font-size: 1.5rem;
      }

      .status-good {
        color: green;
        font-weight: bold;
      }

      .status-bad {
        color: red;
        font-weight: bold;
      }

      .navbar {
        background: #2c3e50;
        color: white;
        padding: 1rem 0;
        margin-bottom: 20px;
      }

      .navbar-content {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
      }

      .navbar-brand {
        font-size: 1.5rem;
        font-weight: bold;
      }

      .mobile-nav-toggle {
        display: none;
        background: none;
        border: none;
        color: white;
        font-size: 1.5rem;
        padding: 0.5rem;
        cursor: pointer;
        border-radius: 0.375rem;
        transition: all 0.3s ease;
      }

      .mobile-nav-toggle:hover {
        background: rgba(255, 255, 255, 0.1);
      }

      .navbar-nav {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
      }

      .navbar-nav li {
        margin-left: 20px;
      }

      .navbar-nav a {
        color: white;
        text-decoration: none;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        transition: background 0.3s ease;
      }

      .navbar-nav a:hover {
        background: rgba(255, 255, 255, 0.1);
      }

      @media (max-width: 768px) {
        .mobile-nav-toggle {
          display: block;
        }

        .navbar-nav {
          display: none;
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(44, 62, 80, 0.95);
          flex-direction: column;
          justify-content: center;
          align-items: center;
          z-index: 1000;
        }

        .navbar-nav.mobile-nav-open {
          display: flex;
        }

        .navbar-nav li {
          margin: 10px 0;
        }

        .navbar-nav a {
          font-size: 1.2rem;
          padding: 1rem 2rem;
        }
      }

      .test-buttons {
        margin-top: 20px;
      }

      .test-btn {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        margin: 5px;
        border-radius: 4px;
        cursor: pointer;
      }

      .test-btn:hover {
        background: #0056b3;
      }
    </style>
  </head>
  <body>
    <div class="navbar">
      <div class="navbar-content">
        <div class="navbar-brand">Hillview Mobile Debug</div>

        <button
          class="mobile-nav-toggle"
          id="mobileToggle"
          aria-label="Toggle navigation"
        >
          <i class="fas fa-bars"></i>
        </button>

        <ul class="navbar-nav" id="mainNav">
          <li>
            <a href="#" data-tab="upload-marks"
              ><i class="fas fa-upload"></i> Upload Marks</a
            >
          </li>
          <li>
            <a href="#" data-tab="recent-reports"
              ><i class="fas fa-chart-bar"></i> Recent Reports</a
            >
          </li>
          <li>
            <a href="#" data-tab="generate-reports"
              ><i class="fas fa-file-alt"></i> Generate Reports</a
            >
          </li>
          <li>
            <a href="#" data-tab="management"
              ><i class="fas fa-users"></i> Management</a
            >
          </li>
        </ul>
      </div>
    </div>

    <div class="debug-container">
      <div class="debug-section">
        <h2>🔍 Font Awesome Status</h2>
        <div id="fontAwesomeStatus">Checking...</div>
        <div class="test-buttons">
          <button class="test-btn" onclick="testFontAwesome()">
            Test Font Awesome
          </button>
          <button
            class="test-btn"
            onclick="window.FontAwesomeHelper.applyFallbacks()"
          >
            Apply Fallbacks
          </button>
        </div>
      </div>

      <div class="debug-section">
        <h2>📱 Mobile Navigation Test</h2>
        <p>Click the hamburger menu above to test mobile navigation</p>
        <div id="mobileNavStatus">Ready</div>
        <div class="test-buttons">
          <button class="test-btn" onclick="testMobileNav()">
            Test Mobile Navigation
          </button>
          <button class="test-btn" onclick="toggleMobileNav()">
            Toggle Navigation
          </button>
        </div>
      </div>

      <div class="debug-section">
        <h2>🎯 Icon Test Gallery</h2>
        <div>
          <span class="icon-test"><i class="fas fa-bars"></i> fa-bars</span>
          <span class="icon-test"><i class="fas fa-times"></i> fa-times</span>
          <span class="icon-test"><i class="fas fa-home"></i> fa-home</span>
          <span class="icon-test"><i class="fas fa-upload"></i> fa-upload</span>
          <span class="icon-test"
            ><i class="fas fa-chart-bar"></i> fa-chart-bar</span
          >
          <span class="icon-test"
            ><i class="fas fa-file-alt"></i> fa-file-alt</span
          >
          <span class="icon-test"><i class="fas fa-users"></i> fa-users</span>
          <span class="icon-test"><i class="fas fa-cog"></i> fa-cog</span>
        </div>
      </div>

      <div class="debug-section">
        <h2>🔧 Debug Information</h2>
        <div id="debugInfo"></div>
      </div>
    </div>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/font_awesome_helper.js') }}"></script>
    <script src="{{ url_for('static', filename='js/mobile_navigation_fixed.js') }}"></script>

    <script>
      // Test functions
      function testFontAwesome() {
        const status = document.getElementById("fontAwesomeStatus");

        if (window.FontAwesomeHelper) {
          const isLoaded = window.FontAwesomeHelper.checkFontAwesome();
          status.innerHTML = isLoaded
            ? '<span class="status-good">✓ Font Awesome is loaded</span>'
            : '<span class="status-bad">✗ Font Awesome failed to load</span>';
        } else {
          status.innerHTML =
            '<span class="status-bad">✗ Font Awesome Helper not loaded</span>';
        }
      }

      function testMobileNav() {
        const status = document.getElementById("mobileNavStatus");
        const toggle = document.getElementById("mobileToggle");
        const nav = document.getElementById("mainNav");

        if (toggle && nav) {
          status.innerHTML =
            '<span class="status-good">✓ Mobile navigation elements found</span>';
        } else {
          status.innerHTML =
            '<span class="status-bad">✗ Mobile navigation elements missing</span>';
        }
      }

      function toggleMobileNav() {
        const toggle = document.getElementById("mobileToggle");
        if (toggle) {
          toggle.click();
        }
      }

      // Update debug info
      function updateDebugInfo() {
        const debugInfo = document.getElementById("debugInfo");
        debugInfo.innerHTML = `
                <p><strong>Screen Width:</strong> ${window.innerWidth}px</p>
                <p><strong>Screen Height:</strong> ${window.innerHeight}px</p>
                <p><strong>User Agent:</strong> ${navigator.userAgent}</p>
                <p><strong>Touch Support:</strong> ${
                  "ontouchstart" in window ? "Yes" : "No"
                }</p>
                <p><strong>Mobile Detection:</strong> ${
                  window.innerWidth <= 768 ? "Mobile" : "Desktop"
                }</p>
            `;
      }

      // Initialize
      document.addEventListener("DOMContentLoaded", function () {
        updateDebugInfo();
        testFontAwesome();
        testMobileNav();

        // Update debug info on resize
        window.addEventListener("resize", updateDebugInfo);
      });

      // Manual mobile navigation setup
      document.addEventListener("DOMContentLoaded", function () {
        const toggle = document.getElementById("mobileToggle");
        const nav = document.getElementById("mainNav");

        if (toggle && nav) {
          toggle.addEventListener("click", function () {
            nav.classList.toggle("mobile-nav-open");

            const icon = toggle.querySelector("i");
            if (icon) {
              if (nav.classList.contains("mobile-nav-open")) {
                icon.className = "fas fa-times";
              } else {
                icon.className = "fas fa-bars";
              }
            }
          });

          // Close on nav link click
          nav.addEventListener("click", function (e) {
            if (e.target.tagName === "A") {
              nav.classList.remove("mobile-nav-open");
              const icon = toggle.querySelector("i");
              if (icon) {
                icon.className = "fas fa-bars";
              }
            }
          });
        }
      });
    </script>
  </body>
</html>
