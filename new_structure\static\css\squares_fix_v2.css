/* ===== SQUARES FIX V3 - UNICODE SYMBOLS APPROACH ===== */
/* Replace squares with Unicode symbols that work everywhere */

/* 1. HIDE ALL PROBLEMATIC PSEUDO-ELEMENTS */
*::before,
*::after {
  content: none !important;
}

/* 2. REPLACE FONT AWESOME ICONS WITH UNICODE SYMBOLS */
/* These symbols work without any external fonts */
.fa-users::before,
.fas.fa-users::before {
  content: "👥" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-book::before,
.fas.fa-book::before {
  content: "📚" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-user-graduate::before,
.fas.fa-user-graduate::before {
  content: "🎓" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-chalkboard-teacher::before,
.fas.fa-chalkboard-teacher::before {
  content: "👨‍🏫" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-cog::before,
.fa-cogs::before,
.fas.fa-cog::before,
.fas.fa-cogs::before {
  content: "⚙️" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-chart-bar::before,
.fas.fa-chart-bar::before {
  content: "📊" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-chart-line::before,
.fas.fa-chart-line::before {
  content: "📈" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-upload::before,
.fas.fa-upload::before {
  content: "📤" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-download::before,
.fas.fa-download::before {
  content: "📥" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-file-pdf::before,
.fas.fa-file-pdf::before {
  content: "📄" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-plus::before,
.fas.fa-plus::before {
  content: "+" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
  font-weight: bold !important;
}

.fa-minus::before,
.fas.fa-minus::before {
  content: "−" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
  font-weight: bold !important;
}

.fa-edit::before,
.fas.fa-edit::before {
  content: "✏️" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-trash::before,
.fas.fa-trash::before {
  content: "🗑️" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-home::before,
.fas.fa-home::before {
  content: "🏠" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-user::before,
.fas.fa-user::before {
  content: "👤" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-lock::before,
.fas.fa-lock::before {
  content: "🔒" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-bars::before,
.fas.fa-bars::before {
  content: "☰" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
  font-weight: bold !important;
}

.fa-times::before,
.fas.fa-times::before {
  content: "✕" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
  font-weight: bold !important;
}

.fa-check::before,
.fas.fa-check::before {
  content: "✓" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
  font-weight: bold !important;
}

.fa-arrow-right::before,
.fas.fa-arrow-right::before {
  content: "→" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
  font-weight: bold !important;
}

.fa-graduation-cap::before,
.fas.fa-graduation-cap::before {
  content: "🎓" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-clipboard-list::before,
.fas.fa-clipboard-list::before {
  content: "📋" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-layer-group::before,
.fas.fa-layer-group::before {
  content: "📚" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-file-export::before,
.fas.fa-file-export::before {
  content: "📤" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

.fa-plus-circle::before,
.fas.fa-plus-circle::before {
  content: "⊕" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
  font-weight: bold !important;
}

.fa-info-circle::before,
.fas.fa-info-circle::before {
  content: "ℹ️" !important;
  font-family: Arial, sans-serif !important;
  display: inline-block !important;
}

/* 5. ENSURE ICONS ARE VISIBLE */
.fa,
.fas,
.far,
.fab,
.fal,
.fad,
i[class^="fa-"],
i[class*=" fa-"] {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  font-size: inherit !important;
  line-height: 1 !important;
}

/* 6. DASHBOARD CARD SPECIFIC FIXES */
.quick-action-card .quick-action-icon,
.management-card .card-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 48px !important;
  height: 48px !important;
  background: rgba(0, 123, 255, 0.1) !important;
  border-radius: 12px !important;
  margin-bottom: 16px !important;
}

.quick-action-card .quick-action-icon i,
.management-card .card-icon i {
  font-size: 24px !important;
  color: #007bff !important;
  font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome" !important;
  font-weight: 900 !important;
}

/* 7. CHECKBOX FIXES */
input[type="checkbox"],
input[type="radio"] {
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  width: 18px !important;
  height: 18px !important;
  border: 2px solid #007bff !important;
  border-radius: 4px !important;
  background: white !important;
  cursor: pointer !important;
  position: relative !important;
  margin-right: 8px !important;
  vertical-align: middle !important;
}

input[type="radio"] {
  border-radius: 50% !important;
}

input[type="checkbox"]:checked {
  background: #007bff !important;
  border-color: #007bff !important;
}

input[type="checkbox"]:checked::before {
  content: "✓" !important;
  color: white !important;
  font-size: 12px !important;
  font-weight: bold !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  font-family: Arial, sans-serif !important;
}

input[type="radio"]:checked {
  background: #007bff !important;
  border-color: #007bff !important;
}

input[type="radio"]:checked::before {
  content: "" !important;
  width: 6px !important;
  height: 6px !important;
  border-radius: 50% !important;
  background: white !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

/* 8. EMERGENCY FALLBACK - TEXT REPLACEMENTS */
/* If icons still don't work, show text */
.font-awesome-failed .fa-users::before {
  content: "Users" !important;
  font-family: Arial !important;
}
.font-awesome-failed .fa-book::before {
  content: "Book" !important;
  font-family: Arial !important;
}
.font-awesome-failed .fa-user-graduate::before {
  content: "Student" !important;
  font-family: Arial !important;
}
.font-awesome-failed .fa-cog::before {
  content: "Settings" !important;
  font-family: Arial !important;
}
.font-awesome-failed .fa-chart-bar::before {
  content: "Chart" !important;
  font-family: Arial !important;
}
.font-awesome-failed .fa-upload::before {
  content: "Upload" !important;
  font-family: Arial !important;
}

/* 9. HIDE EMPTY ICONS */
.fa:empty,
.fas:empty,
.far:empty,
.fab:empty {
  display: none !important;
}
