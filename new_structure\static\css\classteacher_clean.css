/* Clean Classteacher Dashboard Styles */

/* Reset and Base Styles */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #f8f9fa;
}

/* Navigation Styles */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar-brand {
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 1rem;
    align-items: center;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
}

.logout-btn {
    background: rgba(220, 53, 69, 0.2);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.logout-btn:hover {
    background: rgba(220, 53, 69, 0.3);
    border-color: rgba(220, 53, 69, 0.5);
}

/* Dashboard Container */
.dashboard-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

/* Welcome Section */
.dashboard-welcome {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    text-align: center;
}

.dashboard-welcome h1 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
}

.dashboard-welcome p {
    margin: 0;
    opacity: 0.9;
}

/* Dashboard Cards */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #667eea;
    transition: transform 0.3s, box-shadow 0.3s;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.card-icon {
    font-size: 2rem;
    color: #667eea;
    margin-right: 1rem;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.card-content {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* Card Variations */
.card-upload {
    border-left-color: #28a745;
}

.card-upload .card-icon {
    color: #28a745;
}

.card-reports {
    border-left-color: #17a2b8;
}

.card-reports .card-icon {
    color: #17a2b8;
}

.card-students {
    border-left-color: #ffc107;
}

.card-students .card-icon {
    color: #ffc107;
}

/* Button Styles */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-primary:hover {
    background-color: #5a6fd8;
    text-decoration: none;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
    text-decoration: none;
    color: white;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background-color: #138496;
    text-decoration: none;
    color: white;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
    text-decoration: none;
    color: #212529;
}

/* Quick Actions Section */
.quick-actions {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.quick-actions h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s;
}

.action-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    text-decoration: none;
    color: #333;
}

.action-icon {
    font-size: 1.5rem;
    color: #667eea;
    margin-right: 1rem;
}

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
}

.quick-stat {
    text-align: center;
}

.quick-stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
}

.quick-stat-label {
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.25rem;
}

/* Assignment Lists */
.assignment-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.assignment-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 5px;
    gap: 0.75rem;
}

.assignment-badge {
    background: #17a2b8;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.75rem;
    margin-left: auto;
}

/* Alert Styles */
.alert {
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 5px;
    border: 1px solid transparent;
    transition: opacity 0.3s;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* Footer */
footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 1rem;
    margin-top: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .navbar-nav {
        flex-direction: column;
        width: 100%;
        gap: 0.5rem;
    }

    .nav-link {
        justify-content: center;
        width: 100%;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    .actions-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-welcome h1 {
        font-size: 1.5rem;
    }

    .quick-stats {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 480px) {
    .dashboard-container {
        padding: 0 0.5rem;
    }

    .dashboard-card {
        padding: 1rem;
    }

    .card-actions {
        flex-direction: column;
    }

    .btn {
        justify-content: center;
        width: 100%;
    }
}
