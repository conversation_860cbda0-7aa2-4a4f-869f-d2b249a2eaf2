# Code Citations

## License: unknown
https://github.com/mold/explr/blob/72d3be4623f40ee031c28af800de85e18777437e/src/assets/js/aria-announcer.js

```
,
```


## License: unknown
https://github.com/mold/explr/blob/72d3be4623f40ee031c28af800de85e18777437e/src/assets/js/aria-announcer.js

```
, wait) {
    let timeout;
    return function executedFunction(...
```


## License: unknown
https://github.com/mold/explr/blob/72d3be4623f40ee031c28af800de85e18777437e/src/assets/js/aria-announcer.js

```
, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later =
```


## License: unknown
https://github.com/mold/explr/blob/72d3be4623f40ee031c28af800de85e18777437e/src/assets/js/aria-announcer.js

```
, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(
```


## License: unknown
https://github.com/mold/explr/blob/72d3be4623f40ee031c28af800de85e18777437e/src/assets/js/aria-announcer.js

```
, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
```


## License: unknown
https://github.com/mold/explr/blob/72d3be4623f40ee031c28af800de85e18777437e/src/assets/js/aria-announcer.js

```
, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(
```


## License: unknown
https://github.com/mold/explr/blob/72d3be4623f40ee031c28af800de85e18777437e/src/assets/js/aria-announcer.js

```
, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(
```


## License: unknown
https://github.com/mold/explr/blob/72d3be4623f40ee031c28af800de85e18777437e/src/assets/js/aria-announcer.js

```
, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    }
```

<script>
// Enhanced debugging for mobile navigation
function toggleClassteacherNav() {
    console.log('🔧 =================================');
    console.log('🔧 MOBILE NAV TOGGLE CLICKED - ENHANCED DEBUG');
    console.log('🔧 =================================');
    
    const classteacherNav = document.getElementById('classteacherNav');
    const toggleBtn = document.querySelector('.mobile-nav-toggle i');
    
    console.log('🔍 Nav element found:', !!classteacherNav);
    console.log('🔍 Toggle button found:', !!toggleBtn);
    
    if (classteacherNav) {
        console.log('📊 Current nav state:');
        console.log('  - Has "show" class:', classteacherNav.classList.contains('show'));
        console.log('  - Display style:', window.getComputedStyle(classteacherNav).display);
        console.log('  - Visibility:', window.getComputedStyle(classteacherNav).visibility);
        console.log('  - Opacity:', window.getComputedStyle(classteacherNav).opacity);
        console.log('  - Position:', window.getComputedStyle(classteacherNav).position);
        console.log('  - Z-index:', window.getComputedStyle(classteacherNav).zIndex);
        console.log('  - Background:', window.getComputedStyle(classteacherNav).backgroundColor);
        console.log('  - Width:', window.getComputedStyle(classteacherNav).width);
        console.log('  - Height:', window.getComputedStyle(classteacherNav).height);
        console.log('  - All classes:', classteacherNav.className);
        
        // Check nav links
        const navLinks = classteacherNav.querySelectorAll('.nav-link');
        console.log('🔗 Navigation links found:', navLinks.length);
        navLinks.forEach((link, index) => {
            console.log(`  Link ${index + 1}: "${link.textContent.trim()}"`, {
                display: window.getComputedStyle(link).display,
                visibility: window.getComputedStyle(link).visibility,
                opacity: window.getComputedStyle(link).opacity
            });
        });
        
        // Toggle the show class
        if (classteacherNav.classList.contains('show')) {
            // Hide menu
            console.log('🔄 HIDING MENU...');
            classteacherNav.classList.remove('show');
            
            // Force hide with inline styles
            classteacherNav.style.display = 'none !important';
            classteacherNav.style.visibility = 'hidden !important';
            classteacherNav.style.opacity = '0 !important';
            
            if (toggleBtn) {
                toggleBtn.classList.remove('fa-times');
                toggleBtn.classList.add('fa-bars');
                console.log('🔄 Toggle button changed to bars');
            }
            
            document.body.style.overflow = '';
            console.log('✅ Menu hidden successfully');
            
        } else {
            // Show menu
            console.log('🔄 SHOWING MENU...');
            classteacherNav.classList.add('show');
            
            // Force show with inline styles
            classteacherNav.style.cssText = `
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                background: rgba(102, 126, 234, 0.98) !important;
                backdrop-filter: blur(20px) !important;
                flex-direction: column !important;
                justify-content: center !important;
                align-items: center !important;
                gap: 1.5rem !important;
                z-index: 9999 !important;
                padding: 2rem !important;
                margin: 0 !important;
                list-style: none !important;
            `;
            
            if (toggleBtn) {
                toggleBtn.classList.remove('fa-bars');
                toggleBtn.classList.add('fa-times');
                console.log('🔄 Toggle button changed to times');
            }
            
            document.body.style.overflow = 'hidden';
            console.log('✅ Menu shown successfully');
            
            // Debug the nav links after showing
            setTimeout(() => {
                const navLinksAfter = classteacherNav.querySelectorAll('.nav-link');
                console.log('🔗 After showing - Navigation links:', navLinksAfter.length);
                navLinksAfter.forEach((link, index) => {
                    console.log(`  Link ${index + 1} after show:`, {
                        text: link.textContent.trim(),
                        display: window.getComputedStyle(link).display,
                        visibility: window.getComputedStyle(link).visibility,
                        opacity: window.getComputedStyle(link).opacity,
                        color: window.getComputedStyle(link).color,
                        backgroundColor: window.getComputedStyle(link).backgroundColor
                    });
                    
                    // Force show each link
                    link.style.cssText = `
                        display: flex !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        color: white !important;
                        background: rgba(255, 255, 255, 0.1) !important;
                        padding: 1rem 1.5rem !important;
                        border-radius: 0.5rem !important;
                        text-decoration: none !important;
                        font-size: 1.1rem !important;
                        font-weight: 500 !important;
                        min-width: 200px !important;
                        text-align: center !important;
                        margin: 0.5rem 0 !important;
                        border: 1px solid rgba(255, 255, 255, 0.2) !important;
                        transition: all 0.2s ease !important;
                        align-items: center !important;
                        justify-content: center !important;
                        gap: 0.5rem !important;
                    `;
                });
            }, 100);
        }
        
        console.log('📊 Final nav state:');
        console.log('  - Has "show" class:', classteacherNav.classList.contains('show'));
        console.log('  - Display style:', window.getComputedStyle(classteacherNav).display);
        console.log('  - Visibility:', window.getComputedStyle(classteacherNav).visibility);
        console.log('  - Opacity:', window.getComputedStyle(classteacherNav).opacity);
        
    } else {
        console.error('❌ classteacherNav element not found');
        console.log('🔍 Available elements with ID:');
        const allElementsWithId = document.querySelectorAll('[id]');
        allElementsWithId.forEach(el => {
            if (el.id.includes('nav') || el.id.includes('Nav')) {
                console.log(`  - ${el.id}: ${el.tagName}`);
            }
        });
    }
    
    console.log('🔧 =================================');
    console.log('🔧 MOBILE NAV TOGGLE DEBUG END');
    console.log('🔧 =================================');
}

// Enhanced navigation function with debug
window.navigateToFeature = function(feature) {
    console.log('🔧 =================================');
    console.log('🔧 NAVIGATE TO FEATURE DEBUG START');
    console.log('🔧 =================================');
    console.log('🔍 Feature requested:', feature);
    console.log('🔍 Window width:', window.innerWidth);
    console.log('🔍 Is mobile?', window.innerWidth <= 768);
    
    // Close mobile nav first
    const classteacherNav = document.getElementById('classteacherNav');
    if (classteacherNav && classteacherNav.classList.contains('show')) {
        console.log('🔄 Closing mobile nav before navigation...');
        window.toggleClassteacherNav();
    }
    
    // Wait for nav to close, then navigate
    setTimeout(() => {
        console.log('🔄 Proceeding with navigation to:', feature);
        
        switch (feature) {
            case 'upload-marks':
                console.log('🔧 Navigating to upload marks');
                
                // Use the existing switchMainTab function
                if (typeof switchMainTab === 'function') {
                    console.log('✅ switchMainTab function found, calling it');
                    switchMainTab('upload-marks');
                } else {
                    console.warn('⚠️ switchMainTab function not found, using fallback');
                    
                    // Fallback method
                    const uploadTab = document.getElementById('upload-marks-tab');
                    console.log('🔍 Upload tab found:', !!uploadTab);
                    
                    if (uploadTab) {
                        // Hide all tabs
                        document.querySelectorAll('.tab-content-container').forEach(tab => {
                            tab.classList.remove('active');
                            console.log('🔄 Removed active class from tab:', tab.id);
                        });
                        
                        // Show upload marks tab
                        uploadTab.classList.add('active');
                        console.log('✅ Added active class to upload marks tab');
                        
                        // Update tab buttons
                        document.querySelectorAll('.tab-button').forEach(btn => {
                            btn.classList.remove('active');
                        });
                        
                        const uploadBtn = document.querySelector('.tab-button[onclick*="upload-marks"]');
                        if (uploadBtn) {
                            uploadBtn.classList.add('active');
                            console.log('✅ Updated upload marks tab button');
                        }
                        
                        // Check if upload marks content is visible
                        const uploadSection = document.getElementById('upload-marks-section');
                        if (uploadSection) {
                            console.log('🔍 Upload section found:', !!uploadSection);
                            console.log('🔍 Upload section display:', window.getComputedStyle(uploadSection).display);
                            console.log('🔍 Upload section visibility:', window.getComputedStyle(uploadSection).visibility);
                            
                            // Force show upload section
                            uploadSection.style.cssText = `
                                display: block !important;
                                visibility: visible !important;
                                opacity: 1 !important;
                                width: 100% !important;
                                padding: 1rem !important;
                            `;
                            
                            console.log('✅ Forced upload section to be visible');
                        }
                    }
                }
                
                // Scroll to upload marks section
                setTimeout(() => {
                    const uploadSection = document.getElementById('upload-marks-tab') ||
                                        document.getElementById('upload-marks-section');
                    if (uploadSection) {
                        console.log('🔄 Scrolling to upload section...');
                        uploadSection.scrollIntoView({ 
                            behavior: 'smooth',
                            block: 'start'
                        });
                        console.log('✅ Scrolled to upload marks section');
                    } else {
                        console.error('❌ Could not find upload section to scroll to');
                    }
                }, 100);
                break;
                
            case 'recent-reports':
                console.log('🔧 Navigating to recent reports');
                if (typeof switchMainTab === 'function') {
                    switchMainTab('recent-reports');
                } else {
                    console.warn('⚠️ switchMainTab function not found for recent-reports');
                }
                break;
                
            case 'generate-reports':
                console.log('🔧 Navigating to generate reports');
                if (typeof switchMainTab === 'function') {
                    switchMainTab('generate-marksheet');
                } else {
                    console.warn('⚠️ switchMainTab function not found for generate-reports');
                }
                break;
                
            case 'management':
                console.log('🔧 Navigating to management');
                const managementSection = document.getElementById('management-section');
                if (managementSection) {
                    managementSection.scrollIntoView({ 
                        behavior: 'smooth',
                        block: 'start'
                    });
                    console.log('✅ Scrolled to management section');
                } else {
                    console.error('❌ Management section not found');
                }
                break;
                
            default:
                console.warn('⚠️ Unknown feature:', feature);
        }
        
        console.log('✅ Navigation completed for:', feature);
    }, 300); // Wait for nav animation
    
    console.log('🔧 =================================');
    console.log('🔧 NAVIGATE TO FEATURE DEBUG END');
    console.log('🔧 =================================');
    
    return false; // Prevent default link behavior
};

// Debug function to check current page state
function debugPageState() {
    console.log('🔧 =================================');
    console.log('🔧 PAGE STATE DEBUG');
    console.log('🔧 =================================');
    
    console.log('📱 Device info:');
    console.log('  - Window width:', window.innerWidth);
    console.log('  - Window height:', window.innerHeight);
    console.log('  - User agent:', navigator.userAgent);
    console.log('  - Touch support:', 'ontouchstart' in window);
    
    console.log('🎯 Navigation elements:');
    const nav = document.getElementById('classteacherNav');
    console.log('  - Nav element exists:', !!nav);
    if (nav) {
        console.log('  - Nav classes:', nav.className);
        console.log('  - Nav children:', nav.children.length);
        console.log('  - Nav display:', window.getComputedStyle(nav).display);
    }
    
    const toggle = document.querySelector('.mobile-nav-toggle');
    console.log('  - Toggle button exists:', !!toggle);
    if (toggle) {
        console.log('  - Toggle display:', window.getComputedStyle(toggle).display);
    }
    
    console.log('📋 Tab elements:');
    const tabs = document.querySelectorAll('.tab-content-container');
    console.log('  - Total tabs:', tabs.length);
    tabs.forEach((tab, index) => {
        console.log(`  - Tab ${index + 1} (${tab.id}):`, {
            active: tab.classList.contains('active'),
            display: window.getComputedStyle(tab).display
        });
    });
    
    console.log('🔧 =================================');
    console.log('🔧 PAGE STATE DEBUG END');
    console.log('🔧 =================================');
}

// Enhanced CSS for mobile navigation with debug
const debugMobileCSS = `
<style>
/* Enhanced Mobile Navigation with Debug */
@media (max-width: 768px) {
    .mobile-nav-toggle {
        display: block !important;
        position: relative !important;
        z-index: 10000 !important;
        background: none !important;
        border: none !important;
        color: white !important;
        font-size: 1.5rem !important;
        padding: 0.5rem !important;
        cursor: pointer !important;
        border-radius: 0.375rem !important;
        transition: all 0.2s ease !important;
    }

    .mobile-nav-toggle:hover {
        background: rgba(255, 255, 255, 0.1) !important;
    }

    .navbar-nav {
        display: none !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: rgba(102, 126, 234, 0.98) !important;
        backdrop-filter: blur(20px) !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        gap: 1.5rem !important;
        z-index: 9999 !important;
        padding: 2rem !important;
        margin: 0 !important;
        list-style: none !important;
    }

    .navbar-nav.show {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    #classteacherNav {
        display: none !important;
    }

    #classteacherNav.show {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: rgba(102, 126, 234, 0.98) !important;
        backdrop-filter: blur(20px) !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        gap: 1.5rem !important;
        z-index: 9999 !important;
        padding: 2rem !important;
        margin: 0 !important;
        list-style: none !important;
    }

    .navbar-nav .nav-link {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 1rem 1.5rem !important;
        color: white !important;
        text-decoration: none !important;
        border-radius: 0.5rem !important;
        transition: all 0.2s ease !important;
        font-size: 1.1rem !important;
        font-weight: 500 !important;
        min-width: 200px !important;
        text-align: center !important;
        background: rgba(255, 255, 255, 0.1) !important;
        margin: 0.5rem 0 !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        gap: 0.5rem !important;
    }

    .navbar-nav .nav-link:hover {
        background: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
        transform: translateY(-2px) !important;
        text-decoration: none !important;
    }

    .navbar-nav .nav-link i {
        margin-right: 0.5rem !important;
        font-size: 1.2rem !important;
    }

    .navbar-nav .logout-btn {
        background: rgba(220, 53, 69, 0.2) !important;
        border: 1px solid rgba(220, 53, 69, 0.3) !important;
        color: #ff6b6b !important;
    }

    .navbar-nav .logout-btn:hover {
        background: rgba(220, 53, 69, 0.3) !important;
        color: white !important;
    }

    /* Upload Marks Section Mobile Fix */
    #upload-marks-tab {
        display: block !important;
        width: 100% !important;
        padding: 1rem !important;
        margin-top: 1rem !important;
    }

    #upload-marks-tab.active {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .tab-content-container {
        width: 100% !important;
        padding: 0.5rem !important;
    }

    .tab-content-container.active {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Debug outline for mobile nav */
    .navbar-nav.show {
        outline: 3px solid red !important;
    }

    .navbar-nav .nav-link {
        outline: 1px solid yellow !important;
    }
}
</style>
`;

// Add debug CSS to head
document.head.insertAdjacentHTML('beforeend', debugMobileCSS);

// Add debug buttons for testing
document.addEventListener('DOMContentLoaded', function() {
    // Add debug buttons to the page
    const debugContainer = document.createElement('div');
    debugContainer.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 10001;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-size: 12px;
        display: none;
    `;
    
    if (window.innerWidth <= 768) {
        debugContainer.style.display = 'block';
    }
    
    debugContainer.innerHTML = `
        <button onclick="debugPageState()" style="background: #007bff; color: white; border: none; padding: 5px 10px; margin: 2px; border-radius: 3px; cursor: pointer;">
            Debug Page
        </button>
        <button onclick="toggleClassteacherNav()" style="background: #28a745; color: white; border: none; padding: 5px 10px; margin: 2px; border-radius: 3px; cursor: pointer;">
            Toggle Nav
        </button>
        <button onclick="navigateToFeature('upload-marks')" style="background: #ffc107; color: black; border: none; padding: 5px 10px; margin: 2px; border-radius: 3px; cursor: pointer;">
            Test Upload
        </button>
    `;
    
    document.body.appendChild(debugContainer);
    
    // Initial debug
    console.log('🔧 Page loaded - running initial debug...');
    debugPageState();
});

// Make functions globally available
window.toggleClassteacherNav = toggleClassteacherNav;
window.debugPageState = debugPageState;
</script>

