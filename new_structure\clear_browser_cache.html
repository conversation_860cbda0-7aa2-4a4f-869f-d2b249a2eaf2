<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Browser Cache - Hillview School</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 28px;
        }
        
        .problem {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            color: #856404;
        }
        
        .solution {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            color: #155724;
        }
        
        .steps {
            text-align: left;
            margin: 20px 0;
        }
        
        .steps ol {
            padding-left: 20px;
        }
        
        .steps li {
            margin: 10px 0;
            line-height: 1.6;
        }
        
        .url-box {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            margin: 15px 0;
            word-break: break-all;
            color: #495057;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            display: inline-block;
            margin: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .browser-instructions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .browser-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #dee2e6;
        }
        
        .browser-card h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .keyboard-shortcut {
            background: #343a40;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-family: monospace;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🔧</div>
        <h1>Fix HTTPS/SSL Errors</h1>
        
        <div class="problem">
            <h3>🚨 Problem Detected</h3>
            <p>Your browser is trying to access the Hillview School Management System using HTTPS, but the server only supports HTTP connections.</p>
        </div>
        
        <div class="solution">
            <h3>✅ Quick Solution</h3>
            <p>Use the HTTP URL instead of HTTPS:</p>
            <div class="url-box" id="http-url">
                http://*************:8080
            </div>
        </div>
        
        <div class="steps">
            <h3>🛠️ Step-by-Step Fix</h3>
            <ol>
                <li><strong>Clear your browser cache</strong> (instructions below)</li>
                <li><strong>Type the HTTP URL manually</strong> in the address bar</li>
                <li><strong>Make sure to use http://</strong> not https://</li>
                <li><strong>Bookmark the HTTP version</strong> to avoid future issues</li>
            </ol>
        </div>
        
        <div class="browser-instructions">
            <div class="browser-card">
                <h3>🌐 Chrome</h3>
                <p>Press <span class="keyboard-shortcut">Ctrl + Shift + Delete</span></p>
                <p>Select "Cached images and files" and click "Clear data"</p>
            </div>
            
            <div class="browser-card">
                <h3>🦊 Firefox</h3>
                <p>Press <span class="keyboard-shortcut">Ctrl + Shift + Delete</span></p>
                <p>Select "Cache" and click "Clear Now"</p>
            </div>
            
            <div class="browser-card">
                <h3>🧭 Safari</h3>
                <p>Press <span class="keyboard-shortcut">Cmd + Option + E</span></p>
                <p>Or go to Develop > Empty Caches</p>
            </div>
            
            <div class="browser-card">
                <h3>📱 Mobile</h3>
                <p>Go to browser settings and clear browsing data/cache</p>
                <p>Or try using incognito/private mode</p>
            </div>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="http://*************:8080" class="btn">
                🏠 Go to Hillview School (HTTP)
            </a>
            <a href="http://localhost:8080" class="btn btn-secondary">
                💻 Local Access (if on same computer)
            </a>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #e9ecef; border-radius: 8px;">
            <h4>💡 Pro Tips</h4>
            <ul style="text-align: left; display: inline-block;">
                <li>Always use <strong>http://</strong> not https:// for this application</li>
                <li>Bookmark the correct HTTP URL to avoid typing it each time</li>
                <li>If problems persist, try using incognito/private browsing mode</li>
                <li>Make sure you're on the same WiFi network as the server</li>
            </ul>
        </div>
        
        <script>
            // Auto-update the IP address if different
            function updateIPAddress() {
                const currentHost = window.location.hostname;
                if (currentHost && currentHost !== 'localhost' && currentHost !== '127.0.0.1') {
                    const httpUrl = document.getElementById('http-url');
                    const newUrl = `http://${currentHost}:8080`;
                    httpUrl.textContent = newUrl;
                    
                    // Update all links
                    const links = document.querySelectorAll('a[href*="*************"]');
                    links.forEach(link => {
                        link.href = newUrl;
                    });
                }
            }
            
            // Copy URL to clipboard when clicked
            document.getElementById('http-url').addEventListener('click', function() {
                navigator.clipboard.writeText(this.textContent).then(() => {
                    const original = this.textContent;
                    this.textContent = '✅ Copied to clipboard!';
                    this.style.background = '#d4edda';
                    setTimeout(() => {
                        this.textContent = original;
                        this.style.background = '#f8f9fa';
                    }, 2000);
                });
            });
            
            updateIPAddress();
        </script>
    </div>
</body>
</html>
