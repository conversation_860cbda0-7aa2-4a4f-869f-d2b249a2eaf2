<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text Visibility Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .header-test {
            background: linear-gradient(135deg, #1f7d53 0%, #0f5132 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .header-test h1 {
            color: white !important;
            font-size: 2rem;
            margin: 0 0 10px 0;
        }
        
        .header-test p {
            color: white !important;
            margin: 0 0 15px 0;
        }
        
        .header-test a {
            color: white !important;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 5px;
            text-decoration: none;
            margin-right: 10px;
        }
        
        .text-test {
            color: #333 !important;
            font-size: 1rem;
            margin: 10px 0;
        }
        
        .force-visible {
            color: #000000 !important;
            background: #ffffff !important;
            padding: 10px;
            border: 2px solid #ff0000;
        }
    </style>
</head>
<body>
    <h1>Text Visibility Test Page</h1>
    
    <div class="test-container">
        <h2>Basic Text Test</h2>
        <p class="text-test">This is a basic text test. Can you see this clearly?</p>
        <p class="force-visible">This text should be absolutely visible with black text on white background.</p>
    </div>
    
    <div class="header-test">
        <h1>👨‍🎓 Manage Students</h1>
        <p>Add, edit, and manage student profiles and information</p>
        <div>
            <a href="#">Teacher Hub</a>
            <a href="#">Dashboard</a>
            <a href="#">Logout</a>
        </div>
    </div>
    
    <div class="test-container">
        <h3>CSS Variable Test</h3>
        <p style="color: var(--text-primary, #000000) !important;">Text using CSS variable with fallback</p>
        <p style="color: #1f7d53 !important;">Text using direct color value</p>
        <p style="color: #333333 !important;">Text using dark gray</p>
    </div>
    
    <div class="test-container">
        <h3>Debugging Info</h3>
        <p>If you can see this text clearly, the issue is with the manage students page specifically.</p>
        <p>If this text is also hard to see, there's a broader CSS issue.</p>
    </div>
</body>
</html>
