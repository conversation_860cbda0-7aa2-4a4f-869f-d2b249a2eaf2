<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Navigation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
            width: 100%;
        }
        
        .test-button:hover {
            background: #5a6fd8;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>Mobile Navigation Test</h2>
        <p>This page tests if the mobile navigation fixes are working correctly.</p>
        
        <button class="test-button" onclick="testMobileNav()">
            🔧 Test Mobile Navigation
        </button>
        
        <button class="test-button" onclick="testCSRFToken()">
            🔒 Test CSRF Token
        </button>
        
        <button class="test-button" onclick="window.location.href='http://localhost:8080'">
            🏠 Go to Main App
        </button>
        
        <div id="test-results"></div>
    </div>

    <script>
        function testMobileNav() {
            const results = document.getElementById('test-results');
            
            // Test if mobile navigation elements exist
            const tests = [
                {
                    name: 'CSS setProperty support',
                    test: () => {
                        const div = document.createElement('div');
                        div.style.setProperty('display', 'flex', 'important');
                        return div.style.display === 'flex';
                    }
                },
                {
                    name: 'Mobile viewport detection',
                    test: () => window.innerWidth <= 768
                },
                {
                    name: 'Touch events support',
                    test: () => 'ontouchstart' in window
                },
                {
                    name: 'Flexbox support',
                    test: () => {
                        const div = document.createElement('div');
                        div.style.display = 'flex';
                        return div.style.display === 'flex';
                    }
                }
            ];
            
            let html = '<h3>Mobile Navigation Test Results:</h3>';
            let allPassed = true;
            
            tests.forEach(test => {
                const passed = test.test();
                allPassed = allPassed && passed;
                html += `<div class="${passed ? 'success' : 'error'}">
                    ${passed ? '✅' : '❌'} ${test.name}: ${passed ? 'PASS' : 'FAIL'}
                </div>`;
            });
            
            html += `<div class="${allPassed ? 'success' : 'error'}">
                <strong>Overall: ${allPassed ? 'All tests passed! Mobile nav should work.' : 'Some tests failed. Check browser compatibility.'}</strong>
            </div>`;
            
            results.innerHTML = html;
        }
        
        function testCSRFToken() {
            const results = document.getElementById('test-results');
            
            // Test CSRF token functionality
            fetch('http://localhost:8080/', {
                method: 'GET',
                credentials: 'include'
            })
            .then(response => response.text())
            .then(html => {
                const hasCSRF = html.includes('csrf_token') || html.includes('_token');
                const hasForm = html.includes('<form');
                
                let resultHtml = '<h3>CSRF Token Test Results:</h3>';
                resultHtml += `<div class="${hasCSRF ? 'success' : 'error'}">
                    ${hasCSRF ? '✅' : '❌'} CSRF Token: ${hasCSRF ? 'Found in page' : 'Not found'}
                </div>`;
                resultHtml += `<div class="${hasForm ? 'success' : 'error'}">
                    ${hasForm ? '✅' : '❌'} Forms: ${hasForm ? 'Found in page' : 'Not found'}
                </div>`;
                
                const overall = hasCSRF && hasForm;
                resultHtml += `<div class="${overall ? 'success' : 'error'}">
                    <strong>Overall: ${overall ? 'CSRF protection should work!' : 'CSRF issues detected.'}</strong>
                </div>`;
                
                results.innerHTML = resultHtml;
            })
            .catch(error => {
                results.innerHTML = `<div class="error">
                    ❌ Error testing CSRF: ${error.message}
                </div>`;
            });
        }
        
        // Auto-run mobile nav test on load
        window.addEventListener('load', testMobileNav);
    </script>
</body>
</html>
