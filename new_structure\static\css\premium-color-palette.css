/* ===================================================================
   HILLVIEW SCHOOL MANAGEMENT SYSTEM - PREMIUM COLOR PALETTE
   Modern, accessible, and professional color scheme for educational software
   ================================================================== */

:root {
  /* ===== PREMIUM LIGHT THEME ===== */

  /* Primary Brand Colors - Your Custom Light Theme */
  --primary-color: #263ad1; /* Primary text - blue */
  --primary-hover: #1e2db5; /* Darker blue for hover states */
  --primary-light: #4f5cd9; /* Lighter blue for subtle elements */
  --primary-dark: #1a2690; /* Darker blue for emphasis */
  --primary-50: #f0f2ff; /* Ultra light primary for backgrounds */
  --primary-100: #e6e9ff; /* Light primary for subtle backgrounds */
  --primary-200: #d1d6ff; /* Lighter primary for borders */

  /* Secondary Colors - Secondary Text */
  --secondary-color: #484b6a; /* Secondary text - dark gray */
  --secondary-hover: #3a3d57; /* Darker gray for hover */
  --secondary-light: #565a7d; /* Light gray for accents */
  --secondary-dark: #36394f; /* Dark gray for depth */
  --secondary-50: #f7f7f9; /* Ultra light gray */
  --secondary-100: #f0f0f3; /* Light gray background */
  --secondary-200: #e3e4e8; /* Lighter gray for borders */

  /* Accent Colors - Button/Accent Color */
  --accent-color: #f9cc48; /* Accent yellow for buttons */
  --accent-hover: #f7c332; /* Darker yellow for hover */
  --accent-light: #fbdc6e; /* Light yellow for highlights */
  --accent-dark: #d4a91c; /* Dark yellow for emphasis */
  --accent-50: #fffcf0; /* Ultra light yellow */
  --accent-100: #fff8e1; /* Light yellow background */
  --accent-200: #fff2cc; /* Lighter yellow for borders */

  /* Background Colors - Light Gray Background */
  --bg-primary: #fafafa; /* Light gray for main backgrounds */
  --bg-secondary: #f5f5f5; /* Slightly darker gray for secondary areas */
  --bg-tertiary: #f0f0f0; /* Medium gray for tertiary areas */
  --bg-quaternary: #e8e8e8; /* Darker gray for borders */
  --bg-overlay: rgba(250, 250, 250, 0.95); /* Glass overlay */
  --bg-glass: rgba(250, 250, 250, 0.1); /* Glass effect */
  --bg-gradient-primary: linear-gradient(
    135deg,
    #263ad1 0%,
    #4f5cd9 50%,
    #7b85e3 100%
  );
  --bg-gradient-secondary: linear-gradient(
    135deg,
    #484b6a 0%,
    #565a7d 50%,
    #6b7090 100%
  );
  --bg-gradient-accent: linear-gradient(
    135deg,
    #f9cc48 0%,
    #fbdc6e 50%,
    #fde894 100%
  );

  /* Text Colors - Your Custom Text Colors */
  --text-primary: #263ad1; /* Primary text - blue */
  --text-secondary: #484b6a; /* Secondary text - dark gray */
  --text-tertiary: #17cfe0; /* Tertiary text - cyan */
  --text-quaternary: #94a3b8; /* Quaternary text - light gray */
  --text-inverse: #ffffff; /* White text for dark backgrounds */
  --text-muted: #6b7280; /* Muted text for less important content */
  --text-placeholder: #9ca3af; /* Placeholder text */

  /* Border Colors - Subtle & Professional */
  --border-primary: #e8e8e8; /* Primary borders - light gray */
  --border-secondary: #d4d4d4; /* Secondary borders - medium gray */
  --border-tertiary: #b8b8b8; /* Tertiary borders - darker gray */
  --border-focus: #263ad1; /* Focus borders - blue */
  --border-glass: rgba(250, 250, 250, 0.2); /* Glass borders */

  /* Status Colors - Clear & Accessible */
  --success-color: #059669; /* Success green */
  --success-bg: rgba(5, 150, 105, 0.1); /* Light success background */
  --success-border: rgba(5, 150, 105, 0.2); /* Success border */
  --success-text: #065f46; /* Success text */

  --warning-color: #f9cc48; /* Warning yellow - using your accent color */
  --warning-bg: rgba(249, 204, 72, 0.1); /* Light warning background */
  --warning-border: rgba(249, 204, 72, 0.2); /* Warning border */
  --warning-text: #d4a91c; /* Warning text */

  --error-color: #dc2626; /* Error red */
  --error-bg: rgba(220, 38, 38, 0.1); /* Light error background */
  --error-border: rgba(220, 38, 38, 0.2); /* Error border */
  --error-text: #991b1b; /* Error text */

  --info-color: #17cfe0; /* Info cyan - using your text color */
  --info-bg: rgba(23, 207, 224, 0.1); /* Light info background */
  --info-border: rgba(23, 207, 224, 0.2); /* Info border */
  --info-text: #0891b2; /* Info text */

  /* Shadow Colors - Sophisticated Depth */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

  /* Glassmorphism Effects - Modern & Premium */
  --glass-backdrop: blur(20px);
  --glass-border: 1px solid rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(15, 23, 42, 0.37);

  /* Interactive States - Smooth & Professional */
  --hover-opacity: 0.8;
  --active-opacity: 0.6;
  --disabled-opacity: 0.4;
  --focus-ring: 0 0 0 2px rgba(59, 130, 246, 0.5);

  /* Spacing Scale - Consistent & Harmonious */
  --space-px: 1px;
  --space-0: 0px;
  --space-1: 0.25rem; /* 4px */
  --space-2: 0.5rem; /* 8px */
  --space-3: 0.75rem; /* 12px */
  --space-4: 1rem; /* 16px */
  --space-5: 1.25rem; /* 20px */
  --space-6: 1.5rem; /* 24px */
  --space-8: 2rem; /* 32px */
  --space-10: 2.5rem; /* 40px */
  --space-12: 3rem; /* 48px */
  --space-16: 4rem; /* 64px */
  --space-20: 5rem; /* 80px */
  --space-24: 6rem; /* 96px */

  /* Typography Scale - Readable & Professional */
  --font-family-sans: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, sans-serif;
  --font-family-mono: "JetBrains Mono", "Fira Code", Consolas, monospace;

  --font-size-xs: 0.75rem; /* 12px */
  --font-size-sm: 0.875rem; /* 14px */
  --font-size-base: 1rem; /* 16px */
  --font-size-lg: 1.125rem; /* 18px */
  --font-size-xl: 1.25rem; /* 20px */
  --font-size-2xl: 1.5rem; /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem; /* 36px */
  --font-size-5xl: 3rem; /* 48px */

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Line Heights - Perfect Reading Experience */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Border Radius - Modern & Consistent */
  --radius-none: 0px;
  --radius-sm: 0.125rem; /* 2px */
  --radius-base: 0.25rem; /* 4px */
  --radius-md: 0.375rem; /* 6px */
  --radius-lg: 0.5rem; /* 8px */
  --radius-xl: 0.75rem; /* 12px */
  --radius-2xl: 1rem; /* 16px */
  --radius-3xl: 1.5rem; /* 24px */
  --radius-full: 9999px; /* Full circle */

  /* Transitions - Smooth & Professional */
  --transition-none: none;
  --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-default: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 100ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== PREMIUM DARK THEME ===== */
[data-theme="dark"] {
  /* Primary Brand Colors - Adjusted for dark mode */
  --primary-color: #f8fafc; /* Light slate for primary text */
  --primary-hover: #e2e8f0; /* Medium slate for hover */
  --primary-light: #cbd5e1; /* Darker slate for subtle elements */
  --primary-dark: #ffffff; /* Pure white for emphasis */
  --primary-50: #0f172a; /* Dark slate for backgrounds */
  --primary-100: #1e293b; /* Medium dark slate */
  --primary-200: #334155; /* Lighter dark slate */

  /* Secondary Colors - Bright Blue for Dark Mode */
  --secondary-color: #60a5fa; /* Bright blue for secondary actions */
  --secondary-hover: #3b82f6; /* Deeper blue for hover */
  --secondary-light: #93c5fd; /* Light blue for accents */
  --secondary-dark: #2563eb; /* Deep blue for depth */
  --secondary-50: #1e3a8a; /* Dark blue background */
  --secondary-100: #1e40af; /* Medium dark blue */
  --secondary-200: #1d4ed8; /* Lighter dark blue */

  /* Accent Colors - Vibrant Green for Dark Mode */
  --accent-color: #34d399; /* Bright green for positive actions */
  --accent-hover: #10b981; /* Medium green for hover */
  --accent-light: #6ee7b7; /* Light green for highlights */
  --accent-dark: #059669; /* Deep green for emphasis */
  --accent-50: #065f46; /* Dark green background */
  --accent-100: #047857; /* Medium dark green */
  --accent-200: #059669; /* Lighter dark green */

  /* Background Colors - Professional Dark Mode with Better Contrast */
  --bg-primary: #1e293b; /* Dark slate - main background (lighter for better contrast) */
  --bg-secondary: #334155; /* Medium slate - secondary background */
  --bg-tertiary: #475569; /* Light slate - tertiary background */
  --bg-quaternary: #64748b; /* Lighter slate - quaternary background */
  --bg-overlay: rgba(30, 41, 59, 0.95); /* Professional dark glass overlay */
  --bg-glass: rgba(30, 41, 59, 0.1); /* Professional dark glass effect */
  --bg-gradient-primary: linear-gradient(
    135deg,
    #1e293b 0%,
    #334155 50%,
    #475569 100%
  );
  --bg-gradient-secondary: linear-gradient(
    135deg,
    #1e40af 0%,
    #3b82f6 50%,
    #60a5fa 100%
  );
  --bg-gradient-accent: linear-gradient(
    135deg,
    #059669 0%,
    #10b981 50%,
    #34d399 100%
  );

  /* Text Colors - Enhanced Contrast for Professional Look */
  --text-primary: #ffffff; /* Pure white - maximum contrast primary text */
  --text-secondary: #e2e8f0; /* Light slate - highly visible secondary text */
  --text-tertiary: #cbd5e1; /* Medium light slate - visible tertiary text */
  --text-quaternary: #94a3b8; /* Medium slate - quaternary text */
  --text-inverse: #1e293b; /* Dark text for light backgrounds */
  --text-muted: #94a3b8; /* Muted text - still readable */
  --text-placeholder: #64748b; /* Placeholder text - better contrast */

  /* Border Colors - More Visible for Professional Look */
  --border-primary: #475569; /* Primary borders - more visible */
  --border-secondary: #64748b; /* Secondary borders - lighter */
  --border-tertiary: #94a3b8; /* Tertiary borders - even lighter */
  --border-focus: #60a5fa; /* Focus borders - bright blue */
  --border-glass: rgba(255, 255, 255, 0.15); /* More visible glass borders */

  /* Status Colors - Vibrant & Accessible for Dark Mode */
  --success-color: #34d399; /* Bright success green */
  --success-bg: rgba(52, 211, 153, 0.1); /* Dark success background */
  --success-border: rgba(52, 211, 153, 0.2); /* Success border */
  --success-text: #6ee7b7; /* Light success text */

  --warning-color: #fbbf24; /* Bright warning yellow */
  --warning-bg: rgba(251, 191, 36, 0.1); /* Dark warning background */
  --warning-border: rgba(251, 191, 36, 0.2); /* Warning border */
  --warning-text: #fcd34d; /* Light warning text */

  --error-color: #f87171; /* Bright error red */
  --error-bg: rgba(248, 113, 113, 0.1); /* Dark error background */
  --error-border: rgba(248, 113, 113, 0.2); /* Error border */
  --error-text: #fca5a5; /* Light error text */

  --info-color: #60a5fa; /* Bright info blue */
  --info-bg: rgba(96, 165, 250, 0.1); /* Dark info background */
  --info-border: rgba(96, 165, 250, 0.2); /* Info border */
  --info-text: #93c5fd; /* Light info text */

  /* Shadow Colors - Subtle Dark Mode Shadows */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.5);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.5), 0 1px 2px -1px rgb(0 0 0 / 0.5);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.5), 0 2px 4px -2px rgb(0 0 0 / 0.5);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5),
    0 4px 6px -4px rgb(0 0 0 / 0.5);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.5),
    0 8px 10px -6px rgb(0 0 0 / 0.5);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.75);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.5);

  /* Glassmorphism Effects - Dark Mode Glass */
  --glass-backdrop: blur(20px);
  --glass-border: 1px solid rgba(248, 250, 252, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);

  /* Interactive States - Adjusted for Dark Mode */
  --focus-ring: 0 0 0 2px rgba(96, 165, 250, 0.5);
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-none: none;
    --transition-all: none;
    --transition-default: none;
    --transition-fast: none;
    --transition-slow: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --text-secondary: #000000;
    --bg-primary: #ffffff;
    --border-primary: #000000;
  }

  [data-theme="dark"] {
    --text-primary: #ffffff;
    --text-secondary: #ffffff;
    --bg-primary: #000000;
    --border-primary: #ffffff;
  }
}

/* ===== THEME UTILITIES ===== */
.theme-light {
  color-scheme: light;
}

.theme-dark {
  color-scheme: dark;
}

.theme-auto {
  color-scheme: light dark;
}

/* ===== LEGACY COMPATIBILITY ===== */
:root {
  /* Map old variables to new system */
  --bg-gradient-primary: var(--bg-gradient-primary);
  --white: var(--bg-primary);
  --text-light: var(--text-tertiary);
  --border-color: var(--border-primary);
}
