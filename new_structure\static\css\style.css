/* Kirima Primary School - Simple Clean Stylesheet */
/* Note: Theme variables are now managed by theme-manager.css */
:root {
  /* Legacy color mappings - these will use theme variables */
  --primary-color: var(--primary-color);
  --secondary-color: var(--secondary-color);
  --accent-color: var(--accent-color);
  --background-color: var(--bg-secondary);
  --white: var(--bg-primary);
  --text-dark: var(--text-primary);
  --text-light: var(--text-secondary);
  --border-color: var(--border-primary);
  --success-color: var(--success-color);
  --error-color: var(--error-color);
  --warning-color: var(--warning-color);

  /* Typography */
  --font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* Border radius */
  --border-radius: 4px;
  --border-radius-lg: 8px;

  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

  /* Transitions */
  --transition: all 0.3s ease;
}

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Body */
body {
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-gradient-primary);
  min-height: 100vh;
  padding-bottom: 60px;
}

/* Background */
.background {
  background: var(--bg-gradient-primary);
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Container */
.container {
  text-align: center;
  background: var(--bg-overlay);
  backdrop-filter: var(--glass-backdrop);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 90%;
  border: var(--glass-border);
  margin: 0 auto;
}

/* Dashboard container */
.container[style*="max-width: 90%"] {
  width: 100%;
  max-width: 90%;
  margin: 80px auto 60px;
  padding: var(--spacing-xl);
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
}

/* Management pages container - full width */
.manage-container {
  max-width: 95% !important;
  width: 95% !important;
  margin: 80px auto 60px !important;
  padding: var(--spacing-xl) !important;
  background: var(--background-color) !important;
  box-shadow: none !important;
  border: none !important;
}

/* Management page responsive design */
@media (max-width: 768px) {
  .manage-container {
    max-width: 98% !important;
    width: 98% !important;
    padding: var(--spacing-md) !important;
  }
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-bottom: var(--spacing-lg);
  font-weight: 600;
  line-height: 1.2;
  color: var(--text-dark);
}

h1 {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xl);
}

h2 {
  font-size: 2rem;
  color: var(--primary-color);
}

h3 {
  font-size: 1.5rem;
  color: var(--text-dark);
}

/* Links */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--secondary-color);
  text-decoration: underline;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  text-align: center;
  min-width: 120px;
}

.btn:hover {
  background: var(--secondary-color);
  text-decoration: none;
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: var(--white);
}

/* Forms */
.form-group {
  margin-bottom: var(--spacing-lg);
  text-align: left;
}

label {
  display: block;
  margin-bottom: var(--spacing-sm);
  color: var(--text-dark);
  font-weight: 500;
}

input[type="text"],
input[type="password"],
input[type="email"],
input[type="number"],
input[type="file"],
select,
textarea {
  width: 100%;
  padding: var(--spacing-md);
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  color: var(--text-dark);
  transition: var(--transition);
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(31, 125, 83, 0.1);
}

/* Navigation */
.navbar {
  background: var(--white);
  color: var(--text-dark);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: var(--shadow-md);
  border-bottom: 1px solid var(--border-color);
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  list-style: none;
  gap: var(--spacing-lg);
}

.nav-link {
  color: var(--text-dark);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
}

.nav-link:hover {
  color: var(--primary-color);
  background: rgba(31, 125, 83, 0.1);
  text-decoration: none;
}

.logout-btn {
  background: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  border: none;
  cursor: pointer;
  transition: var(--transition);
}

.logout-btn:hover {
  background: var(--secondary-color);
}

/* Tables */
.table-responsive {
  overflow-x: auto;
  margin: var(--spacing-lg) 0;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
}

table {
  width: 100%;
  border-collapse: collapse;
  background: var(--white);
}

th,
td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  background: var(--primary-color);
  color: var(--white);
  font-weight: 600;
}

tr:nth-child(even) {
  background: rgba(31, 125, 83, 0.05);
}

tr:hover {
  background: rgba(31, 125, 83, 0.1);
}

/* Dashboard Cards */
.dashboard-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
}

.card-header {
  color: var(--text-dark);
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  border-bottom: 2px solid var(--border-color);
  padding-bottom: var(--spacing-sm);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.management-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.filter-group {
  display: flex;
  gap: var(--spacing-sm);
}

.filter-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.9rem;
}

.filter-btn:hover {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.filter-btn.active {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.search-group {
  flex: 1;
  max-width: 300px;
}

.search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  background: var(--white);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(31, 125, 83, 0.1);
}

/* Footer */
footer {
  text-align: center;
  padding: var(--spacing-md);
  background: var(--white);
  color: var(--text-light);
  position: fixed;
  bottom: 0;
  width: 100%;
  border-top: 1px solid var(--border-color);
  box-shadow: var(--shadow-md);
}

/* Utility Classes */
.text-center {
  text-align: center;
}
.mb-4 {
  margin-bottom: var(--spacing-xl);
}
.mt-4 {
  margin-top: var(--spacing-xl);
}

/* Login Form */
.login-form {
  text-align: center;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.login-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

/* Management Options Grid */
.management-options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}

.management-option-card {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--white);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  text-decoration: none;
  color: var(--text-dark);
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.management-option-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  text-decoration: none;
  color: var(--text-dark);
}

.management-option-card:hover .option-arrow {
  transform: translateX(5px);
  color: var(--primary-color);
}

.option-icon {
  font-size: 2.5rem;
  margin-right: var(--spacing-lg);
  flex-shrink: 0;
  width: 60px;
  text-align: center;
}

.option-content {
  flex: 1;
}

.option-content h4 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-color);
}

.option-content p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-light);
  line-height: 1.4;
}

.option-arrow {
  font-size: 1.5rem;
  color: var(--text-light);
  transition: var(--transition);
  margin-left: var(--spacing-md);
}

.option-stats {
  margin-top: var(--spacing-sm);
}

.stat-badge {
  display: inline-block;
  background: rgba(31, 125, 83, 0.1);
  color: var(--primary-color);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid rgba(31, 125, 83, 0.2);
}

/* Category-based styling */
.management-option-card[data-category="core"] {
  border-left: 4px solid var(--primary-color);
}

.management-option-card[data-category="assignments"] {
  border-left: 4px solid var(--accent-color);
}

.management-option-card[data-category="structure"] {
  border-left: 4px solid var(--secondary-color);
}

.management-option-card[data-category="core"]:hover {
  border-left-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(31, 125, 83, 0.15);
}

.management-option-card[data-category="assignments"]:hover {
  border-left-color: var(--accent-color);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.15);
}

.management-option-card[data-category="structure"]:hover {
  border-left-color: var(--secondary-color);
  box-shadow: 0 4px 12px rgba(46, 139, 87, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .management-options-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .management-option-card {
    padding: var(--spacing-md);
  }

  .option-icon {
    font-size: 2rem;
    width: 50px;
    margin-right: var(--spacing-md);
  }

  .option-content h4 {
    font-size: 1rem;
  }

  .option-content p {
    font-size: 0.85rem;
  }
}
