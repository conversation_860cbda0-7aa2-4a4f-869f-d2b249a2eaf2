#!/usr/bin/env python3
"""
Simple script to add test assignments for <PERSON> using direct database operations.
"""
import os
import sys

# Set up the Flask app context
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ['FLASK_APP'] = 'app.py'

from flask import Flask
from extensions import db
from models.user import Teacher
from models.assignment import TeacherSubjectAssignment
from models.academic import Subject, Grade, Stream

def setup_kevin_data():
    """Set up test data for <PERSON>."""
    # Create Flask app
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:@localhost/hillview_db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # Initialize database
    db.init_app(app)
    
    with app.app_context():
        print("=== SETTING UP KEVIN'S DATA ===")
        
        # Find <PERSON>
        kevin = Teacher.query.filter_by(username='kevin').first()
        if not kevin:
            print("❌ Kevin not found!")
            return
        
        print(f"✅ Found Kevin: ID={kevin.id}, Name={kevin.name}")
        
        # Check existing assignments
        existing = TeacherSubjectAssignment.query.filter_by(teacher_id=kevin.id).count()
        print(f"📚 Existing assignments: {existing}")
        
        if existing > 0:
            print("✅ Kevin already has assignments!")
            # Show existing assignments
            assignments = TeacherSubjectAssignment.query.filter_by(teacher_id=kevin.id).all()
            for assignment in assignments:
                subject_name = assignment.subject.name if assignment.subject else "Unknown"
                grade_name = assignment.grade.name if assignment.grade else "Unknown"
                stream_name = assignment.stream.name if assignment.stream else "All"
                print(f"  - {subject_name} | Grade {grade_name} | Stream {stream_name}")
            return
        
        # Get available data
        subjects = Subject.query.limit(3).all()
        grades = Grade.query.limit(2).all()
        
        print(f"📖 Available subjects: {[s.name for s in subjects]}")
        print(f"🎓 Available grades: {[g.name for g in grades]}")
        
        # Add assignments
        assignments_added = 0
        
        for grade in grades:
            # Get streams for this grade
            streams = Stream.query.filter_by(grade_id=grade.id).limit(1).all()
            
            for stream in streams:
                for subject in subjects[:2]:  # Only first 2 subjects per grade
                    # Check if assignment already exists
                    existing_assignment = TeacherSubjectAssignment.query.filter_by(
                        teacher_id=kevin.id,
                        subject_id=subject.id,
                        grade_id=grade.id,
                        stream_id=stream.id
                    ).first()
                    
                    if not existing_assignment:
                        assignment = TeacherSubjectAssignment(
                            teacher_id=kevin.id,
                            subject_id=subject.id,
                            grade_id=grade.id,
                            stream_id=stream.id,
                            is_class_teacher=False
                        )
                        db.session.add(assignment)
                        assignments_added += 1
                        print(f"➕ Added: {subject.name} | Grade {grade.name} | Stream {stream.name}")
        
        try:
            db.session.commit()
            print(f"✅ Successfully added {assignments_added} assignments!")
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error: {e}")
        
        # Verify final state
        final_count = TeacherSubjectAssignment.query.filter_by(teacher_id=kevin.id).count()
        print(f"📊 Final assignment count: {final_count}")

if __name__ == "__main__":
    setup_kevin_data()
