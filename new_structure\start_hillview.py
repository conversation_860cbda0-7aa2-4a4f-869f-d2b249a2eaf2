#!/usr/bin/env python3
"""
Hillview School Management System Startup Script
This script ensures proper virtual environment and starts the application.
"""
import sys
import os
import subprocess

def check_virtual_environment():
    """Check if we're running in the virtual environment."""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    venv_python = os.path.join(current_dir, 'venv', 'Scripts', 'python.exe')

    if os.path.exists(venv_python):
        if sys.executable != venv_python:
            print("🔄 Switching to virtual environment...")
            # Re-run this script with the virtual environment Python
            subprocess.run([venv_python, __file__] + sys.argv[1:])
            return False
        else:
            print("✅ Running in virtual environment")
            return True
    else:
        print("❌ Virtual environment not found. Please run: python -m venv venv")
        return False

# Check and switch to virtual environment if needed
if not check_virtual_environment():
    sys.exit(0)

# Add current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Set environment variables
os.environ['PYTHONPATH'] = current_dir
os.environ['FLASK_APP'] = 'new_structure'
os.environ['FLASK_ENV'] = 'development'

try:
    print("🚀 Starting Hillview School Management System...")
    print(f"📁 Working Directory: {current_dir}")
    print(f"🐍 Python Path: {sys.path[0]}")

    # Import and run the application
    from new_structure import create_app

    app = create_app()

    print("✅ Application initialized successfully!")
    print("🌐 Starting server on http://localhost:3000")
    print("📱 Network access: http://0.0.0.0:3000")
    print("🔧 Debug mode: ON")
    print("=" * 50)

    # Run the application
    app.run(
        host='0.0.0.0',
        port=3000,
        debug=True,
        use_reloader=False  # Disable reloader to avoid issues
    )
    
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("💡 Try installing missing packages:")
    print("   pip install Flask-WTF WTForms Flask-SQLAlchemy")
    sys.exit(1)
    
except Exception as e:
    print(f"❌ Error starting application: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
