/* ===== PREMIUM MANAGEMENT CARDS STYLING ===== */
/* Ultra-modern, premium design for management cards */

/* Enhanced Quick Action Cards with Premium Look */
.quick-action-card {
  position: relative;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0.85)
  );
  backdrop-filter: blur(25px);
  border-radius: 24px;
  padding: 32px 24px;
  text-decoration: none;
  color: inherit;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08), 0 4px 16px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  overflow: hidden;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Premium Gradient Overlay */
.quick-action-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 123, 255, 0.03) 0%,
    rgba(108, 99, 255, 0.03) 50%,
    rgba(255, 107, 107, 0.03) 100%
  );
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

/* Premium Hover Effects */
.quick-action-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  text-decoration: none;
  color: inherit;
  border-color: rgba(255, 255, 255, 0.6);
}

.quick-action-card:hover::before {
  opacity: 1;
}

/* Premium Icon Styling */
.quick-action-icon {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  background: linear-gradient(
    135deg,
    rgba(0, 123, 255, 0.1) 0%,
    rgba(108, 99, 255, 0.1) 50%,
    rgba(255, 107, 107, 0.1) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.quick-action-icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.quick-action-card:hover .quick-action-icon::before {
  opacity: 1;
}

.quick-action-icon i {
  font-size: 32px;
  background: linear-gradient(135deg, #007bff 0%, #6c63ff 50%, #ff6b6b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 1;
}

/* Premium Typography */
.quick-action-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: #1a1a1a;
  line-height: 1.3;
  letter-spacing: -0.02em;
}

.quick-action-desc {
  font-size: 0.9rem;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 16px;
  font-weight: 400;
}

/* Premium Badge Styling */
.modern-badge {
  padding: 8px 16px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  align-self: flex-start;
  margin-top: auto;
}

.badge-success {
  background: linear-gradient(
    135deg,
    rgba(34, 197, 94, 0.15),
    rgba(34, 197, 94, 0.05)
  );
  color: #059669;
  border-color: rgba(34, 197, 94, 0.2);
}

.badge-warning {
  background: linear-gradient(
    135deg,
    rgba(245, 158, 11, 0.15),
    rgba(245, 158, 11, 0.05)
  );
  color: #d97706;
  border-color: rgba(245, 158, 11, 0.2);
}

.badge-info {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.15),
    rgba(59, 130, 246, 0.05)
  );
  color: #2563eb;
  border-color: rgba(59, 130, 246, 0.2);
}

.badge-primary {
  background: linear-gradient(
    135deg,
    rgba(139, 92, 246, 0.15),
    rgba(139, 92, 246, 0.05)
  );
  color: #7c3aed;
  border-color: rgba(139, 92, 246, 0.2);
}

/* Premium Grid Layout */
.modern-grid.grid-cols-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-bottom: 32px;
}

/* Premium Animation for Card Entrance */
@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.quick-action-card {
  animation: cardSlideIn 0.6s ease-out;
}

.quick-action-card:nth-child(1) {
  animation-delay: 0.1s;
}
.quick-action-card:nth-child(2) {
  animation-delay: 0.2s;
}
.quick-action-card:nth-child(3) {
  animation-delay: 0.3s;
}
.quick-action-card:nth-child(4) {
  animation-delay: 0.4s;
}
.quick-action-card:nth-child(5) {
  animation-delay: 0.5s;
}
.quick-action-card:nth-child(6) {
  animation-delay: 0.6s;
}

/* Premium Focus States for Accessibility */
.quick-action-card:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), 0 20px 60px rgba(0, 0, 0, 0.15),
    0 8px 32px rgba(0, 0, 0, 0.08);
  transform: translateY(-8px);
}

/* Premium Mobile Responsiveness */
@media (max-width: 768px) {
  .modern-grid.grid-cols-3 {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .quick-action-card {
    min-height: 160px;
    padding: 24px 20px;
  }

  .quick-action-icon {
    width: 64px;
    height: 64px;
    margin-bottom: 16px;
  }

  .quick-action-icon i {
    font-size: 24px;
  }

  .quick-action-title {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .quick-action-card {
    min-height: 140px;
    padding: 20px 16px;
  }

  .quick-action-icon {
    width: 56px;
    height: 56px;
    margin-bottom: 12px;
  }

  .quick-action-icon i {
    font-size: 20px;
  }
}

/* Premium Indicator Styling */
.premium-indicator {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  z-index: 2;
}

.premium-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: linear-gradient(135deg, #00d4aa, #00a3ff);
  animation: premiumPulse 2s infinite;
}

.premium-label {
  color: #374151;
  font-size: 0.65rem;
}

@keyframes premiumPulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

/* Card Content Wrapper */
.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Enhanced Card Structure */
.premium-card {
  position: relative;
}

.premium-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  border-radius: 24px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.premium-card:hover::after {
  opacity: 1;
}

/* Premium Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .quick-action-card {
    background: linear-gradient(
      145deg,
      rgba(30, 30, 30, 0.95),
      rgba(20, 20, 20, 0.85)
    );
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 4px 16px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .quick-action-title {
    color: #f9fafb;
  }

  .quick-action-desc {
    color: #9ca3af;
  }

  .quick-action-card:hover {
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4), 0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .premium-indicator {
    background: rgba(30, 30, 30, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .premium-label {
    color: #d1d5db;
  }
}
