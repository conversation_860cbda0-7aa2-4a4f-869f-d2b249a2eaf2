{% extends "base.html" %}

{% block title %}Subject Analytics - Hillview School{% endblock %}

{% block content %}
<div class="modern-container">
    <!-- Header -->
    <header class="modern-header fade-in">
        <div class="header-content">
            <div>
                <h1 class="header-title">
                    <i class="fas fa-chart-line"></i>
                    Subject Analytics
                </h1>
                <p class="header-subtitle">
                    View performance analysis for subjects you teach
                </p>
            </div>
            <div class="header-actions">
                <a href="{{ url_for('classteacher.dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </header>

    <!-- Assignment Selection -->
    <div class="dashboard-card">
        <div class="card-header">
            <h2>Select Subject Assignment</h2>
            <p>Choose the subject and class for which you want to view analytics</p>
        </div>

        <div class="assignments-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
            {% for assignment in assignments %}
            <div class="assignment-card" style="
                background: white;
                border: 2px solid {% if assignment.subject_id == subject_id and assignment.grade_id == grade_id and assignment.stream_id == stream_id %}#28a745{% else %}#e0e0e0{% endif %};
                border-radius: 12px;
                padding: 20px;
                cursor: pointer;
                transition: all 0.2s ease;
            " onclick="selectAssignment({{ assignment.subject_id }}, {{ assignment.grade_id }}, {{ assignment.stream_id or 'null' }})">
                
                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                    <div style="
                        width: 40px;
                        height: 40px;
                        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                        border-radius: 8px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 12px;
                    ">
                        <i class="fas fa-chart-bar" style="color: white; font-size: 18px;"></i>
                    </div>
                    <div>
                        <h4 style="margin: 0; color: #333; font-size: 16px;">{{ assignment.subject_name }}</h4>
                        <p style="margin: 2px 0 0 0; color: #666; font-size: 12px;">{{ assignment.education_level }}</p>
                    </div>
                </div>

                <div>
                    <p style="margin: 0; color: #555; font-size: 14px;">
                        <strong>Grade {{ assignment.grade_name }}</strong>
                        {% if assignment.stream_name and assignment.stream_name != 'No Stream' %}
                        - {{ assignment.stream_name }}
                        {% endif %}
                    </p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Analytics Dashboard (shown when assignment is selected) -->
    {% if selected_assignment and analytics_data %}
    <div class="dashboard-card">
        <div class="card-header">
            <h2>Subject Performance Analytics</h2>
            <p>
                Subject: <strong>{{ analytics_data.subject.name }}</strong> | 
                Class: <strong>Grade {{ analytics_data.grade.name }}{% if analytics_data.stream %} - {{ analytics_data.stream.name }}{% endif %}</strong>
            </p>
        </div>

        <!-- Analytics Filters -->
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
            <div style="display: grid; grid-template-columns: 1fr 1fr auto; gap: 15px; align-items: end;">
                <div class="form-group">
                    <label for="analytics_term">Term</label>
                    <select id="analytics_term">
                        <option value="">All Terms</option>
                        {% for term in terms %}
                        <option value="{{ term.id }}">{{ term.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group">
                    <label for="analytics_assessment">Assessment Type</label>
                    <select id="analytics_assessment">
                        <option value="">All Assessments</option>
                        {% for assessment_type in assessment_types %}
                        <option value="{{ assessment_type.id }}">{{ assessment_type.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <button type="button" class="btn btn-primary" onclick="loadAnalytics()">
                    <i class="fas fa-chart-line"></i>
                    Generate Analytics
                </button>
            </div>
        </div>

        <!-- Analytics Content -->
        <div id="analytics-content">
            <div class="analytics-placeholder" style="text-align: center; padding: 60px; color: #6c757d;">
                <i class="fas fa-chart-line" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>Select Term and Assessment Type</h3>
                <p>Choose the term and assessment type above to view detailed analytics for this subject.</p>
            </div>
        </div>

        <!-- Quick Stats Cards -->
        <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 30px;">
            <div class="stat-card" style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 12px;
                text-align: center;
            ">
                <i class="fas fa-users" style="font-size: 24px; margin-bottom: 10px;"></i>
                <h3 style="margin: 0; font-size: 24px;">--</h3>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">Total Students</p>
            </div>

            <div class="stat-card" style="
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                color: white;
                padding: 20px;
                border-radius: 12px;
                text-align: center;
            ">
                <i class="fas fa-chart-line" style="font-size: 24px; margin-bottom: 10px;"></i>
                <h3 style="margin: 0; font-size: 24px;">--%</h3>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">Class Average</p>
            </div>

            <div class="stat-card" style="
                background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
                color: white;
                padding: 20px;
                border-radius: 12px;
                text-align: center;
            ">
                <i class="fas fa-trophy" style="font-size: 24px; margin-bottom: 10px;"></i>
                <h3 style="margin: 0; font-size: 24px;">--%</h3>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">Highest Score</p>
            </div>

            <div class="stat-card" style="
                background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                color: white;
                padding: 20px;
                border-radius: 12px;
                text-align: center;
            ">
                <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i>
                <h3 style="margin: 0; font-size: 24px;">--</h3>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">Below Average</p>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
function selectAssignment(subjectId, gradeId, streamId) {
    const url = new URL(window.location.href);
    url.searchParams.set('subject_id', subjectId);
    url.searchParams.set('grade_id', gradeId);
    if (streamId) {
        url.searchParams.set('stream_id', streamId);
    } else {
        url.searchParams.delete('stream_id');
    }
    window.location.href = url.toString();
}

function loadAnalytics() {
    const termId = document.getElementById('analytics_term').value;
    const assessmentId = document.getElementById('analytics_assessment').value;
    const subjectId = {{ subject_id or 'null' }};
    const gradeId = {{ grade_id or 'null' }};
    const streamId = {{ stream_id or 'null' }};

    if (!termId || !assessmentId) {
        alert('Please select both term and assessment type');
        return;
    }

    const analyticsContent = document.getElementById('analytics-content');
    analyticsContent.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: #007bff;"></i>
            <p style="margin-top: 10px; color: #666;">Loading analytics...</p>
        </div>
    `;

    // Here you would make an AJAX call to load the actual analytics data
    // For now, we'll show a placeholder
    setTimeout(() => {
        analyticsContent.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #28a745;">
                <i class="fas fa-check-circle" style="font-size: 48px; margin-bottom: 20px;"></i>
                <h3>Analytics Feature Coming Soon</h3>
                <p>Subject-specific analytics will be available in the next update.</p>
            </div>
        `;
    }, 1500);
}
</script>
{% endblock %}
