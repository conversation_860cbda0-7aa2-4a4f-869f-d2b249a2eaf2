/* ===================================================================
   HILLVIEW SCHOOL MANAGEMENT SYSTEM - DARK MODE TOGGLE
   Professional dark mode implementation with smooth transitions
   ================================================================== */

/* ===== THEME TOGGLE BUTTON ===== */
.theme-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-full);
  padding: var(--space-3);
  cursor: pointer;
  transition: var(--transition-all);
  box-shadow: var(--shadow-sm);
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Hide theme toggle on desktop/laptop views */
@media (min-width: 1024px) {
  .theme-toggle {
    display: none !important;
  }
}

/* Show theme toggle only on tablets and mobile */
@media (max-width: 1023px) {
  .theme-toggle {
    display: flex !important;
  }
}

.theme-toggle:hover {
  box-shadow: var(--shadow-md);
  transform: scale(1.05);
}

.theme-toggle i {
  transition: var(--transition-all);
}

/* ===== THEME TRANSITION ===== */
body,
.card,
.navbar,
.table,
.form-control,
.form-select,
.modal-content,
.btn {
  transition: background-color 0.3s ease, color 0.3s ease,
    border-color 0.3s ease !important;
}

/* Keep icons stable during transitions */
i,
.fa,
.fas,
.far,
.fal,
.fab {
  transition: none !important;
}

/* ===== DARK MODE SPECIFIC OVERRIDES ===== */
[data-theme="dark"] .navbar {
  background: var(--bg-gradient-primary) !important;
  border-bottom-color: var(--border-primary) !important;
}

[data-theme="dark"] .card,
[data-theme="dark"] .modern-card,
[data-theme="dark"] .stat-card {
  background: var(--bg-secondary) !important;
  border-color: var(--border-primary) !important;
}

[data-theme="dark"] .table {
  background: var(--bg-secondary) !important;
}

[data-theme="dark"] .table thead th {
  background: var(--bg-tertiary) !important;
  border-bottom-color: var(--border-primary) !important;
}

[data-theme="dark"] .table tbody tr:hover {
  background: var(--bg-tertiary) !important;
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
  background: var(--bg-secondary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .modal {
  background: rgba(0, 0, 0, 0.7) !important;
}

[data-theme="dark"] .modal-content {
  background: var(--bg-secondary) !important;
  border-color: var(--border-primary) !important;
}

/* ===== DARK MODE TEXT AND ICON VISIBILITY ===== */
[data-theme="dark"] {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .text-dark,
[data-theme="dark"] .text-muted,
[data-theme="dark"] .text-secondary {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .nav-link,
[data-theme="dark"] .navbar-nav .nav-link {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .nav-link:hover,
[data-theme="dark"] .navbar-nav .nav-link:hover {
  color: var(--primary-color) !important;
}

/* Dark mode icon visibility */
[data-theme="dark"] i,
[data-theme="dark"] .fa,
[data-theme="dark"] .fas,
[data-theme="dark"] .far,
[data-theme="dark"] .fal,
[data-theme="dark"] .fab {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .btn i,
[data-theme="dark"] .nav-link i {
  color: inherit !important;
}

/* Dark mode button improvements */
[data-theme="dark"] .btn-primary {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: var(--text-inverse) !important;
}

[data-theme="dark"] .btn-secondary {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .btn-outline-primary {
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

[data-theme="dark"] .btn-outline-primary:hover {
  background: var(--primary-color) !important;
  color: var(--text-inverse) !important;
}

/* Dark mode card improvements */
[data-theme="dark"] .card-header {
  background: var(--bg-tertiary) !important;
  border-bottom-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .card-title,
[data-theme="dark"] .card-text {
  color: var(--text-primary) !important;
}

/* Dark mode alert improvements */
[data-theme="dark"] .alert {
  border-color: var(--border-primary) !important;
}

[data-theme="dark"] .alert-primary {
  background: var(--primary-bg) !important;
  color: var(--text-primary) !important;
  border-color: var(--primary-border) !important;
}

[data-theme="dark"] .alert-success {
  background: var(--success-bg) !important;
  color: var(--success-text) !important;
  border-color: var(--success-border) !important;
}

[data-theme="dark"] .alert-warning {
  background: var(--warning-bg) !important;
  color: var(--warning-text) !important;
  border-color: var(--warning-border) !important;
}

[data-theme="dark"] .alert-danger {
  background: var(--error-bg) !important;
  color: var(--error-text) !important;
  border-color: var(--error-border) !important;
}

[data-theme="dark"] .alert-success {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
  color: var(--success-text) !important;
}

[data-theme="dark"] .alert-warning {
  background: var(--warning-bg) !important;
  border-color: var(--warning-border) !important;
  color: var(--warning-text) !important;
}

[data-theme="dark"] .alert-danger {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
  color: var(--error-text) !important;
}

[data-theme="dark"] .alert-info {
  background: var(--info-bg) !important;
  border-color: var(--info-border) !important;
  color: var(--info-text) !important;
}

/* ===== LOADING ANIMATION ===== */
.theme-switching {
  pointer-events: none;
  opacity: 0.8;
}

.theme-switching * {
  transition: none !important;
}

/* ===== MOBILE ADJUSTMENTS ===== */
@media (max-width: 768px) {
  .theme-toggle {
    top: 15px;
    right: 15px;
    width: 44px;
    height: 44px;
    font-size: var(--font-size-base);
  }
}

/* ===== SYSTEM PREFERENCE DETECTION ===== */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]) {
    /* Auto dark mode - inherit from system preference */
    color-scheme: dark;
  }
}

/* ===== ACCESSIBILITY ===== */
.theme-toggle:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* ===== THEME INDICATOR ===== */
.theme-indicator {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
  z-index: 999;
  opacity: 0;
  transform: translateY(20px);
  transition: var(--transition-all);
  pointer-events: none;
}

.theme-indicator.show {
  opacity: 1;
  transform: translateY(0);
}

/* ===== SMOOTH SCROLLBAR FOR DARK MODE ===== */
[data-theme="dark"] ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: var(--border-tertiary);
  border-radius: var(--radius-full);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* ===== LIGHT MODE SCROLLBAR ===== */
[data-theme="light"] ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

[data-theme="light"] ::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

[data-theme="light"] ::-webkit-scrollbar-thumb {
  background: var(--border-tertiary);
  border-radius: var(--radius-full);
}

[data-theme="light"] ::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* ===== ANIMATION KEYFRAMES ===== */
@keyframes theme-switch {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.theme-switching {
  animation: theme-switch 0.3s ease-in-out;
}

/* ===== PRINT MODE OVERRIDE ===== */
@media print {
  .theme-toggle,
  .theme-indicator {
    display: none !important;
  }

  * {
    background: white !important;
    color: black !important;
  }
}
