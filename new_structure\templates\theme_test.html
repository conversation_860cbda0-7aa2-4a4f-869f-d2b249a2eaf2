{% extends "modern_base.html" %}

{% block title %}Theme Test - {{ super() }}{% endblock %}

{% block page_title %}Theme Test{% endblock %}

{% block header_icon %}<i class="fas fa-palette"></i>{% endblock %}
{% block header_title %}Light/Dark Mode Test{% endblock %}
{% block header_subtitle %}Testing the theme switching functionality{% endblock %}

{% block content %}
<div class="modern-grid grid-cols-1 gap-6">
  <!-- Theme Demo Card -->
  <div class="modern-card">
    <div class="card-header">
      <h2 class="card-title">
        <i class="fas fa-swatchbook"></i>
        Theme Demonstration
      </h2>
    </div>
    
    <div class="card-content">
      <p class="text-secondary mb-4">
        This page demonstrates the light and dark theme switching functionality. 
        Use the theme toggle button in the header to switch between modes.
      </p>
      
      <div class="modern-grid grid-cols-2 gap-4 mb-6">
        <div class="demo-section">
          <h3 class="text-primary mb-3">Colors</h3>
          <div class="color-demo">
            <div class="color-box bg-primary"></div>
            <div class="color-box bg-secondary"></div>
            <div class="color-box bg-tertiary"></div>
            <div class="color-box bg-quaternary"></div>
          </div>
        </div>
        
        <div class="demo-section">
          <h3 class="text-primary mb-3">Text Colors</h3>
          <p class="text-primary">Primary Text</p>
          <p class="text-secondary">Secondary Text</p>
          <p class="text-tertiary">Tertiary Text</p>
          <p class="text-muted">Muted Text</p>
        </div>
      </div>
      
      <div class="button-demo mb-6">
        <h3 class="text-primary mb-3">Buttons</h3>
        <div class="flex gap-3 flex-wrap">
          <button class="modern-btn btn-primary">Primary</button>
          <button class="modern-btn btn-secondary">Secondary</button>
          <button class="modern-btn btn-success">Success</button>
          <button class="modern-btn btn-warning">Warning</button>
          <button class="modern-btn btn-danger">Danger</button>
          <button class="modern-btn btn-outline">Outline</button>
        </div>
      </div>
      
      <div class="form-demo">
        <h3 class="text-primary mb-3">Form Elements</h3>
        <form class="modern-form">
          <div class="form-group">
            <label class="form-label">Sample Input</label>
            <input type="text" class="form-input" placeholder="Enter text here...">
          </div>
          
          <div class="form-group">
            <label class="form-label">Sample Select</label>
            <select class="form-select">
              <option>Option 1</option>
              <option>Option 2</option>
              <option>Option 3</option>
            </select>
          </div>
          
          <div class="form-group">
            <label class="form-label">Sample Textarea</label>
            <textarea class="form-textarea" rows="3" placeholder="Enter description..."></textarea>
          </div>
        </form>
      </div>
    </div>
  </div>
  
  <!-- Status Messages Demo -->
  <div class="modern-card">
    <div class="card-header">
      <h2 class="card-title">
        <i class="fas fa-bell"></i>
        Status Messages
      </h2>
    </div>
    
    <div class="card-content">
      <div class="flash-message flash-success mb-3">
        <i class="fas fa-check-circle"></i>
        This is a success message
      </div>
      
      <div class="flash-message flash-warning mb-3">
        <i class="fas fa-exclamation-triangle"></i>
        This is a warning message
      </div>
      
      <div class="flash-message flash-error mb-3">
        <i class="fas fa-times-circle"></i>
        This is an error message
      </div>
      
      <div class="flash-message flash-info">
        <i class="fas fa-info-circle"></i>
        This is an info message
      </div>
    </div>
  </div>
</div>

<style>
/* Demo-specific styles */
.demo-section h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.color-demo {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.color-box {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: 2px solid var(--border-primary);
}

.bg-primary { background: var(--bg-primary); }
.bg-secondary { background: var(--bg-secondary); }
.bg-tertiary { background: var(--bg-tertiary); }
.bg-quaternary { background: var(--bg-quaternary); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-muted { color: var(--text-muted); }

.button-demo .flex {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.form-demo .modern-form {
  max-width: 400px;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--border-primary);
  border-radius: 8px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: inherit;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--border-focus);
}

.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
</style>
{% endblock %}
