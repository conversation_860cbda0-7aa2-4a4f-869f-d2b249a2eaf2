/* =============================================================================
   MOBILE NAVIGATION FIXES - COMPREHENSIVE SOLUTION
   Fixes overlapping content, floating elements, and navigation issues
   ============================================================================= */

/* Reset and base mobile styles */
@media (max-width: 768px) {
  /* Fix mobile navigation toggle */
  .mobile-nav-toggle {
    display: block !important;
    position: relative;
    z-index: 1002;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    padding: 8px 12px;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 40px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .mobile-nav-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
  }

  /* Main navigation - hidden by default on mobile */
  #classteacherNav {
    display: none !important;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(44, 62, 80, 0.98);
    z-index: 1001;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 60px 20px 20px;
    gap: 0;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    backdrop-filter: blur(10px);
  }

  /* Mobile navigation when open */
  #classteacherNav.mobile-nav-open {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* Navigation links in mobile */
  #classteacherNav li {
    width: 100%;
    max-width: 300px;
    margin: 8px 0;
    list-style: none;
  }

  #classteacherNav .nav-link,
  #classteacherNav .logout-btn {
    display: flex !important;
    align-items: center;
    justify-content: flex-start;
    gap: 12px;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    text-decoration: none;
    font-size: 1rem;
    font-weight: 500;
    width: 100%;
    box-sizing: border-box;
    transition: all 0.3s ease;
    min-height: 50px;
  }

  #classteacherNav .nav-link:hover,
  #classteacherNav .logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  #classteacherNav .nav-link i,
  #classteacherNav .logout-btn i {
    font-size: 1.2rem;
    min-width: 20px;
    text-align: center;
  }

  /* Fix main content layout on mobile */
  .modern-container {
    padding: 1rem !important;
    margin: 0 !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Fix floating elements */
  .stat-card,
  .modern-card,
  .card {
    margin: 0 0 1rem 0 !important;
    padding: 1rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
    position: relative !important;
    float: none !important;
  }

  /* Fix stat numbers floating */
  .stat-number,
  .stat-value {
    position: relative !important;
    float: none !important;
    display: block !important;
    text-align: center !important;
    margin: 0 !important;
  }

  /* Fix grid layouts */
  .modern-grid,
  .dashboard-grid,
  .stats-grid {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
    width: 100% !important;
  }

  /* Fix tables */
  .table-wrapper {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
    width: 100% !important;
    margin: 1rem 0 !important;
  }

  .table-wrapper table {
    min-width: 600px !important;
    font-size: 14px !important;
  }

  /* Fix forms */
  .modern-form {
    padding: 1rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  .form-control,
  .form-select {
    width: 100% !important;
    min-height: 44px !important;
    font-size: 16px !important;
    padding: 0.75rem !important;
    box-sizing: border-box !important;
  }

  .modern-btn {
    width: 100% !important;
    min-height: 44px !important;
    padding: 0.75rem !important;
    font-size: 16px !important;
    margin: 0.5rem 0 !important;
    box-sizing: border-box !important;
  }

  /* Fix navbar on mobile */
  .navbar {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 1rem !important;
    flex-wrap: nowrap !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 1000 !important;
  }

  .navbar-brand {
    font-size: 1rem !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 60% !important;
  }

  /* Fix content scrolling when nav is open */
  body.nav-open {
    overflow: hidden !important;
  }

  /* Fix overlapping content */
  .main-content {
    position: relative !important;
    z-index: 1 !important;
  }

  /* Fix floating action buttons */
  .fab,
  .floating-btn {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    z-index: 999 !important;
  }

  /* Ensure FAB system works properly on mobile */
  .floating-action-system {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    z-index: 1001 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-end !important;
    gap: 10px !important;
  }

  /* FAB menu should be hidden by default */
  .fab-menu {
    display: none !important;
  }

  /* FAB menu visible only when active */
  .fab-menu.active {
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
    align-items: flex-end !important;
  }

  /* FAB items styling for mobile */
  .fab-item {
    padding: 10px 14px !important;
    font-size: 13px !important;
    border-radius: 50px !important;
    white-space: nowrap !important;
    background: rgba(255, 255, 255, 0.95) !important;
    color: #333 !important;
    text-decoration: none !important;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15) !important;
    transition: all 0.3s ease !important;
  }

  .fab-item:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
  }

  /* Main FAB button */
  .fab-main {
    width: 50px !important;
    height: 50px !important;
    border-radius: 50% !important;
    background: var(--bg-gradient-primary) !important;
    border: none !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15) !important;
    transition: all 0.3s ease !important;
    z-index: 1002 !important;
  }

  .fab-main:hover {
    transform: scale(1.1) !important;
  }

  .fab-main i {
    color: white !important;
    font-size: 18px !important;
  }

  /* Fix alert messages */
  .alert {
    margin: 1rem 0 !important;
    padding: 1rem !important;
    border-radius: 8px !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Fix assignment cards */
  .assignment-card,
  .quick-action-card {
    width: 100% !important;
    margin: 0 0 1rem 0 !important;
    padding: 1rem !important;
    box-sizing: border-box !important;
    display: block !important;
  }

  /* Fix text wrapping */
  .text-wrap {
    word-wrap: break-word !important;
    word-break: break-word !important;
    hyphens: auto !important;
  }

  /* Fix modal dialogs */
  .modal {
    padding: 1rem !important;
  }

  .modal-dialog {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
  }

  /* Fix search and filter inputs */
  .search-container,
  .filter-container {
    width: 100% !important;
    margin: 0 0 1rem 0 !important;
  }

  /* Fix pagination */
  .pagination {
    justify-content: center !important;
    flex-wrap: wrap !important;
  }

  .pagination .page-item {
    margin: 2px !important;
  }
}

/* Ensure proper icon display */
.mobile-nav-toggle i {
  font-family: "Font Awesome 6 Free" !important;
  font-weight: 900 !important;
  font-style: normal !important;
  display: inline-block !important;
  text-rendering: auto !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* Fallback for hamburger icon */
.mobile-nav-toggle i.fa-bars::before {
  content: "\f0c9" !important;
}

.mobile-nav-toggle i.fa-times::before {
  content: "\f00d" !important;
}

/* If Font Awesome fails, use Unicode */
.no-fontawesome .mobile-nav-toggle i.fa-bars::before {
  content: "☰" !important;
  font-family: Arial, sans-serif !important;
}

.no-fontawesome .mobile-nav-toggle i.fa-times::before {
  content: "✕" !important;
  font-family: Arial, sans-serif !important;
}

/* =============================================================================
   MOBILE FOOTER FIXES
   ============================================================================= */

/* Fix misplaced footer on mobile */
@media (max-width: 768px) {
  footer,
  .site-footer {
    position: relative !important;
    bottom: auto !important;
    width: 100% !important;
    margin-top: 2rem !important;
    padding: 1rem !important;
    background: var(--white) !important;
    color: var(--text-light) !important;
    border-top: 1px solid var(--border-color) !important;
    box-shadow: none !important;
    text-align: center !important;
    font-size: 0.875rem !important;
    z-index: 1 !important;
  }

  footer p,
  .site-footer p {
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Ensure main content has proper spacing for footer */
  .main-content-container,
  .container-fluid,
  .dashboard-content {
    padding-bottom: 4rem !important;
  }

  /* Add proper bottom margin to last content elements */
  .container-fluid:last-child,
  .tab-content-container:last-child,
  .dashboard-content:last-child {
    margin-bottom: 2rem !important;
  }

  /* Ensure body has proper padding for footer */
  body {
    padding-bottom: 0 !important;
  }

  /* Make sure the main wrapper has proper spacing */
  .main-wrapper,
  .content-wrapper {
    padding-bottom: 3rem !important;
  }
}
