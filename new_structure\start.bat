@echo off
echo 🚀 Starting Hillview School Management System...
echo.

REM Check if virtual environment exists
if not exist "venv\Scripts\python.exe" (
    echo ❌ Virtual environment not found!
    echo Creating virtual environment...
    python -m venv venv
    echo.
    echo Installing required packages...
    venv\Scripts\pip.exe install Flask-WTF==1.1.1 WTForms==3.0.1 Flask-SQLAlchemy==3.0.5 Flask==2.3.3 Werkzeug==2.3.7 PyMySQL cryptography mysql-connector-python reportlab Pillow openpyxl
    echo.
)

echo ✅ Using virtual environment
echo 📁 Working Directory: %CD%
echo.

REM Run the application using virtual environment Python
venv\Scripts\python.exe run.py

pause
