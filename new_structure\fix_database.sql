-- Database fix for class_teacher_permissions table
-- Run this SQL script to fix the missing revoked_at column

USE hillview_db;

-- Add the missing revoked_at column to class_teacher_permissions table
ALTER TABLE class_teacher_permissions 
ADD COLUMN IF NOT EXISTS revoked_at DATETIME NULL 
AFTER granted_at;

-- Add an index for better performance
CREATE INDEX IF NOT EXISTS idx_class_teacher_permissions_revoked_at 
ON class_teacher_permissions(revoked_at);

-- Update any existing records to set revoked_at to NULL if not already
UPDATE class_teacher_permissions 
SET revoked_at = NULL 
WHERE revoked_at IS NULL AND is_active = 1;

-- Show the table structure to verify the change
DESCRIBE class_teacher_permissions;

SELECT 'Database fix completed successfully!' as message;
