<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Icon and Checkbox Test</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/checkbox-improvements.css') }}"
    />
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Oxygen, Ubuntu, Cantarell, sans-serif;
        padding: 20px;
        background: #f5f5f5;
      }
      .test-section {
        background: white;
        padding: 20px;
        margin: 20px 0;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .test-item {
        margin: 10px 0;
        padding: 10px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
      }
      .before-after {
        display: flex;
        gap: 20px;
      }
      .before,
      .after {
        flex: 1;
        padding: 10px;
        border-radius: 4px;
      }
      .before {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
      }
      .after {
        background: #d4edda;
        border: 1px solid #c3e6cb;
      }
    </style>
  </head>
  <body>
    <h1>Icon and Checkbox Fix Test</h1>

    <div class="test-section">
      <h2>1. Checkbox Test</h2>
      <div class="test-item">
        <input type="checkbox" class="subject-checkbox" id="test1" checked />
        <label for="test1" class="checkbox-label">
          Mathematics
          <span class="upload-status uploaded-by-me">
            <i class="fas fa-check-circle"></i> Uploaded by you
          </span>
        </label>
      </div>

      <div class="test-item">
        <input type="checkbox" class="subject-checkbox" id="test2" />
        <label for="test2" class="checkbox-label">
          English
          <span class="upload-status not-uploaded">
            <i class="fas fa-exclamation-triangle"></i> Not uploaded
          </span>
        </label>
      </div>

      <div class="test-item">
        <input type="checkbox" class="subject-checkbox" id="test3" checked />
        <label for="test3" class="checkbox-label">
          Science
          <span class="upload-status uploaded-by-other">
            <i class="fas fa-check-circle"></i> Uploaded by Teacher
          </span>
        </label>
      </div>
    </div>

    <div class="test-section">
      <h2>2. Unicode Symbol Replacement Test</h2>
      <div class="before-after">
        <div class="before">
          <h3>Before (Problematic)</h3>
          <p>✓ Check mark</p>
          <p>⚠ Warning</p>
          <p>☐ Empty checkbox</p>
          <p>☑ Checked checkbox</p>
          <p>→ Arrow right</p>
          <p>★ Star</p>
          <p>● Circle</p>
        </div>
        <div class="after">
          <h3>After (Fixed)</h3>
          <p><i class="fas fa-check"></i> Check mark</p>
          <p><i class="fas fa-exclamation-triangle"></i> Warning</p>
          <p><i class="far fa-square"></i> Empty checkbox</p>
          <p><i class="fas fa-check-square"></i> Checked checkbox</p>
          <p><i class="fas fa-arrow-right"></i> Arrow right</p>
          <p><i class="fas fa-star"></i> Star</p>
          <p><i class="fas fa-circle"></i> Circle</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>3. Font Awesome Icons Test</h2>
      <div class="test-item">
        <i class="fas fa-check-circle" style="color: green"></i> Success
        <i class="fas fa-exclamation-triangle" style="color: orange"></i>
        Warning <i class="fas fa-times-circle" style="color: red"></i> Error
        <i class="fas fa-info-circle" style="color: blue"></i> Info
      </div>
    </div>

    <div class="test-section">
      <h2>4. Notification Test</h2>
      <div class="test-item">
        <button onclick="showTestNotification()">Test Notification</button>
        <div id="notification-area"></div>
      </div>
    </div>

    <script src="{{ url_for('static', filename='js/icon-fallback.js') }}"></script>
    <script>
      function showTestNotification() {
        const area = document.getElementById("notification-area");
        area.innerHTML = `
                <div style="background: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin: 10px 0;">
                    <i class="fas fa-check-circle"></i> Test notification with icon
                </div>
            `;

        // Test the icon fallback
        setTimeout(() => {
          area.innerHTML += `
                    <div style="background: #fff3cd; color: #856404; padding: 10px; border-radius: 4px; margin: 10px 0;">
                        <i class="fas fa-exclamation-triangle"></i> Unicode symbols should be replaced automatically
                    </div>
                `;
        }, 1000);
      }

      // Test the icon system
      document.addEventListener("DOMContentLoaded", function () {
        console.log("Icon test page loaded");

        // Test Font Awesome detection
        const testIcon = document.createElement("i");
        testIcon.className = "fas fa-check";
        testIcon.style.display = "none";
        document.body.appendChild(testIcon);

        const computed = window.getComputedStyle(testIcon);
        const fontFamily = computed.getPropertyValue("font-family");

        console.log("Font family detected:", fontFamily);
        console.log(
          "Font Awesome loaded:",
          fontFamily.includes("Font Awesome")
        );

        document.body.removeChild(testIcon);
      });
    </script>
  </body>
</html>
