<!DOCTYPE html>
<html>
  <head>
    <title>Mobile Navigation Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background-color: #f5f5f5;
      }

      .test-container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      h1 {
        color: #333;
        text-align: center;
        margin-bottom: 30px;
      }

      .test-section {
        margin-bottom: 30px;
        padding: 20px;
        background-color: #f9f9f9;
        border-radius: 5px;
        border-left: 4px solid #007bff;
      }

      .test-section h2 {
        color: #007bff;
        margin-top: 0;
      }

      .test-button {
        background-color: #007bff;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
        font-size: 14px;
      }

      .test-button:hover {
        background-color: #0056b3;
      }

      .test-results {
        margin-top: 15px;
        padding: 15px;
        background-color: #e9ecef;
        border-radius: 4px;
        font-family: monospace;
        white-space: pre-wrap;
      }

      .success {
        color: #28a745;
      }

      .error {
        color: #dc3545;
      }

      .warning {
        color: #ffc107;
      }

      .info {
        color: #17a2b8;
      }

      .navigation-links {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 20px;
      }

      .nav-link {
        display: inline-block;
        padding: 8px 16px;
        background-color: #6c757d;
        color: white;
        text-decoration: none;
        border-radius: 4px;
        transition: background-color 0.2s;
      }

      .nav-link:hover {
        background-color: #5a6268;
      }

      .nav-link[data-tab="recent-reports"] {
        background-color: #28a745;
      }

      .nav-link[data-tab="upload-marks"] {
        background-color: #17a2b8;
      }

      .nav-link[data-tab="generate-reports"] {
        background-color: #ffc107;
        color: #212529;
      }

      .nav-link[data-tab="management"] {
        background-color: #fd7e14;
      }
    </style>
  </head>
  <body>
    <div class="test-container">
      <h1>🧪 Mobile Navigation Test</h1>

      <div class="test-section">
        <h2>📱 Navigation Links Test</h2>
        <p>Click these links to test mobile navigation functionality:</p>
        <div class="navigation-links">
          <a href="#" data-tab="recent-reports" class="nav-link"
            >📊 Recent Reports</a
          >
          <a href="#" data-tab="upload-marks" class="nav-link"
            >📝 Upload Marks</a
          >
          <a href="#" data-tab="generate-reports" class="nav-link"
            >📈 Generate Reports</a
          >
          <a href="#" data-tab="management" class="nav-link">⚙️ Management</a>
        </div>
        <div class="test-results" id="navigationResults"></div>
      </div>

      <div class="test-section">
        <h2>🔧 Function Tests</h2>
        <p>Test individual functions:</p>
        <button class="test-button" onclick="testSwitchMainTab()">
          Test switchMainTab
        </button>
        <button class="test-button" onclick="testNavigateToFeature()">
          Test navigateToFeature
        </button>
        <button class="test-button" onclick="testMobileNavigation()">
          Test Mobile Navigation
        </button>
        <button class="test-button" onclick="clearResults()">
          Clear Results
        </button>
        <div class="test-results" id="functionResults"></div>
      </div>

      <div class="test-section">
        <h2>📋 Function Availability</h2>
        <div class="test-results" id="availabilityResults"></div>
      </div>
    </div>

    <script>
      // Test results logging
      function logResult(elementId, message, type = "info") {
        const element = document.getElementById(elementId);
        const timestamp = new Date().toLocaleTimeString();
        const colorClass =
          type === "success"
            ? "success"
            : type === "error"
            ? "error"
            : type === "warning"
            ? "warning"
            : "info";

        element.innerHTML += `<span class="${colorClass}">[${timestamp}] ${message}</span>\n`;
        element.scrollTop = element.scrollHeight;
      }

      function clearResults() {
        document
          .querySelectorAll(".test-results")
          .forEach((el) => (el.innerHTML = ""));
      }

      // Test navigation link clicks
      document.addEventListener("click", function (e) {
        const link = e.target.closest("a[data-tab]");
        if (link) {
          e.preventDefault();
          const tabId = link.getAttribute("data-tab");

          logResult(
            "navigationResults",
            `🔄 Clicked navigation link: ${tabId}`,
            "info"
          );

          // Test if switchMainTab function exists
          if (typeof switchMainTab === "function") {
            try {
              switchMainTab(tabId);
              logResult(
                "navigationResults",
                `✅ Successfully called switchMainTab('${tabId}')`,
                "success"
              );
            } catch (error) {
              logResult(
                "navigationResults",
                `❌ Error calling switchMainTab('${tabId}'): ${error.message}`,
                "error"
              );
            }
          } else {
            logResult(
              "navigationResults",
              `⚠️ switchMainTab function not found`,
              "warning"
            );
          }

          // Test if navigateToFeature function exists
          if (typeof navigateToFeature === "function") {
            try {
              navigateToFeature(tabId);
              logResult(
                "navigationResults",
                `✅ Successfully called navigateToFeature('${tabId}')`,
                "success"
              );
            } catch (error) {
              logResult(
                "navigationResults",
                `❌ Error calling navigateToFeature('${tabId}'): ${error.message}`,
                "error"
              );
            }
          } else {
            logResult(
              "navigationResults",
              `⚠️ navigateToFeature function not found`,
              "warning"
            );
          }
        }
      });

      // Test individual functions
      function testSwitchMainTab() {
        logResult(
          "functionResults",
          "🧪 Testing switchMainTab function...",
          "info"
        );

        if (typeof switchMainTab === "function") {
          try {
            switchMainTab("recent-reports");
            logResult(
              "functionResults",
              "✅ switchMainTab function called successfully",
              "success"
            );
          } catch (error) {
            logResult(
              "functionResults",
              `❌ Error in switchMainTab: ${error.message}`,
              "error"
            );
          }
        } else {
          logResult(
            "functionResults",
            "⚠️ switchMainTab function not available",
            "warning"
          );
        }
      }

      function testNavigateToFeature() {
        logResult(
          "functionResults",
          "🧪 Testing navigateToFeature function...",
          "info"
        );

        if (typeof navigateToFeature === "function") {
          try {
            navigateToFeature("recent-reports");
            logResult(
              "functionResults",
              "✅ navigateToFeature function called successfully",
              "success"
            );
          } catch (error) {
            logResult(
              "functionResults",
              `❌ Error in navigateToFeature: ${error.message}`,
              "error"
            );
          }
        } else {
          logResult(
            "functionResults",
            "⚠️ navigateToFeature function not available",
            "warning"
          );
        }
      }

      function testMobileNavigation() {
        logResult(
          "functionResults",
          "🧪 Testing mobile navigation classes...",
          "info"
        );

        // Test if mobile navigation elements exist
        const navElement = document.getElementById("classteacherNav");
        const toggleBtn = document.querySelector(".mobile-nav-toggle");

        if (navElement) {
          logResult(
            "functionResults",
            "✅ Navigation element found",
            "success"
          );
        } else {
          logResult(
            "functionResults",
            "⚠️ Navigation element not found",
            "warning"
          );
        }

        if (toggleBtn) {
          logResult("functionResults", "✅ Toggle button found", "success");
        } else {
          logResult("functionResults", "⚠️ Toggle button not found", "warning");
        }
      }

      // Check function availability on page load
      window.addEventListener("load", function () {
        const functions = [
          "switchMainTab",
          "navigateToFeature",
          "MobileNavigationController",
          "sendToTerminal",
        ];

        functions.forEach((funcName) => {
          if (typeof window[funcName] === "function") {
            logResult(
              "availabilityResults",
              `✅ ${funcName} is available`,
              "success"
            );
          } else {
            logResult(
              "availabilityResults",
              `❌ ${funcName} is not available`,
              "error"
            );
          }
        });

        // Check for mobile navigation elements
        const elements = [
          { id: "classteacherNav", name: "Navigation Element" },
          { selector: ".mobile-nav-toggle", name: "Toggle Button" },
          { selector: ".nav-link[data-tab]", name: "Navigation Links" },
        ];

        elements.forEach((element) => {
          let found = false;
          if (element.id) {
            found = document.getElementById(element.id) !== null;
          } else if (element.selector) {
            found = document.querySelector(element.selector) !== null;
          }

          if (found) {
            logResult(
              "availabilityResults",
              `✅ ${element.name} found`,
              "success"
            );
          } else {
            logResult(
              "availabilityResults",
              `❌ ${element.name} not found`,
              "error"
            );
          }
        });
      });
    </script>
  </body>
</html>
