/**
 * H<PERSON><PERSON><PERSON>EW SCHOOL MANAGEMENT SYSTEM - MOBILE NAVIGATION
 * Clean, Modern, Responsive JavaScript for Class Teacher Dashboard
 *
 * Features:
 * - Mobile-first responsive navigation
 * - Touch-friendly interactions
 * - Smooth animations
 * - Accessibility support
 * - Modern ES6+ syntax
 */

class MobileNavigation {
  constructor() {
    this.mobileNav = null;
    this.mobileNavToggle = null;
    this.mobileNavClose = null;
    this.isOpen = false;

    this.init();
  }

  init() {
    // Wait for DOM to be ready
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () => this.setupElements());
    } else {
      this.setupElements();
    }
  }

  setupElements() {
    this.mobileNav = document.getElementById("mobileNav");
    this.mobileNavToggle = document.getElementById("mobileNavToggle");
    this.mobileNavClose = document.getElementById("mobileNavClose");

    if (!this.mobileNav || !this.mobileNavToggle || !this.mobileNavClose) {
      console.warn("Mobile navigation elements not found");
      return;
    }

    this.bindEvents();
  }

  bindEvents() {
    // Toggle button click
    this.mobileNavToggle.addEventListener("click", (e) => {
      e.preventDefault();
      this.toggle();
    });

    // Close button click
    this.mobileNavClose.addEventListener("click", (e) => {
      e.preventDefault();
      this.close();
    });

    // Close on overlay click
    this.mobileNav.addEventListener("click", (e) => {
      if (e.target === this.mobileNav) {
        this.close();
      }
    });

    // Close on escape key
    document.addEventListener("keydown", (e) => {
      if (e.key === "Escape" && this.isOpen) {
        this.close();
      }
    });

    // Close on navigation link click
    const navLinks = this.mobileNav.querySelectorAll(".mobile-nav-links a");
    navLinks.forEach((link) => {
      link.addEventListener("click", () => {
        this.close();
      });
    });

    // Handle window resize
    window.addEventListener("resize", () => {
      if (window.innerWidth > 768 && this.isOpen) {
        this.close();
      }
    });
  }

  toggle() {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  open() {
    this.mobileNav.classList.add("active");
    this.isOpen = true;

    // Prevent body scroll
    document.body.style.overflow = "hidden";

    // Focus management
    this.mobileNavClose.focus();

    // Update toggle button icon
    const icon = this.mobileNavToggle.querySelector("i");
    if (icon) {
      icon.className = "fas fa-times";
    }
  }

  close() {
    this.mobileNav.classList.remove("active");
    this.isOpen = false;

    // Restore body scroll
    document.body.style.overflow = "";

    // Return focus to toggle button
    this.mobileNavToggle.focus();

    // Update toggle button icon
    const icon = this.mobileNavToggle.querySelector("i");
    if (icon) {
      icon.className = "fas fa-bars";
    }
  }
}

// Touch-friendly form enhancements
class TouchEnhancements {
  constructor() {
    this.init();
  }

  init() {
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () =>
        this.setupTouchEnhancements()
      );
    } else {
      this.setupTouchEnhancements();
    }
  }

  setupTouchEnhancements() {
    // Add touch feedback to buttons
    const buttons = document.querySelectorAll(
      ".modern-btn, .tab-button, .quick-action-card"
    );
    buttons.forEach((button) => {
      button.addEventListener("touchstart", () => {
        button.style.transform = "scale(0.95)";
        button.style.transition = "transform 0.1s ease";
      });

      button.addEventListener("touchend", () => {
        button.style.transform = "scale(1)";
      });
    });

    // Improve file upload touch experience
    const fileInput = document.getElementById("marks_file");
    const dragDropArea = document.querySelector(".drag-drop-area");

    if (fileInput && dragDropArea) {
      dragDropArea.addEventListener("touchstart", (e) => {
        dragDropArea.style.backgroundColor = "rgba(0, 123, 255, 0.1)";
      });

      dragDropArea.addEventListener("touchend", (e) => {
        dragDropArea.style.backgroundColor = "";
        fileInput.click();
      });
    }

    // Prevent zoom on form inputs (iOS)
    const inputs = document.querySelectorAll("input, select, textarea");
    inputs.forEach((input) => {
      if (input.type !== "file") {
        input.style.fontSize = "16px";
      }
    });
  }
}

// Responsive table handling
class ResponsiveTable {
  constructor() {
    this.init();
  }

  init() {
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () =>
        this.setupResponsiveTables()
      );
    } else {
      this.setupResponsiveTables();
    }
  }

  setupResponsiveTables() {
    const tables = document.querySelectorAll("table");
    tables.forEach((table) => {
      if (!table.closest(".table-wrapper")) {
        const wrapper = document.createElement("div");
        wrapper.className = "table-wrapper";
        table.parentNode.insertBefore(wrapper, table);
        wrapper.appendChild(table);
      }
    });
  }
}

// Initialize all mobile enhancements
document.addEventListener("DOMContentLoaded", () => {
  // Only initialize on mobile devices or when mobile nav toggle is present
  if (window.innerWidth <= 768 || document.getElementById("mobileNavToggle")) {
    new MobileNavigation();
    new TouchEnhancements();
    new ResponsiveTable();

    console.log("✅ Mobile navigation initialized");
  }
});

// Navigation feature mapping for backward compatibility
window.navigateToFeature = function (feature) {
  // Close mobile nav first
  const mobileNav = document.getElementById("mobileNav");
  if (mobileNav && mobileNav.classList.contains("mobile-nav-open")) {
    mobileNav.classList.remove("mobile-nav-open");
    document.body.style.overflow = "";
  }

  // Navigate to feature
  switch (feature) {
    case "upload-marks":
      if (typeof switchMainTab === "function") {
        switchMainTab("upload-marks");
      } else {
        // Fallback method
        const uploadTab = document.getElementById("upload-marks-tab");
        if (uploadTab) {
          // Hide all tabs
          document.querySelectorAll(".tab-content-container").forEach((tab) => {
            tab.classList.remove("active");
          });
          // Show upload marks tab
          uploadTab.classList.add("active");

          // Update tab buttons
          document.querySelectorAll(".tab-button").forEach((btn) => {
            btn.classList.remove("active");
          });
          const uploadBtn = document.querySelector(
            '.tab-button[onclick*="upload-marks"]'
          );
          if (uploadBtn) {
            uploadBtn.classList.add("active");
          }
        }
      }

      // Scroll to upload marks section
      setTimeout(() => {
        const uploadSection =
          document.getElementById("upload-marks-tab") ||
          document.getElementById("upload-marks-section");
        if (uploadSection) {
          uploadSection.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }, 100);
      break;

    case "recent-reports":
      if (typeof switchMainTab === "function") {
        switchMainTab("recent-reports");
      }
      break;

    case "generate-reports":
      if (typeof switchMainTab === "function") {
        switchMainTab("generate-reports");
      }
      break;

    case "management":
      if (typeof switchMainTab === "function") {
        switchMainTab("management");
      }
      break;

    default:
      console.warn("Unknown feature:", feature);
  }

  return false; // Prevent default link behavior
};

// Export for manual initialization if needed
window.HillviewMobile = {
  MobileNavigation,
  TouchEnhancements,
  ResponsiveTable,
};
