/* Enhanced Checkbox Styling for Better Visual Experience */

/* Modern Checkbox Replacement */
.subject-checkbox {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid #007bff;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
  outline: none;
  margin-right: 8px;
}

.subject-checkbox:hover {
  border-color: #0056b3;
  background: #f8f9fa;
}

.subject-checkbox:checked {
  background: #007bff;
  border-color: #007bff;
}

.subject-checkbox:checked::before {
  content: "✓";
  font-family: Arial, sans-serif;
  font-weight: bold;
  color: white;
  font-size: 12px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.subject-checkbox:focus {
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Improved Upload Status Icons */
.upload-status {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-left: 8px;
}

.upload-status.uploaded-by-me {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.upload-status.uploaded-by-other {
  background: rgba(23, 162, 184, 0.1);
  color: #17a2b8;
}

.upload-status.not-uploaded {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.upload-status i {
  font-size: 12px;
}

/* Better checkbox labels */
.checkbox-label {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  min-height: 44px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.checkbox-label:hover {
  background: rgba(0, 123, 255, 0.05);
}

/* Fix for notification checkbox */
input[type="checkbox"][name="notify_parents"] {
  width: 18px;
  height: 18px;
  accent-color: #007bff;
  cursor: pointer;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .subject-checkbox {
    width: 18px;
    height: 18px;
    margin-right: 6px;
  }

  .checkbox-label {
    font-size: 13px;
    min-height: 40px;
    padding: 6px;
  }

  .upload-status {
    font-size: 11px;
    padding: 2px 6px;
  }
}
