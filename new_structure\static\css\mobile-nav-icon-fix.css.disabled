/* ===================================================================
   HILLVIEW SCHOOL MANAGEMENT SYSTEM - MOBILE NAVIGATION ICON FIX
   Ensures all icons are visible and properly styled in mobile navigation
   ================================================================== */

/* ===== MOBILE NAVIGATION ICON VISIBILITY ===== */
.mobile-nav-toggle {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: var(--primary-color) !important;
  color: var(--text-inverse) !important;
  border: none !important;
  border-radius: var(--radius-md) !important;
  padding: var(--space-3) !important;
  font-size: var(--font-size-lg) !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  width: 48px !important;
  height: 48px !important;
  z-index: 1001 !important;
  position: relative !important;
}

.mobile-nav-toggle:hover {
  background: var(--primary-hover) !important;
  transform: scale(1.05) !important;
}

.mobile-nav-toggle i,
.mobile-nav-toggle .mobile-nav-icon {
  font-size: 20px !important;
  color: var(--text-inverse) !important;
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  font-family: "Font Awesome 6 Free" !important;
  font-weight: 900 !important;
  text-rendering: auto !important;
  line-height: 1 !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* Force the hamburger icon to show */
.mobile-nav-toggle .fa-bars::before {
  content: "\f0c9" !important;
  display: inline-block !important;
  font-style: normal !important;
  font-variant: normal !important;
  text-rendering: auto !important;
  line-height: 1 !important;
}

/* Emergency fallback for hamburger icon */
.mobile-nav-toggle::after {
  content: "☰" !important;
  font-size: 20px !important;
  color: var(--text-inverse) !important;
  display: none !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

/* Show fallback if Font Awesome fails */
.mobile-nav-toggle i:empty::before {
  content: "☰" !important;
}

/* Show fallback if no icon at all */
.mobile-nav-toggle:empty::after {
  display: block !important;
}

/* ===== MOBILE NAVIGATION CONTENT ICONS ===== */
.mobile-nav-content .nav-link {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-3) !important;
  padding: var(--space-4) var(--space-6) !important;
  color: var(--text-inverse) !important;
  text-decoration: none !important;
  border-radius: var(--radius-lg) !important;
  margin: var(--space-2) 0 !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  transition: all 0.3s ease !important;
  min-height: 56px !important;
}

.mobile-nav-content .nav-link:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-2px) !important;
}

.mobile-nav-content .nav-link i {
  font-size: var(--font-size-xl) !important;
  color: var(--text-inverse) !important;
  opacity: 1 !important;
  display: inline-block !important;
  width: 24px !important;
  text-align: center !important;
  flex-shrink: 0 !important;
}

.mobile-nav-content .nav-link span {
  font-size: var(--font-size-base) !important;
  color: var(--text-inverse) !important;
  opacity: 1 !important;
  display: inline-block !important;
  font-weight: var(--font-weight-medium) !important;
}

/* ===== FAB SYSTEM ICONS ===== */
.fab-container {
  position: fixed !important;
  bottom: 30px !important;
  right: 30px !important;
  z-index: 1000 !important;
}

.fab-main {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 64px !important;
  height: 64px !important;
  border-radius: var(--radius-full) !important;
  background: var(--primary-color) !important;
  color: var(--text-inverse) !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow: var(--shadow-lg) !important;
}

.fab-main:hover {
  background: var(--primary-hover) !important;
  transform: scale(1.1) !important;
  box-shadow: var(--shadow-xl) !important;
}

.fab-main i {
  font-size: 24px !important;
  color: var(--text-inverse) !important;
  opacity: 1 !important;
  display: block !important;
  transition: transform 0.3s ease !important;
}

.fab-main.active i {
  transform: rotate(45deg) !important;
}

.fab-item {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 48px !important;
  height: 48px !important;
  border-radius: var(--radius-full) !important;
  background: var(--secondary-color) !important;
  color: var(--text-inverse) !important;
  text-decoration: none !important;
  margin: var(--space-2) 0 !important;
  transition: all 0.3s ease !important;
  box-shadow: var(--shadow-md) !important;
  opacity: 0 !important;
  transform: scale(0) !important;
}

.fab-menu.active .fab-item {
  opacity: 1 !important;
  transform: scale(1) !important;
}

.fab-item:hover {
  background: var(--secondary-hover) !important;
  transform: scale(1.1) !important;
  box-shadow: var(--shadow-lg) !important;
}

.fab-item i {
  font-size: 18px !important;
  color: var(--text-inverse) !important;
  opacity: 1 !important;
  display: block !important;
}

/* ===== THEME TOGGLE ICON ===== */
.theme-toggle {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  z-index: 1000 !important;
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: var(--radius-full) !important;
  padding: var(--space-3) !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow: var(--shadow-sm) !important;
  color: var(--text-primary) !important;
  font-size: var(--font-size-lg) !important;
  width: 48px !important;
  height: 48px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.theme-toggle:hover {
  box-shadow: var(--shadow-md) !important;
  transform: scale(1.05) !important;
}

.theme-toggle i {
  transition: all 0.3s ease !important;
  color: var(--text-primary) !important;
  opacity: 1 !important;
  display: block !important;
  font-size: 20px !important;
}

/* ===== GENERAL ICON FIXES ===== */
i,
.fa,
.fas,
.far,
.fal,
.fab {
  font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro",
    "Font Awesome 5 Free", "Font Awesome 5 Pro" !important;
  font-style: normal !important;
  font-variant: normal !important;
  text-rendering: auto !important;
  line-height: 1 !important;
  opacity: 1 !important;
  display: inline-block !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

.fas {
  font-weight: 900 !important;
}

.far {
  font-weight: 400 !important;
}

.fab {
  font-weight: 400 !important;
}

/* ===== DARK MODE ICON OVERRIDES ===== */
[data-theme="dark"] .mobile-nav-toggle {
  background: var(--primary-color) !important;
  color: var(--text-inverse) !important;
}

[data-theme="dark"] .mobile-nav-toggle i {
  color: var(--text-inverse) !important;
}

[data-theme="dark"] .mobile-nav-content .nav-link {
  color: var(--text-inverse) !important;
}

[data-theme="dark"] .mobile-nav-content .nav-link i {
  color: var(--text-inverse) !important;
}

[data-theme="dark"] .mobile-nav-content .nav-link span {
  color: var(--text-inverse) !important;
}

[data-theme="dark"] .fab-main {
  background: var(--primary-color) !important;
  color: var(--text-inverse) !important;
}

[data-theme="dark"] .fab-main i {
  color: var(--text-inverse) !important;
}

[data-theme="dark"] .fab-item {
  background: var(--secondary-color) !important;
  color: var(--text-inverse) !important;
}

[data-theme="dark"] .fab-item i {
  color: var(--text-inverse) !important;
}

[data-theme="dark"] .theme-toggle {
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .theme-toggle i {
  color: var(--text-primary) !important;
}

/* ===== MOBILE RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {
  .theme-toggle {
    top: 15px !important;
    right: 15px !important;
    width: 44px !important;
    height: 44px !important;
    font-size: var(--font-size-base) !important;
  }

  .theme-toggle i {
    font-size: 18px !important;
  }

  .mobile-nav-toggle {
    width: 44px !important;
    height: 44px !important;
  }

  .mobile-nav-toggle i {
    font-size: 18px !important;
  }

  .fab-main {
    width: 56px !important;
    height: 56px !important;
    bottom: 20px !important;
    right: 20px !important;
  }

  .fab-main i {
    font-size: 20px !important;
  }

  .fab-item {
    width: 44px !important;
    height: 44px !important;
  }

  .fab-item i {
    font-size: 16px !important;
  }
}

/* ===== ANIMATION FIXES ===== */
.theme-switching * {
  transition: none !important;
}

.theme-switching i {
  opacity: 1 !important;
  display: block !important;
}

/* ===== FALLBACK ICON CONTENT ===== */
.fa-bars::before {
  content: "\f0c9" !important;
}
.fa-times::before {
  content: "\f00d" !important;
}
.fa-plus::before {
  content: "\f067" !important;
}
.fa-home::before {
  content: "\f015" !important;
}
.fa-chart-bar::before {
  content: "\f080" !important;
}
.fa-users::before {
  content: "\f0c0" !important;
}
.fa-sign-out-alt::before {
  content: "\f2f5" !important;
}
.fa-moon::before {
  content: "\f186" !important;
}
.fa-sun::before {
  content: "\f185" !important;
}
.fa-cog::before {
  content: "\f013" !important;
}
.fa-bell::before {
  content: "\f0f3" !important;
}
.fa-envelope::before {
  content: "\f0e0" !important;
}
