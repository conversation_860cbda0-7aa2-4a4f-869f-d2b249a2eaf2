#!/usr/bin/env python3
"""
Debug version of run script for troubleshooting connection issues
"""

import os
import sys
import socket

# Add the parent directory to the Python path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def get_local_ip():
    """Get the local IP address of this machine."""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            return local_ip
    except Exception:
        return "127.0.0.1"

try:
    print("🔧 Starting Hillview School Management System (Debug Mode)")
    print("=" * 50)
    
    # Import create_app from the new_structure package
    from new_structure import create_app
    
    print("✅ Successfully imported create_app")
    
    # Create the Flask application
    app = create_app('development')
    
    print("✅ Flask app created successfully")
    
    # Get the actual local IP address
    local_ip = get_local_ip()
    
    print(f"🌐 Local IP detected: {local_ip}")
    print(f"📍 Starting server on http://localhost:5000")
    print(f"🌐 Network access: http://{local_ip}:5000")
    print("=" * 50)
    
    # Run on port 5000 instead of 3000 to avoid conflicts
    app.run(
        debug=True, 
        host='0.0.0.0', 
        port=5000, 
        use_reloader=False,
        threaded=True
    )

except Exception as e:
    print(f"❌ Error starting application: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
