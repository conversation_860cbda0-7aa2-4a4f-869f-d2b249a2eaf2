#!/usr/bin/env python3
"""
Enhanced run script for the Hillview School Management System.
This script creates and runs the Flask application with better error handling.
"""

import os
import sys
import socket
import platform
import signal
import threading
import time

# Add the parent directory to the Python path so we can import new_structure as a package
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def get_local_ip():
    """Get the local IP address of this machine."""
    try:
        # Connect to a remote address to determine local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            # Connect to Google's DNS server (doesn't actually send data)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            return local_ip
    except Exception:
        # Fallback to localhost if network detection fails
        return "127.0.0.1"

def signal_handler(signum, frame):
    """Handle graceful shutdown on SIGINT/SIGTERM."""
    print("\n🛑 Shutting down Hillview School Management System...")
    sys.exit(0)

def is_mingw_environment():
    """Check if running in MINGW64 environment."""
    return (
        platform.system() == "Windows" and
        ("MINGW" in os.environ.get("MSYSTEM", "") or
         "MSYS" in os.environ.get("MSYSTEM", ""))
    )

def run_app():
    """Run the Flask application with enhanced error handling."""
    try:
        # Import create_app from the new_structure package
        from new_structure import create_app

        # Determine environment
        env = os.environ.get('FLASK_ENV', 'development')

        # Create the Flask application
        app = create_app(env)

        # Check if HTTPS is requested
        use_https = '--https' in sys.argv or os.environ.get('FLASK_HTTPS') == 'true'

        if use_https:
            print("🚀 Hillview School Management System")
            print("📍 Server: https://localhost:5000")
            print("🔒 HTTPS Mode: Enabled for PWA testing")
            print("⚠️  You may see security warnings - click 'Advanced' and 'Proceed'")

            # Install pyOpenSSL if not available
            try:
                import ssl
                app.run(
                    debug=True, 
                    host='0.0.0.0', 
                    port=5000, 
                    ssl_context='adhoc',
                    use_reloader=False,
                    threaded=True
                )
            except ImportError:
                print("❌ HTTPS requires pyOpenSSL. Install with: pip install pyOpenSSL")
                print("📍 Falling back to HTTP: http://localhost:5000")
                app.run(
                    debug=True, 
                    host='0.0.0.0', 
                    port=5000,
                    use_reloader=False,
                    threaded=True
                )
        else:
            # Get the actual local IP address
            local_ip = get_local_ip()

            print("✅ Hillview School Management System - Ready")
            print(f"📍 Local:   http://localhost:3000")
            print(f"🌐 Network: http://{local_ip}:3000")
            print("")

            # Enhanced configuration for different environments
            app_config = {
                'debug': True,
                'host': '0.0.0.0',
                'port': 3000,
                'use_reloader': False,
                'threaded': True,
                'use_evalex': False,  # Disable interactive debugger for safety
            }

            # Additional configuration for MINGW64 environment
            if is_mingw_environment():
                print("🔧 MINGW64 environment detected - using compatibility mode")
                app_config.update({
                    'use_debugger': False,
                    'passthrough_errors': True,
                })

            # Run the application
            app.run(**app_config)

    except KeyboardInterrupt:
        print("\n🛑 Application stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        import traceback
        traceback.print_exc()
        
        # Additional error information
        print("\n📋 Environment Information:")
        print(f"   - Platform: {platform.system()}")
        print(f"   - Python Version: {sys.version}")
        print(f"   - MSYSTEM: {os.environ.get('MSYSTEM', 'Not set')}")
        print(f"   - Working Directory: {os.getcwd()}")
        
        sys.exit(1)

if __name__ == "__main__":
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)
    
    # Run the application
    run_app()
