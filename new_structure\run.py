#!/usr/bin/env python3
"""
Run script for the Hillview School Management System.
This script creates and runs the Flask application.
"""

import os
import sys
import socket
import subprocess

def check_and_use_venv():
    """Check if virtual environment exists and switch to it if needed."""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    venv_python = os.path.join(current_dir, 'venv', 'Scripts', 'python.exe')

    # Normalize paths for comparison
    current_python = os.path.normpath(sys.executable)
    expected_python = os.path.normpath(venv_python)

    # Check if we need to switch to virtual environment

    # If venv exists and we're not already using it
    if os.path.exists(venv_python) and current_python != expected_python:
        print("🔄 Switching to virtual environment...")
        # Re-run this script with the virtual environment Python
        try:
            result = subprocess.run([venv_python, __file__] + sys.argv[1:])
            sys.exit(result.returncode)
        except Exception as e:
            print(f"❌ Failed to switch to venv: {e}")
            print("⚠️ Continuing with current Python...")
    elif os.path.exists(venv_python):
        print("✅ Using virtual environment")
    else:
        print("⚠️ Virtual environment not found - using system Python")

# Check and switch to virtual environment if needed
check_and_use_venv()

# Add the current directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def get_local_ip():
    """Get the local IP address of this machine."""
    try:
        # Connect to a remote address to determine local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            # Connect to Google's DNS server (doesn't actually send data)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            return local_ip
    except Exception:
        # Fallback to localhost if network detection fails
        return "127.0.0.1"

try:
    # Add parent directory to Python path for imports
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)

    # Import create_app from the new_structure package
    from new_structure import create_app

    # Determine environment
    env = os.environ.get('FLASK_ENV', 'development')

    # Create the Flask application
    app = create_app(env)

    # Check if HTTPS is requested
    use_https = '--https' in sys.argv or os.environ.get('FLASK_HTTPS') == 'true'

    if use_https:
        print("🚀 Hillview School Management System")
        print("📍 Server: https://localhost:6700")
        print("🔒 HTTPS Mode: Enabled for PWA testing")
        print("⚠️  You may see security warnings - click 'Advanced' and 'Proceed'")

        # Install pyOpenSSL if not available
        try:
            import ssl
            app.run(debug=True, host='0.0.0.0', port=6700, ssl_context='adhoc')
        except ImportError:
            print("❌ HTTPS requires pyOpenSSL. Install with: pip install pyOpenSSL")
            print("📍 Falling back to HTTP: http://localhost:6700")
            app.run(debug=True, host='0.0.0.0', port=6700)
    else:
        # Get the actual local IP address
        local_ip = get_local_ip()

        print("🚀 Hillview School Management System")
        print(f"📍 Local:   http://localhost:6700")
        print(f"🌐 Network: http://{local_ip}:6700")
        print("")

        # Enhanced configuration for stability
        app.run(
            debug=True,
            host='0.0.0.0',
            port=6700,
            use_reloader=False,
            threaded=True,
            use_evalex=False,  # Disable interactive debugger for safety
            passthrough_errors=False
        )

except Exception as e:
    print(f"❌ Error starting application: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
