/* ===================================================================
   HILLVIEW SCHOOL MANAGEMENT SYSTEM - CUSTOM LIGHT MODE COLORS
   Implementing your specific color scheme for light mode
   ================================================================== */

/* ===== ROOT LIGHT MODE OVERRIDES ===== */
:root {
  /* Your Custom Color Palette */
  --custom-text-color: #17cfe0; /* Cyan text */
  --custom-bg-color: #fafafa; /* Light gray background */
  --custom-primary-text: #263ad1; /* Blue primary text */
  --custom-secondary-text: #484b6a; /* Dark gray secondary text */
  --custom-accent-color: #f9cc48; /* Yellow accent/button */
}

/* ===== BODY AND GLOBAL STYLES ===== */
body {
  background-color: var(--custom-bg-color) !important;
  color: var(--custom-primary-text) !important;
}

.container,
.container-fluid {
  background-color: transparent !important;
}

/* ===== HEADING STYLES ===== */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--custom-primary-text) !important;
}

/* ===== TEXT STYLES ===== */
p {
  color: var(--custom-secondary-text) !important;
}

.text-primary {
  color: var(--custom-primary-text) !important;
}

.text-secondary {
  color: var(--custom-secondary-text) !important;
}

.text-muted {
  color: var(--custom-secondary-text) !important;
  opacity: 0.7;
}

/* ===== NAVIGATION STYLES ===== */
.navbar {
  background-color: var(--custom-bg-color) !important;
  border-bottom: 1px solid #e8e8e8 !important;
}

.navbar-brand {
  color: var(--custom-primary-text) !important;
}

.navbar-nav .nav-link {
  color: var(--custom-secondary-text) !important;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
  color: var(--custom-primary-text) !important;
}

.navbar-nav .nav-link.active {
  color: var(--custom-primary-text) !important;
}

/* ===== BUTTON STYLES ===== */
.btn-primary {
  background-color: var(--custom-accent-color) !important;
  border-color: var(--custom-accent-color) !important;
  color: #000000 !important; /* Black text on yellow background */
}

.btn-primary:hover,
.btn-primary:focus {
  background-color: #f7c332 !important;
  border-color: #f7c332 !important;
  color: #000000 !important;
}

.btn-secondary {
  background-color: var(--custom-primary-text) !important;
  border-color: var(--custom-primary-text) !important;
  color: #ffffff !important;
}

.btn-secondary:hover,
.btn-secondary:focus {
  background-color: #1e2db5 !important;
  border-color: #1e2db5 !important;
  color: #ffffff !important;
}

.btn-outline-primary {
  color: var(--custom-primary-text) !important;
  border-color: var(--custom-primary-text) !important;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus {
  background-color: var(--custom-primary-text) !important;
  border-color: var(--custom-primary-text) !important;
  color: #ffffff !important;
}

/* ===== CARD STYLES ===== */
.card {
  background-color: #ffffff !important;
  border: 1px solid #e8e8e8 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
  background-color: #f5f5f5 !important;
  border-bottom: 1px solid #e8e8e8 !important;
  color: var(--custom-primary-text) !important;
}

.card-title {
  color: var(--custom-primary-text) !important;
}

.card-text {
  color: var(--custom-secondary-text) !important;
}

/* ===== FORM STYLES ===== */
.form-control {
  background-color: #ffffff !important;
  border: 1px solid #e8e8e8 !important;
  color: var(--custom-primary-text) !important;
}

.form-control:focus {
  border-color: var(--custom-primary-text) !important;
  box-shadow: 0 0 0 0.2rem rgba(38, 58, 209, 0.25) !important;
}

.form-control::placeholder {
  color: var(--custom-secondary-text) !important;
  opacity: 0.6;
}

.form-label {
  color: var(--custom-primary-text) !important;
}

.form-select {
  background-color: #ffffff !important;
  border: 1px solid #e8e8e8 !important;
  color: var(--custom-primary-text) !important;
}

.form-select:focus {
  border-color: var(--custom-primary-text) !important;
  box-shadow: 0 0 0 0.2rem rgba(38, 58, 209, 0.25) !important;
}

/* ===== TABLE STYLES ===== */
.table {
  background-color: #ffffff !important;
  color: var(--custom-primary-text) !important;
}

.table th {
  background-color: #f5f5f5 !important;
  border-bottom: 1px solid #e8e8e8 !important;
  color: var(--custom-primary-text) !important;
}

.table td {
  border-top: 1px solid #e8e8e8 !important;
  color: var(--custom-secondary-text) !important;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(250, 250, 250, 0.5) !important;
}

.table-hover tbody tr:hover {
  background-color: #f5f5f5 !important;
}

/* ===== MODAL STYLES ===== */
.modal-content {
  background-color: #ffffff !important;
  border: 1px solid #e8e8e8 !important;
}

.modal-header {
  border-bottom: 1px solid #e8e8e8 !important;
}

.modal-title {
  color: var(--custom-primary-text) !important;
}

.modal-body {
  color: var(--custom-secondary-text) !important;
}

.modal-footer {
  border-top: 1px solid #e8e8e8 !important;
}

/* ===== ALERT STYLES ===== */
.alert-primary {
  background-color: rgba(38, 58, 209, 0.1) !important;
  border-color: rgba(38, 58, 209, 0.3) !important;
  color: var(--custom-primary-text) !important;
}

.alert-success {
  background-color: rgba(5, 150, 105, 0.1) !important;
  border-color: rgba(5, 150, 105, 0.3) !important;
  color: #065f46 !important;
}

.alert-warning {
  background-color: rgba(249, 204, 72, 0.1) !important;
  border-color: rgba(249, 204, 72, 0.3) !important;
  color: #d4a91c !important;
}

.alert-danger {
  background-color: rgba(220, 38, 38, 0.1) !important;
  border-color: rgba(220, 38, 38, 0.3) !important;
  color: #991b1b !important;
}

.alert-info {
  background-color: rgba(23, 207, 224, 0.1) !important;
  border-color: rgba(23, 207, 224, 0.3) !important;
  color: #0891b2 !important;
}

/* ===== BADGE STYLES ===== */
.badge.bg-primary {
  background-color: var(--custom-primary-text) !important;
  color: #ffffff !important;
}

.badge.bg-secondary {
  background-color: var(--custom-secondary-text) !important;
  color: #ffffff !important;
}

.badge.bg-warning {
  background-color: var(--custom-accent-color) !important;
  color: #000000 !important;
}

/* ===== LINK STYLES ===== */
a {
  color: var(--custom-text-color) !important;
}

a:hover,
a:focus {
  color: #14a5b5 !important;
}

/* ===== DROPDOWN STYLES ===== */
.dropdown-menu {
  background-color: #ffffff !important;
  border: 1px solid #e8e8e8 !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.dropdown-item {
  color: var(--custom-secondary-text) !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
  background-color: #f5f5f5 !important;
  color: var(--custom-primary-text) !important;
}

/* ===== PAGINATION STYLES ===== */
.pagination .page-link {
  background-color: #ffffff !important;
  border-color: #e8e8e8 !important;
  color: var(--custom-primary-text) !important;
}

.pagination .page-link:hover {
  background-color: #f5f5f5 !important;
  border-color: #e8e8e8 !important;
  color: var(--custom-primary-text) !important;
}

.pagination .page-item.active .page-link {
  background-color: var(--custom-primary-text) !important;
  border-color: var(--custom-primary-text) !important;
  color: #ffffff !important;
}

/* ===== BREADCRUMB STYLES ===== */
.breadcrumb {
  background-color: #f5f5f5 !important;
  border: 1px solid #e8e8e8 !important;
}

.breadcrumb-item a {
  color: var(--custom-text-color) !important;
}

.breadcrumb-item.active {
  color: var(--custom-primary-text) !important;
}

/* ===== PROGRESS BAR STYLES ===== */
.progress {
  background-color: #f5f5f5 !important;
  border: 1px solid #e8e8e8 !important;
}

.progress-bar {
  background-color: var(--custom-accent-color) !important;
}

/* ===== ICON STYLES ===== */
i,
.fa,
.fas,
.far,
.fal,
.fab {
  color: var(--custom-primary-text) !important;
}

.text-primary i,
.text-primary .fa,
.text-primary .fas {
  color: var(--custom-primary-text) !important;
}

.text-secondary i,
.text-secondary .fa,
.text-secondary .fas {
  color: var(--custom-secondary-text) !important;
}

.text-info i,
.text-info .fa,
.text-info .fas {
  color: var(--custom-text-color) !important;
}

/* ===== FOCUS STYLES ===== */
*:focus {
  outline: 2px solid var(--custom-primary-text) !important;
  outline-offset: 2px !important;
}

.btn:focus,
.form-control:focus,
.form-select:focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 58, 209, 0.25) !important;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {
  body {
    background-color: var(--custom-bg-color) !important;
  }

  .navbar {
    background-color: var(--custom-bg-color) !important;
  }

  .card {
    background-color: #ffffff !important;
    border: 1px solid #e8e8e8 !important;
  }
}

/* ===== UTILITY CLASSES ===== */
.bg-custom-primary {
  background-color: var(--custom-bg-color) !important;
}

.text-custom-primary {
  color: var(--custom-primary-text) !important;
}

.text-custom-secondary {
  color: var(--custom-secondary-text) !important;
}

.text-custom-accent {
  color: var(--custom-text-color) !important;
}

.btn-custom-accent {
  background-color: var(--custom-accent-color) !important;
  border-color: var(--custom-accent-color) !important;
  color: #000000 !important;
}

.btn-custom-accent:hover {
  background-color: #f7c332 !important;
  border-color: #f7c332 !important;
  color: #000000 !important;
}
