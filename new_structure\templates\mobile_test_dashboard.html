<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Responsiveness Test - Hillview School</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .test-header h1 {
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .test-header p {
            color: #64748b;
            font-size: 1.1rem;
        }

        .device-simulator {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .device-frame {
            background: #1e293b;
            border-radius: 20px;
            padding: 1rem;
            position: relative;
        }

        .device-screen {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
        }

        .device-label {
            color: white;
            text-align: center;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .mobile-frame {
            width: 280px;
            height: 500px;
            margin: 0 auto;
        }

        .tablet-frame {
            width: 400px;
            height: 300px;
            margin: 0 auto;
        }

        .desktop-frame {
            width: 100%;
            height: 400px;
        }

        .iframe-container {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 12px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .feature-card {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #3b82f6, #10b981);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
        }

        .feature-card h3 {
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .feature-card p {
            color: #64748b;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .test-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
            flex-wrap: wrap;
        }

        .test-btn {
            background: linear-gradient(135deg, #3b82f6, #10b981);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
            color: white;
            text-decoration: none;
        }

        @media (max-width: 768px) {
            .test-container {
                padding: 1rem;
                margin: 10px;
            }

            .device-simulator {
                grid-template-columns: 1fr;
            }

            .mobile-frame, .tablet-frame {
                width: 100%;
                max-width: 300px;
            }

            .test-buttons {
                flex-direction: column;
                align-items: center;
            }

            .test-btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-mobile-alt"></i> Mobile Responsiveness Test</h1>
            <p>Testing the premium classteacher dashboard across different devices</p>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <h3>Mobile Optimized</h3>
                <p>Responsive design that works perfectly on phones with touch-friendly navigation</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-tablet-alt"></i>
                </div>
                <h3>Tablet Ready</h3>
                <p>Adaptive layouts that make the most of tablet screen real estate</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-desktop"></i>
                </div>
                <h3>Desktop Enhanced</h3>
                <p>Full-featured experience with glassmorphism effects and premium styling</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-bars"></i>
                </div>
                <h3>Mobile Menu</h3>
                <p>Hamburger menu with smooth animations and easy access to all features</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-sync-alt"></i>
                </div>
                <h3>Orientation Support</h3>
                <p>Handles device rotation and orientation changes seamlessly</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-tachometer-alt"></i>
                </div>
                <h3>Performance Optimized</h3>
                <p>Fast loading with optimized animations and efficient resource usage</p>
            </div>
        </div>

        <div class="test-buttons">
            <a href="{{ url_for('classteacher.dashboard') }}" class="test-btn">
                <i class="fas fa-eye"></i>
                View Premium Dashboard
            </a>
            <a href="{{ url_for('classteacher.legacy_dashboard') }}" class="test-btn">
                <i class="fas fa-list"></i>
                View Legacy Dashboard
            </a>
            <button class="test-btn" onclick="testMobileFeatures()">
                <i class="fas fa-play"></i>
                Test Mobile Features
            </button>
        </div>
    </div>

    <script>
        function testMobileFeatures() {
            alert('Mobile features test:\n\n' +
                  '✅ Responsive grid layouts\n' +
                  '✅ Touch-friendly buttons\n' +
                  '✅ Mobile navigation menu\n' +
                  '✅ Optimized typography\n' +
                  '✅ Smooth animations\n' +
                  '✅ Orientation handling\n\n' +
                  'Try resizing your browser or using developer tools to test different screen sizes!');
        }

        // Log screen information
        console.log('📱 Screen Info:', {
            width: window.innerWidth,
            height: window.innerHeight,
            devicePixelRatio: window.devicePixelRatio,
            userAgent: navigator.userAgent.substring(0, 50) + '...'
        });
    </script>
</body>
</html>
