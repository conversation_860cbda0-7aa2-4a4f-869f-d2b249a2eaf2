/**
 * Hillview School Management System - Theme Manager
 * Handles light/dark mode switching with persistence
 */

class ThemeManager {
  constructor() {
    this.currentTheme = this.getStoredTheme() || this.getSystemTheme();
    this.init();
  }

  /**
   * Initialize theme manager
   */
  init() {
    this.applyTheme(this.currentTheme);
    this.setupEventListeners();
    this.updateToggleButtons();

    // Listen for system theme changes
    if (window.matchMedia) {
      window
        .matchMedia("(prefers-color-scheme: dark)")
        .addEventListener("change", (e) => {
          if (!this.getStoredTheme()) {
            this.setTheme(e.matches ? "dark" : "light");
          }
        });
    }
  }

  /**
   * Get stored theme from localStorage
   */
  getStoredTheme() {
    try {
      return localStorage.getItem("hillview-theme");
    } catch (e) {
      console.warn(
        "localStorage not available, theme preference will not persist"
      );
      return null;
    }
  }

  /**
   * Get system theme preference
   */
  getSystemTheme() {
    if (
      window.matchMedia &&
      window.matchMedia("(prefers-color-scheme: dark)").matches
    ) {
      return "dark";
    }
    return "light";
  }

  /**
   * Store theme preference
   */
  storeTheme(theme) {
    try {
      localStorage.setItem("hillview-theme", theme);
    } catch (e) {
      console.warn("Could not store theme preference");
    }
  }

  /**
   * Apply theme to document
   */
  applyTheme(theme) {
    // Add theme switching class to prevent transition flashing
    document.body.classList.add("theme-switching");

    // Set theme attribute on document element
    document.documentElement.setAttribute("data-theme", theme);

    // Update meta theme-color for mobile browsers
    this.updateMetaThemeColor(theme);

    // Remove theme switching class after a short delay
    setTimeout(() => {
      document.body.classList.remove("theme-switching");
    }, 100);

    this.currentTheme = theme;
  }

  /**
   * Update meta theme-color for mobile browsers
   */
  updateMetaThemeColor(theme) {
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      const color = theme === "dark" ? "#111827" : "#667eea";
      metaThemeColor.setAttribute("content", color);
    }
  }

  /**
   * Set theme and persist preference
   */
  setTheme(theme) {
    this.applyTheme(theme);
    this.storeTheme(theme);
    this.updateToggleButtons();

    // Dispatch custom event for other components to listen to
    window.dispatchEvent(
      new CustomEvent("themeChanged", {
        detail: { theme: theme },
      })
    );
  }

  /**
   * Toggle between light and dark themes
   */
  toggleTheme() {
    const newTheme = this.currentTheme === "light" ? "dark" : "light";
    this.setTheme(newTheme);
  }

  /**
   * Setup event listeners for theme toggle buttons
   */
  setupEventListeners() {
    // Handle theme toggle buttons
    document.addEventListener("click", (e) => {
      if (e.target.closest(".theme-toggle")) {
        e.preventDefault();
        this.toggleTheme();
      }
    });

    // Handle keyboard navigation for theme toggle
    document.addEventListener("keydown", (e) => {
      if (
        e.target.closest(".theme-toggle") &&
        (e.key === "Enter" || e.key === " ")
      ) {
        e.preventDefault();
        this.toggleTheme();
      }
    });
  }

  /**
   * Update all theme toggle buttons on the page
   */
  updateToggleButtons() {
    const toggleButtons = document.querySelectorAll(".theme-toggle");
    toggleButtons.forEach((button) => {
      button.setAttribute("data-theme", this.currentTheme);
      button.setAttribute(
        "aria-label",
        `Switch to ${this.currentTheme === "light" ? "dark" : "light"} mode`
      );

      // Update icon
      const slider = button.querySelector(".theme-toggle-slider");
      const icon = button.querySelector(".theme-toggle-icon");
      if (slider && icon) {
        icon.className = `theme-toggle-icon fas ${
          this.currentTheme === "light" ? "fa-sun" : "fa-moon"
        }`;
      }
    });
  }

  /**
   * Create theme toggle button HTML
   */
  static createToggleButton(className = "") {
    return `
      <button class="theme-toggle ${className}" 
              type="button" 
              role="switch" 
              aria-label="Toggle theme"
              title="Toggle light/dark mode">
        <div class="theme-toggle-slider">
          <i class="theme-toggle-icon fas fa-sun"></i>
        </div>
      </button>
    `;
  }

  /**
   * Get current theme
   */
  getCurrentTheme() {
    return this.currentTheme;
  }

  /**
   * Check if dark mode is active
   */
  isDarkMode() {
    return this.currentTheme === "dark";
  }

  /**
   * Force refresh theme (useful after dynamic content loading)
   */
  refreshTheme() {
    this.applyTheme(this.currentTheme);
    this.updateToggleButtons();
  }
}

// Initialize theme manager when DOM is ready
document.addEventListener("DOMContentLoaded", () => {
  window.themeManager = new ThemeManager();

  // Add theme toggle buttons to any existing containers
  const themeToggleContainers = document.querySelectorAll(
    ".theme-toggle-placeholder"
  );
  themeToggleContainers.forEach((container) => {
    container.innerHTML = ThemeManager.createToggleButton();
  });

  // Update all toggle buttons after initialization
  window.themeManager.updateToggleButtons();
});

// Listen for page visibility changes to sync theme across tabs
document.addEventListener("visibilitychange", () => {
  if (!document.hidden && window.themeManager) {
    // Check if theme was changed in another tab
    const storedTheme = window.themeManager.getStoredTheme();
    if (storedTheme && storedTheme !== window.themeManager.getCurrentTheme()) {
      window.themeManager.setTheme(storedTheme);
    }
  }
});

// Export for module usage
if (typeof module !== "undefined" && module.exports) {
  module.exports = ThemeManager;
}
